{"pnpm": {"overrides": {"node-fetch": "npm:node-fetch-native@latest"}}, "name": "living-room", "version": "1.0.0", "scripts": {"depcheck": "node depcheck.js", "dev": "NODE_OPTIONS='--max-old-space-size=8192' NUXT_ENV=development nuxt dev", "devbuild": "NUXT_ENV=development nuxt build --webpack-config ./webpack.config.js", "development": "NUXT_ENV=development nuxt start", "stagingbuild": "NUXT_ENV=staging nuxt build --webpack-config ./webpack.config.js", "stagingAnalyz": "NUXT_ENV=staging nuxt build --webpack-config ./webpack.config.js --analyz", "staging": "NUXT_ENV=staging nuxt start", "build": "NUXT_ENV=production nuxt build --webpack-config ./webpack.config.js", "start": "NUXT_ENV=production nuxt start", "generate": "nuxt generate", "lint:js": "eslint --ext .js,.vue --ignore-path .gitignore .", "lint": "node lint:js"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "dependencies": {"@babel/helper-define-map": "^7.18.6", "@babel/helper-remap-async-to-generator": "^7.18.9", "@chenfengyuan/vue-qrcode": "^1.0.2", "@line/liff": "^2.24.0", "@lottiefiles/vue-lottie-player": "^1.1.0", "@nuxt/content": "^1.10.0", "@nuxtjs/axios": "^5.12.2", "@nuxtjs/device": "2.1.0", "@nuxtjs/dotenv": "^1.4.1", "@nuxtjs/gtm": "^2.4.0", "@nuxtjs/robots": "^2.4.2", "@nuxtjs/sitemap": "^2.4.0", "@nuxtjs/style-resources": "^1.0.0", "axios": "0.27.2", "browser-image-compression": "^1.0.17", "browserslist": "^4.21.5", "camelcase-keys": "^6.2.2", "caniuse-lite": "^1.0.30001517", "consola": "^2.15.3", "cookie": "^0.5.0", "cookie-universal": "^2.2.2", "cookie-universal-nuxt": "^2.1.4", "core-js": "^3.30.0", "countries-and-timezones": "^3.4.1", "countries-list": "^2.5.6", "crypto-js": "^4.2.0", "decimal.js": "^10.4.3", "devalue": "2.0.1", "device-uuid": "^1.0.4", "dompurify": "^3.1.6", "eslint-plugin-prettier": "^3.3.1", "exif-js": "^2.3.0", "form-data": "^4.0.0", "grunt": "^0.4.0", "html2canvas": "1.0.0-rc.0", "html2img": "^0.0.1", "js-cookie": "^3.0.5", "js-crc32": "1.0.1", "jsonwebtoken": "^8.5.1", "levenary": "^1.1.1", "libphonenumber-js": "^1.11.16", "lodash": "^4.17.21", "lottie-web-vue": "1.2.1", "mark.js": "^8.11.1", "moment-timezone": "^0.5.43", "node-fetch-native": "^1.2.0", "nosleep.js": "^0.12.0", "nuxt": "^2.16.3", "nuxt-compress": "^5.0.0", "nuxt-facebook-pixel-module": "^1.6.0", "nuxt-i18n": "^6.28.1", "nuxt-user-agent": "^1.2.2", "page-lifecycle": "^0.1.2", "property-information": "5.6.0", "qs": "^6.11.2", "quill": "^1.3.7", "screenfull": "5", "sharp": "^0.32.0", "snakecase-keys": "^3.2.0", "swiper": "^5.2.0", "uuid-by-string": "^3.0.4", "vee-validate": "2.2.15", "vue": "^2.7.14", "vue-advanced-cropper": "^1.11.7", "vue-awesome-swiper": "^4.1.1", "vue-client-only": "2.1.0", "vue-clipboard2": "^0.3.1", "vue-count-to": "^1.0.13", "vue-cropper": "^0.6.2", "vue-gtag": "1.16.1", "vue-i18n": "8.28.2", "vue-meta": "2.4.0", "vue-no-ssr": "1.1.1", "vue-notification": "^1.3.20", "vue-observe-visibility": "^1.0.0", "vue-router": "3.6.5", "vue-server-renderer": "2.7.14", "vue-video-player": "^5.0.2", "vuex": "^3.6.2", "woothee": "1.11.1"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.21.0", "@babel/eslint-parser": "^7.19.1", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.20.2", "@babel/runtime-corejs3": "^7.21.0", "@nuxt/babel-preset-app": "2.16.3", "@nuxt/image": "^0.7.1", "@nuxtjs/eslint-config": "^6.0.1", "@nuxtjs/eslint-module": "^3.1.0", "@nuxtjs/proxy": "^2.0.1", "@nuxtjs/pwa": "^3.3.5", "@nuxtjs/svg": "^0.4.0", "@nuxtjs/vuetify": "^1.12.3", "@vue/compiler-dom": "^3.2.40", "@vue/eslint-config-prettier": "^7.0.0", "babel-plugin-dynamic-import-node": "^2.3.3", "depcheck": "^1.4.0", "depcheck-special-nuxt": "^1.2.2", "esbuild-loader": "^3.0.1", "eslint": "^7.8.1", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.21.0", "eslint-plugin-node": ">=7.2.0", "eslint-plugin-nuxt": "3.1.0", "eslint-plugin-promise": ">=4.0.1", "eslint-plugin-standard": ">=4.0.0", "eslint-plugin-unicorn": "38.0.0", "eslint-webpack-plugin": "2.x", "hard-source-webpack-plugin": "^0.13.1", "jest": "^24.1.0", "prettier": "^2.5.1", "sass": "^1.49.9", "sass-loader": "^10.1.1", "terser-webpack-plugin": "4.2.3", "typescript": "^5.0.2", "vue-eslint-parser": "^7.11.0", "vue-template-compiler": "^2.7.14", "webpack": "4.46.0", "worker-loader": "^3.0.8"}}
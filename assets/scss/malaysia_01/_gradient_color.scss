$pv1-color: map-get($colors, primary-variant-1);
$pv2-color: map-get($colors, primary-variant-2);
$pv3-color: map-get($colors, primary-variant-3);
$grey-1-color: map-get($colors, grey-1);
$grey-2-color: map-get($colors, grey-2);
$grey-3-color: map-get($colors, grey-3);
$grey-4-color: map-get($colors, grey-4);
$grey-5-color: map-get($colors, grey-5);
$primary-color: map-get($colors, primary);
$bg-card: map-get($colors, bg-card);

.gradient-primary {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
}

.gradient-primary-reverse {
  background: linear-gradient(180deg, #ffed30 0%, #fff6aa 100%);
}

.gradient-primary-left {
  background: linear-gradient(270deg, #fff6aa 0%, #ffc700 100%);
}

.gradient-light-primary {
  background: linear-gradient(180deg, rgba(102, 191, 255, 0.8) 0%, rgba(65, 118, 250, 1) 100%);
}

.gradient-white-card {
  background: linear-gradient(180deg, $white-color 29.17%, $grey-4-color 92.71%, $grey-5-color 100%);
}

.gradient-button {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
}

.gradient-game-maintenance {
  background: linear-gradient(180deg, rgba(144, 145, 153, 0.8) 0%, rgba(22, 24, 33, 1) 100%);
}

.gradient-primary-box {
  background: linear-gradient(270.47deg, #7b0bec -0.45%, #0d1497 99.59%);
}

.gradient-title--text {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}
.gradient-subtitle--text {
  background: linear-gradient(180deg, $grey-2-color 61.46%, $grey-4-color 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
}

.gradient-primary--text {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  border-radius: 4px;
}

.gradient-primary-border {
  border: 3px solid;
  border-image: linear-gradient(180deg, rgba(255, 231, 189, 1), rgba(247, 182, 117, 1)) 1;
}

.app-bar-gradient {
  background: linear-gradient(180deg, #290a92 0%, #290a92 100%);
}
.bottom-bar-gradient {
  background: linear-gradient(180deg, #290a92 0%, #290a92 100%);
}

.app-bar-button {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.border-line {
  width: 100%;
  height: 100%;
  border: solid 0.14159rem transparent;
  background-image: linear-gradient(0deg, #440106, #440106),
    linear-gradient(rgba(233, 185, 79, 1) 0%, rgba(255, 255, 255, 1) 50%, rgba(233, 185, 79, 1) 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
}

.card-gradient-1 {
  background: linear-gradient(180deg, #ac2335 29.17%, #550d10 92.71%, #a0574c 100%);
}

.card-gradient-2 {
  background: linear-gradient(180deg, #240d77 0%, #13143c 100%);
}

.background {
  background: linear-gradient(180deg, #290a92 0%, #270a62 51.04%, #8f2094 100%);
}

.info-card {
  background: linear-gradient(180deg, #202fad 0%, #530782 100%);
}

.vip-title {
  background: linear-gradient(180deg, #fec600 0%, #dc1646 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-game-maintenance-2 {
  background: linear-gradient(180deg, rgba(153, 144, 144, 0) 0%, #161821 100%);
}

.gradient-side-bar-game {
  background: linear-gradient(99deg, #3f4dc6 -2.56%, #5c40cb 37.71%, #762bc1 109.3%);
}

.game-intro-grade-bg {
  background: linear-gradient(180deg, #4530c3 0%, rgba(115, 81, 197, 0.4) 50%, rgba(114, 67, 136, 0) 100%) !important;
}

.game-intro-grade-border {
  background: linear-gradient(270deg, #37296d 0%, #443892 28%, rgba(73, 56, 143, 0.2) 76%, rgba(28, 26, 121, 0.2) 100%);
  &::before {
    border: 2px solid;
    border-image: linear-gradient(
        to right bottom,
        #dad6e4 -0.44%,
        #574e8b 49.78%,
        rgba(90, 78, 116, 0.2) 74.89%,
        #c4bdd8 87.45%,
        rgba(90, 78, 116, 0.2) 100%
      )
      1;
  }
}

.mail-attachment-gradient-bg {
  background: linear-gradient(180deg, #6573f2 0%, rgba(101, 115, 242, 0.4) 50%, rgba(101, 115, 242, 0) 100%);
}

.gift-pack-reward-card-fill {
  background: linear-gradient(
    270deg,
    rgba(241, 142, 28, 0) 0%,
    rgba(241, 142, 28, 0.5) 12.86%,
    rgb(241, 142, 28) 50%,
    rgba(241, 142, 28, 0.5) 85%,
    rgba(241, 142, 28, 0) 100%
  );
}

.gift-pack-frame {
  background: linear-gradient(
    270deg,
    rgba(173, 196, 255, 0) 0%,
    rgba(173, 196, 255, 0.8) 51%,
    rgba(173, 196, 255, 0) 100%
  );
}
.gift-pack-frame-vip {
  background: linear-gradient(270deg, rgba(130, 134, 211, 0) 0%, rgba(130, 134, 211, 0.8) 51%, rgba(130, 134, 211, 0) 100%);
}

//replaceColor start
.bg-dialog-header {
  background: linear-gradient(270deg, #fff6aa 0%, #ffc700 100%);
}
.bg-marquee {
  background: linear-gradient(90deg, #2c4ae5 0%, #502fab 50%, #7e4bec 100%);
}
.title-heavy--text {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  border-radius: 4px;
}
.overlay-game-square {
  background: linear-gradient(180deg, rgba(144, 145, 153, 0.5) 0%, #161821 100%);
}
.overlay-game-promote {
  background: linear-gradient(180deg, rgba(144, 145, 153, 0) 0%, #161821 100%);
}
.bg-btn-heavy {
  background: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%);
}
.bg-game-square-chip {
  background: linear-gradient(180deg, #ffed30 0%, #fff6aa 100%);
}
.border-medium {
  border: 3px solid;
  border-image: linear-gradient(180deg, #fff6aa 0%, #ffed30 100%) 1;
}
.border-title-right {
  border-image-source: linear-gradient(270deg, #ffed30 0%, #ffed3000 100%) !important;
  -webkit-border-image-source: linear-gradient(270deg, #ffed30 0%, #ffed3000 100%) !important;
  -moz-border-image-source: linear-gradient(270deg, #ffed30 0%, #ffed3000 100%) !important;
  -ms-border-image-source: linear-gradient(270deg, #ffed30 0%, #ffed3000 100%) !important;
  border-image-slice: 1;
  -webkit-border-image-slice: 1;
  -moz-border-image-slice: 1;
  -ms-border-image-slice: 1;
  transform: rotate(180deg);
}
.border-title-left {
  border-image-source: linear-gradient(270deg, #ffed3000 0%, #ffed30 100%) !important;
  -webkit-border-image-source: linear-gradient(270deg, #ffed3000 0%, #ffed30 100%) !important;
  -moz-border-image-source: linear-gradient(270deg, #ffed3000 0%, #ffed30 100%) !important;
  -ms-border-image-source: linear-gradient(270deg, #ffed3000 0%, #ffed30 100%) !important;
  border-image-slice: 1;
  -webkit-border-image-slice: 1;
  -moz-border-image-slice: 1;
  -ms-border-image-slice: 1;
  transform: rotate(180deg);
}
.bg-card-border {
  width: 100%;
  height: 100%;
  border: solid 0.14159rem transparent;
  background-image: linear-gradient(0deg, $bg-card, $bg-card), linear-gradient(#ffe138 0%, #ffffff 50%, #ffe138 100%) !important;
  background-origin: border-box;
  background-clip: content-box, border-box;
}

.bg-gift-pack-vip-section {
  background: linear-gradient(
    270deg,
    rgba(241, 142, 28, 0) 0%,
    rgba(241, 142, 28, 0.5) 12.86%,
    #f18e1c 50%,
    rgba(241, 142, 28, 0.5) 85%,
    rgba(241, 142, 28, 0) 100%
  );
}
//replaceColor end


.player-ranking-hint-bg-fill{
  background:  linear-gradient(90deg, rgba(26, 11, 11, 0.00) 0%, rgba(26, 11, 11, 0.42) 50%);
  }

  .player-ranking-bn-bg-fill-1{
    background:  linear-gradient(98deg, #7E1635 51.8%, rgba(126, 22, 53, 0.00) 86.67%);
  }

  .player-ranking-button-fill{
    background:  linear-gradient(90deg, rgba(168, 50, 84, 0.00) 0%, rgba(168, 50, 84, 0.30) 25%, rgba(168, 50, 84, 0.80) 50%, rgba(168, 50, 84, 0.30) 75%, rgba(168, 50, 84, 0.00) 100%);
  }

  .player-ranking-winners-fill--text{
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 30%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0.2) 70%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .player-ranking-top-three-bg-fill-1{
    background: linear-gradient(90deg, #6D422D -0.01%, #A57259 49.76%, #A57259 69.82%, #6D422D 99.99%);
  }
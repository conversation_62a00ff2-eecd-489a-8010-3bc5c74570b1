$colors: (
  primary: #ffc700,
  secondary: #6d27b9,
  warning: #ff7a00,
  error: #ff005c,
  white: #fff,
  black: #000,
  success: #00cf6c,
  info: #0065fc,
  primary-variant-1: #ff9900,
  primary-variant-2: #ffaf37,
  primary-variant-3: #fff576,
  secondary-variant-1: #440106,
  grey-1: #e6e1e6,
  grey-2: #b2aab1,
  grey-3: #948695,
  grey-4: #47354c,
  grey-5: #341c47,
  grey-6: #2b1439,
  default-content: #fff,
  default-content-1: #ffffff,
  default-content-2: #8e7194,
  button-content: #000,
  dialog-fill: #3b2050,
  dialog-fill-2: #2c183a,
  divider: #ffffff66,
  footer-fill: #340f64,
  iframe-bar: #0d051a,
  footer-item: #4d4545,
  app-bar-item: #46184b,
  button-icon: #292222,
  game-hover: #4b454dcc,
  card-gradient-2: #440106,
  card-fill: #553073,
  card-fill-2: #36174e,
  pagination-fill: #693676,
  scroll-fill: #1e0b37,
  scroll: #422c47,
  letter-info: #71a1ff,
  footer-button: #b2aab1,
  btn-disable: #ffffff4d,
  offline: #b2aab1,
  private: #ffffff,
  name-private: #36bf36,
  vip-g-1: #fe7a00,
  vip-g-2: #ac2335,
  black-with-opacity-20: #00000033,
  black-with-opacity-50: rgba(0, 0, 0, 0.5),
  // replaceColor
  title-soft: #ffffff,
  title-character-card: #ffc700,
  text-character-card: #ffffff,
  text-soft: #b2aab0,
  text-regular: #ffffff,
  text-medium: #ffc700,
  text-heavy: #ff9900,
  text-list: #ffffff,
  text-list-focused: #ffc700,
  text-dialog-header: #e6e1e6,
  text-dialog-header-inverse: #000000,
  text-btn: #000000,
  text-btn-heavy: #000000,
  text-marquee-medium: #ffc700,
  text-marquee: #ffffff,
  text-app-awards: #f7b675,
  text-app-awards-soft: #fbfbfb,
  text-app-awards-medium: #e9b950,
  text-game-square-rtp: #ffffff,
  text-game-promote-rtp: #ffffff,
  text-game-square-chip: #321b42,
  text-pwa: #1a0b0b,
  text-cookie: #ffffff,
  text-cookie-link: #ffc700,
  text-field-focused: #ffc700,
  text-field-error: #ff005c,
  text-gift-pack: #ffffff,
  text-gift-pack-medium: #e9b950,
  text-gift-pack-btn: #ffffff,
  bg-text-field: #3b2050,
  bg-dialog: #3b2050,
  bg-dialog-medium: #2b1439,
  bg-btn: #ffc700,
  bg-switch: #ffc700,
  bg-menu: #36174e,
  bg-app-awards-title-dot: #e9b950,
  bg-game-square-rtp-up: #ff005c,
  bg-game-square-rtp-down: #00cf6c,
  bg-card: #431b57,
  bg-table-medium: #47354c,
  bg-table-hover: #3b2050,
  bg-table: #2b1439,
  tab-focused: #ffc700,
  btn-regular: #ffc700,
  btn-soft: #ffffff,
  btn-dialog-inverse: #000000,
  btn-chat: #ffc700,
  border-regular: #ffaf37,
  decorate-border-gift-pack: #ffaf37,
  border-marquee: rgba(255, 255, 255, 0.5),
  bg-scrollbar: #1e0b37,
  scrollbar-regular: #422c47,
  btn-iframe-bar: #ffffff,
  bg-iframe-bar: #0d051a,
  bg-slider-track: #ffc700,
  bg-slider-thumb: #ffc700,
  text-slider: #ffffff,
  text-slider-hover: #ffc700,
  bg-pagination: #693676,
  bg-pagination-focus: #ffc700,
  text-pagination: #ffffff,
  text-pagination-focused: #000000//replaceColor end
) !default;

$title-soft: map-get($colors, title-soft);
$title-character-card: map-get($colors, title-character-card);
$text-character-card: map-get($colors, text-character-card);
$text-soft: map-get($colors, text-soft);
$text-regular: map-get($colors, text-regular);
$text-medium: map-get($colors, text-medium);
$text-heavy: map-get($colors, text-heavy);
$text-list: map-get($colors, text-list);
$text-list-focused: map-get($colors, text-list-focused);
$text-dialog-header: map-get($colors, text-dialog-header);
$text-dialog-header-inverse: map-get($colors, text-dialog-header-inverse);
$text-btn: map-get($colors, text-btn);
$text-btn-heavy: map-get($colors, text-btn-heavy);
$text-marquee-medium: map-get($colors, text-marquee-medium);
$text-marquee: map-get($colors, text-marquee);
$text-app-awards: map-get($colors, text-app-awards);
$text-app-awards-soft: map-get($colors, text-app-awards-soft);
$text-app-awards-medium: map-get($colors, text-app-awards-medium);
$text-game-square-rtp: map-get($colors, text-game-square-rtp);
$text-game-promote-rtp: map-get($colors, text-game-promote-rtp);
$text-game-square-chip: map-get($colors, text-game-square-chip);
$text-pwa: map-get($colors, text-pwa);
$text-cookie: map-get($colors, text-cookie);
$text-cookie-link: map-get($colors, text-cookie-link);
$text-field-focused: map-get($colors, text-field-focused);
$text-field-error: map-get($colors, text-field-error);
$text-gift-pack: map-get($colors, text-gift-pack);
$text-gift-pack-medium: map-get($colors, text-gift-pack-medium);
$text-gift-pack-btn: map-get($colors, text-gift-pack-btn);
$bg-text-field: map-get($colors, bg-text-field);
$bg-dialog: map-get($colors, bg-dialog);
$bg-dialog-medium: map-get($colors, bg-dialog-medium);
$bg-btn: map-get($colors, bg-btn);
$bg-switch: map-get($colors, bg-switch);
$bg-menu: map-get($colors, bg-menu);
$bg-app-awards-title-dot: map-get($colors, bg-app-awards-title-dot);
$bg-game-square-rtp-up: map-get($colors, bg-game-square-rtp-up);
$bg-game-square-rtp-down: map-get($colors, bg-game-square-rtp-down);
$bg-card: map-get($colors, bg-card);
$bg-table-medium: map-get($colors, bg-table-medium);
$bg-table-hover: map-get($colors, bg-table-hover);
$bg-table: map-get($colors, bg-table);
$tab-focused: map-get($colors, tab-focused);
$btn-regular: map-get($colors, btn-regular);
$btn-soft: map-get($colors, btn-soft);
$btn-dialog-inverse: map-get($colors, btn-dialog-inverse);
$btn-chat: map-get($colors, btn-chat);
$border-regular: map-get($colors, border-regular);
$decorate-border-gift-pack: map-get($colors, decorate-border-gift-pack);
$border-marquee: map-get($colors, border-marquee);
$bg-scrollbar: map-get($colors, bg-scrollbar);
$scrollbar-regular: map-get($colors, scrollbar-regular);
$btn-iframe-bar: map-get($colors, btn-iframe-bar);
$bg-iframe-bar: map-get($colors, bg-iframe-bar);
$bg-slider-track: map-get($colors, bg-slider-track);
$bg-slider-thumb: map-get($colors, bg-slider-thumb);
$text-slider: map-get($colors, text-slider);
$text-slider-hover: map-get($colors, text-slider-hover);
$bg-pagination: map-get($colors, bg-pagination);
$bg-pagination-focus: map-get($colors, bg-pagination-focus);
$text-pagination: map-get($colors, text-pagination);
$text-pagination-focused: map-get($colors, text-pagination-focused);

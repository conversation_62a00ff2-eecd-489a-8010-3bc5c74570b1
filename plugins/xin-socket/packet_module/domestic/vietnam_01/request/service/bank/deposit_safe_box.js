'use strict'

import RequestBase from '../../../../request/base'
const STATION = process.env.STATION
const Config = require(`@/plugins/xin-socket/protocolConfig/${STATION}/protocol.config`).default

class DepositSafeBox extends RequestBase {
  constructor() {
    super(Config.PROTOCOL_ID.SERVICE)
    this.serviceId = Config.BANK_SERVICE.ID
    this.serial = 0
    this.type = Config.BANK_SERVICE.TYPE.DEPOSIT_SAFE_BOX.ID
    /** 存入額度 */
    this.amount = 0
  }

  get size() {
    return 12
  }

  packing(writer) {
    writer.addUint16(this.serviceId) // 服務編號
    writer.addUint16(this.serial) // 流水號
    writer.addUint8(this.type)
    writer.addLongToBytes5(this.amount)
    writer.addString16('㍿') // 無效參數，需必帶
  }
}

export { DepositSafeBox as default }

'use strict'

import RequestBase from '../base'
const STATION = process.env.STATION
const Config = require(`@/plugins/xin-socket/protocolConfig/${STATION}/protocol.config`).default

class VerifyOTP extends RequestBase {
  constructor() {
    super(Config.PROTOCOL_ID.SERVICE)
    this.serviceId = Config.LOGIN_SERVICE.ID
    this.serial = 2

    this.type = Config.LOGIN_SERVICE.TYPE.SMS.ID // 獲取安全碼
    this.command = Config.LOGIN_SERVICE.TYPE.SMS.COMMAND.VERIFY_SAFETY_CODE // 取得安全碼
    this.smsType = Config.OTP.TYPE.LOGIN // 驗證的功能  0 = 門號登入 1 = 設定寶物密碼 11 = 裝置認證
    this.otp = ''
  }

  get size() {
    let result = 7
    result = result + 2 + this.otp.length * 2
    return result
  }

  packing(writer) {
    writer.addInt16(this.serviceId)
    writer.addInt16(this.serial)
    writer.addInt8(this.type)
    writer.addInt8(this.command)
    writer.addInt8(this.smsType)
    writer.addString16WithLength(this.otp)
  }
}

export default VerifyOTP

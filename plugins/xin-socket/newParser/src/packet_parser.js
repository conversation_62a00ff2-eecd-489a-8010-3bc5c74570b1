const Reader = require('./reader')
const md5 = require('./md5')

// 預緩存常用正則表達式以提高效能
const REGEX = {
  TYPE: /type=(\w+)/,
  COUNT: /count=(\d+|\{[^}]+\})/,
  LENGTH: /length=([^,]+)/,
  WHILE: /while=([^,]+)/,
  TRANSFORM: /transform=([^,]+)/,
  SOURCE_FIELD: /source=([a-zA-Z0-9_]+)/,
  PEEK: /peek\((.*?)\)/,
  FILTER_USERS: /_users\.filter\(function\(([^)]+)\)\{return ([^}]+)\}\)/,
  MD5: /prefix=(.*?)(?:&|,|\s|$)/
}

class PacketParser {
  constructor(packetDefinition, externalData = null) {
    this.definition = packetDefinition
    this.externalData = externalData
    this.isFeature = (featureId) => (this.definition.identify?.isFeature || []).includes(featureId)
    this.skipTypeZero = this.definition.identify?.skipTypeZero ?? true

    this.result = Object.create(null)
    this.internalFields = Object.create(null)
    this.contextCache = Object.create(null)

    this._cachedConditions = new Map()
    this._cachedExpressions = new Map()

    this.processedFields = this.preprocessFields(this.definition.fields)
  }

  preprocessFields(fields) {
    if (!fields || !Array.isArray(fields)) return []

    return fields.map((field) => {
      // Handle both array and object formats
      if (Array.isArray(field)) {
        const fieldObj = Object.create(null)
        fieldObj.name = field[0]
        fieldObj.type = field[1]
        fieldObj.options = field[2] || ''
        fieldObj.subFields = field[3]

        // Add any additional options from field[5]
        if (field[5]) {
          Object.assign(fieldObj, field[5])
        }

        if (fieldObj.options) {
          this._preprocessOptions(fieldObj)
        }

        if (fieldObj.type === 'array' && Array.isArray(fieldObj.subFields)) {
          fieldObj.subFields = this.preprocessFields(fieldObj.subFields)
        }

        return fieldObj
      } else {
        // Object format is already in the desired structure
        const fieldObj = Object.assign(Object.create(null), field)

        if (fieldObj.options) {
          this._preprocessOptions(fieldObj)
        }

        if (fieldObj.type === 'array' && Array.isArray(fieldObj.subFields)) {
          fieldObj.subFields = this.preprocessFields(fieldObj.subFields)
        }

        return fieldObj
      }
    })
  }

  // Function to access field properties regardless of format
  _getFieldProperty(field, propName) {
    if (Array.isArray(field)) {
      // Array format mapping
      const propMap = {
        name: 0,
        type: 1,
        options: 2,
        subFields: 3
      }

      if (propName in propMap) {
        return field[propMap[propName]]
      } else if (field[5] && propName in field[5]) {
        return field[5][propName]
      }
      return undefined
    } else {
      // Object format
      return field[propName]
    }
  }

  _preprocessOptions(fieldObj) {
    const options = fieldObj.options || ''

    if (options.includes('count=')) {
      const match = options.match(REGEX.COUNT)
      if (match) {
        fieldObj._countStr = match[1]
        fieldObj._isDynamicCount =
          fieldObj._countStr.startsWith('{') && fieldObj._countStr.endsWith('}')
        if (!fieldObj._isDynamicCount) {
          fieldObj._count = parseInt(fieldObj._countStr)
        }
      }
    }

    if (options.includes('length=')) {
      const match = options.match(REGEX.LENGTH)
      if (match) {
        fieldObj._lengthStr = match[1]
        fieldObj._isDynamicLength =
          fieldObj._lengthStr.startsWith('{') && fieldObj._lengthStr.endsWith('}')
        if (!fieldObj._isDynamicLength) {
          fieldObj._length = parseInt(fieldObj._lengthStr)
        }
      }
    }

    if (options.includes('type=')) {
      const match = options.match(REGEX.TYPE)
      if (match) {
        fieldObj._elementType = match[1]
      }
    }

    fieldObj._hasTransform = options.includes('transform=')
    fieldObj._littleEndian = options.includes('littleEndian')
  }

  parse(buffer) {
    const reader = new Reader(buffer)

    this._fastClearObject(this.result)
    this._fastClearObject(this.internalFields)
    this._fastClearObject(this.contextCache)

    this._cachedConditions.clear()
    this._cachedExpressions.clear()

    if (this.externalData) {
      this.internalFields.externalData = this.externalData
    }

    this.internalFields.reader = reader

    this.internalFields.isEOF = () => reader.isEOF()
    this.internalFields.EOFCount = () => reader.EOFCount()
    this.internalFields.remaining = () => reader.remaining()
    this.internalFields.peek = (n) => reader.peek(n)
    this.internalFields.peekUint8 = () => reader.peekUint8()

    this.parseFields(reader, this.processedFields)

    if (this.definition.postProcess) {
      this.applyPostProcess(this.result, this.definition.postProcess, reader)
    }

    if (this.result.serviceType) {
      let typeArray = this.result.serviceType
      if (!Array.isArray(typeArray)) {
        typeArray = [typeArray]
      }

      const isFirstZero = this.skipTypeZero ? typeArray[0] === 0 : false

      this.result.type = typeArray[isFirstZero ? 1 : 0]
      if (typeArray[isFirstZero ? 2 : 1]) this.result.commandId = typeArray[isFirstZero ? 2 : 1]
      if (typeArray[isFirstZero ? 3 : 2]) this.result.functionId = typeArray[isFirstZero ? 3 : 2]
    }

    this.result.isFeature = this.isFeature
    return this.result
  }

  _fastClearObject(obj) {
    for (const key in obj) {
      delete obj[key]
    }
  }

  getContext() {
    for (const key in this.result) {
      this.contextCache[key] = this.result[key]
    }
    for (const key in this.internalFields) {
      this.contextCache[key] = this.internalFields[key]
    }
    return this.contextCache
  }

  parseFields(reader, fields) {
    const resultObj = this.result
    const internalFieldsObj = this.internalFields

    for (let i = 0; i < fields.length; i++) {
      const field = fields[i]

      if (field.condition || (field.extraOptions && field.extraOptions.condition)) {
        const conditionKey = field.condition || (field.extraOptions && field.extraOptions.condition)
        let condition = this._cachedConditions.get(conditionKey)

        if (condition === undefined) {
          const contextWithReader = this.getContextWithReader(reader)
          condition = this.evaluateCondition(conditionKey, contextWithReader, reader)
          this._cachedConditions.set(conditionKey, condition)
        }

        if (!condition) continue
      }
      // console.log('field', field, internalFieldsObj, resultObj)
      let value
      if (field.type === 'array') {
        value = this.parseArray(reader, field, this.getContextWithReader(reader))
      } else {
        value = this.parseValue(
          reader,
          field.type,
          field.options,
          this.getContextWithReader(reader)
        )
      }
      if (field._hasTransform) {
        const transformStr =
          field._transformStr ||
          (field._transformStr = field.options.match(REGEX.TRANSFORM)?.[1].split(',')[0])

        try {
          value = this.applyTransform(value, transformStr, this.getContextWithReader(reader))
        } catch (e) {
          console.error('Error applying transform:', transformStr, e)
        }
      }

      if (field.name.startsWith('_')) {
        internalFieldsObj[field.name] = value
      } else {
        resultObj[field.name] = value
      }
    }
  }

  parseValue(reader, type, options, context) {
    switch (type) {
      case 'uint8':
        return reader.getUint8()
      case 'int8':
        return reader.getInt8()
      case 'uint16':
        return reader.getUint16(options.includes('littleEndian'))
      case 'int16':
        return reader.getInt16(options.includes('littleEndian'))
      case 'uint24':
        return reader.getUint24(options.includes('littleEndian'))
      case 'uint32':
        return reader.getUint32(options.includes('littleEndian'))
      case 'int32':
        return reader.getInt32(options.includes('littleEndian'))
      case 'float32':
        return reader.getFloat32(options.includes('littleEndian'))
      case 'float64':
        return reader.getFloat64(options.includes('littleEndian'))
      case 'ulong':
      case 'long':
        return reader.getLong(options.includes('littleEndian'))

      case 'bytes': {
        const length = this.parseLength(options, context)
        return reader.getBytes(length)
      }

      case 'skip': {
        const length = this.parseLength(options, context)
        return reader.skip(length)
      }

      case 'string': {
        if (options.includes('withLength16')) return reader.getString16WithLength()
        if (options.includes('withLength')) return reader.getStringWithLength()
        if (options.includes('byteCount')) return reader.getString16WithByteCount()
        if (options.includes('remaining')) return reader.getString16Remaining()

        const length = this.parseLength(options, context)
        return options.includes('utf16') ? reader.getString16(length) : reader.getString(length)
      }

      case 'value_array':
        return this.parseValueArray(reader, options, context)
      case 'predefined_type_array':
        return reader.getPredefinedTypeArray()

      case 'md5': {
        const sourceFieldMatch = options.match(REGEX.SOURCE_FIELD)
        if (!sourceFieldMatch) {
          throw new Error("md5 type requires a source field specified as 'source=fieldName'")
        }

        const sourceField = sourceFieldMatch[1]
        const sourceValue = context[sourceField]
        if (!sourceValue) return null

        const prefix = options.match(REGEX.MD5)?.[1] || ''
        return md5(prefix + sourceValue)
      }
      case 'uint8_color':
        return reader.getUint8Color()

      case 'derived':
        return null

      default:
        throw new Error(`Unknown type: ${type}`)
    }
  }

  parseValueArray(reader, options, context) {
    let result = []
    let elementType = 'uint8'
    let count = 0
    const isLittleEndian = options.includes('littleEndian')

    try {
      if (options.includes('type=')) {
        const typeMatch = options.match(REGEX.TYPE)
        if (typeMatch) {
          elementType = typeMatch[1]
        }
      }

      if (options.includes('count=')) {
        const countStr = options.match(REGEX.COUNT)[1]

        if (countStr.startsWith('{') && countStr.endsWith('}')) {
          const varName = countStr.slice(1, -1)
          count = this.evaluateExpression(varName, context)
        } else {
          count = parseInt(countStr)
        }

        result = new Array(count)

        for (let i = 0; i < count; i++) {
          try {
            result[i] = this.readValueByType(reader, elementType, isLittleEndian)
          } catch (e) {
            console.error(`Error reading element ${i} of type ${elementType}:`, e)
            result[i] = null
          }
        }
      } else if (options.includes('while=')) {
        const whileStr = options.match(REGEX.WHILE)[1]

        let estimatedSize = 16
        result = new Array(estimatedSize)
        let index = 0

        while (!reader.isEOF()) {
          try {
            const value = this.readValueByType(reader, elementType, isLittleEndian)

            if (index >= result.length) {
              const newArray = new Array(result.length * 2)
              for (let i = 0; i < result.length; i++) {
                newArray[i] = result[i]
              }
              result = newArray
            }

            result[index++] = value

            const continueReading = this.evaluateCondition(
              whileStr.replace(/value/g, value.toString()),
              { ...context, lastValue: value, values: result.slice(0, index) },
              reader
            )

            if (!continueReading) break
          } catch (e) {
            console.error(`Error in while loop with type ${elementType}:`, e)
            break
          }
        }

        if (index < result.length) {
          result.length = index
        }
      }
    } catch (e) {
      console.error('Error parsing value array:', e)
    }

    return result
  }

  readValueByType(reader, type, isLittleEndian) {
    try {
      if (type === 'uint8') return reader.getUint8()
      if (type === 'uint16') return reader.getUint16(isLittleEndian)
      if (type === 'uint32') return reader.getUint32(isLittleEndian)

      switch (type) {
        case 'int8':
          return reader.getInt8()
        case 'int16':
          return reader.getInt16(isLittleEndian)
        case 'uint24':
          return reader.getUint24(isLittleEndian)
        case 'int32':
          return reader.getInt32(isLittleEndian)
        case 'float32':
          return reader.getFloat32(isLittleEndian)
        case 'float64':
          return reader.getFloat64(isLittleEndian)
        case 'ulong':
        case 'long':
          return reader.getLong(isLittleEndian)
        case 'withLength16':
          return reader.getString16WithLength()
        case 'withLength':
          return reader.getStringWithLength()
        case 'byteCount':
          return reader.getString16WithByteCount()
        case 'remaining':
          return reader.getString16Remaining()
        case 'uint8_color':
          return reader.getUint8Color()
        default:
          throw new Error(`Unsupported number type: ${type}`)
      }
    } catch (e) {
      console.error(`Error in readValueByType for type ${type}:`, e)
      throw e
    }
  }

  parseArray(reader, field, parentContext) {
    const results = []

    if (field.options.startsWith('while')) {
      return this.parseWhileLoop(reader, field, parentContext)
    }

    const count = this.parseArrayCount(field.options, parentContext)
    for (let i = 0; i < count; i++) {
      if (Array.isArray(field.subFields)) {
        const itemContext = {
          ...parentContext,
          index: i,
          items: results
        }

        const result = {}
        for (const subField of field.subFields) {
          if (subField.condition || (subField.extraOptions && subField.extraOptions.condition)) {
            const conditionKey =
              subField.condition || (subField.extraOptions && subField.extraOptions.condition)
            const condition = this.evaluateCondition(
              conditionKey,
              { ...itemContext, ...result },
              reader
            )
            if (!condition) continue
          }

          let subValue
          if (subField.type === 'array') {
            subValue = this.parseArray(reader, subField, { ...itemContext, ...result })
          } else {
            subValue = this.parseValue(reader, subField.type, subField.options || '', {
              ...itemContext,
              ...result
            })
          }

          if (subField.options && subField.options.includes('transform=')) {
            const transformStr = subField.options.split('transform=')[1].split(',')[0]
            try {
              subValue = this.applyTransform(subValue, transformStr, {
                ...itemContext,
                ...result
              })
            } catch (e) {
              console.error('Error applying transform in array:', transformStr, e)
            }
          }

          result[subField.name] = subValue
        }

        results.push(result)
      } else {
        if (typeof field.subFields === 'string') {
          const parts = field.subFields.split(',')
          const valueType = parts[1]
          const valueOptions = parts[3] || ''

          let value = this.parseValue(reader, valueType, valueOptions, parentContext)
          if (valueOptions && valueOptions.includes('transform=')) {
            const transformStr = valueOptions.split('transform=')[1].split(',')[0]
            try {
              value = this.applyTransform(value, transformStr, {
                ...parentContext,
                index: i,
                items: results
              })
            } catch (e) {
              console.error('Error applying transform in simple array:', transformStr, e)
            }
          }
          results.push(value)
        } else {
          const valueType = field.subFields.type
          const valueOptions = field.subFields.options || ''

          let value = this.parseValue(reader, valueType, valueOptions, parentContext)
          if (valueOptions && valueOptions.includes('transform=')) {
            const transformStr = valueOptions.split('transform=')[1].split(',')[0]
            try {
              value = this.applyTransform(value, transformStr, {
                ...parentContext,
                index: i,
                items: results
              })
            } catch (e) {
              console.error('Error applying transform in simple array:', transformStr, e)
            }
          }
          results.push(value)
        }
      }
    }

    return results
  }

  parseWhileLoop(reader, field, parentContext) {
    const whileCondition = this.extractWhileCondition(field.options)
    const results = []
    const context = { ...parentContext }

    try {
      while (!reader.isEOF()) {
        if (whileCondition.peekField) {
          const peekValue = reader.peekUint8()
          if (
            !this.evaluateWhileCondition(
              whileCondition.condition,
              { value: peekValue, ...context },
              reader
            )
          ) {
            break
          }
        }

        if (Array.isArray(field.subFields)) {
          const itemContext = {
            ...context,
            index: results.length,
            items: results
          }

          const result = {}
          for (const subField of field.subFields) {
            if (subField.condition || (subField.extraOptions && subField.extraOptions.condition)) {
              const conditionKey =
                subField.condition || (subField.extraOptions && subField.extraOptions.condition)
              const condition = this.evaluateCondition(
                conditionKey,
                { ...itemContext, ...result },
                reader
              )
              if (!condition) continue
            }

            let subValue
            if (subField.type === 'array') {
              subValue = this.parseArray(reader, subField, { ...itemContext, ...result })
            } else {
              subValue = this.parseValue(reader, subField.type, subField.options || '', {
                ...itemContext,
                ...result
              })
            }

            if (subField.options && subField.options.includes('transform=')) {
              const transformStr = subField.options.split('transform=')[1].split(',')[0]
              try {
                subValue = this.applyTransform(subValue, transformStr, {
                  ...itemContext,
                  ...result
                })
              } catch (e) {
                console.error('Error applying transform in while loop:', transformStr, e)
              }
            }

            result[subField.name] = subValue
          }

          results.push(result)
        } else {
          let value
          if (typeof field.subFields === 'string') {
            const parts = field.subFields.split(',')
            const valueType = parts[1]
            const valueOptions = parts[3] || ''
            value = this.parseValue(reader, valueType, valueOptions, context)

            if (valueOptions && valueOptions.includes('transform=')) {
              const transformStr = valueOptions.split('transform=')[1].split(',')[0]
              try {
                value = this.applyTransform(value, transformStr, {
                  ...context,
                  index: results.length,
                  items: results
                })
              } catch (e) {
                console.error('Error applying transform in while loop:', transformStr, e)
              }
            }
          } else {
            value = this.parseValue(reader, field.type, field.options, context)
            if (field.options && field.options.includes('transform=')) {
              const transformStr = field.options.split('transform=')[1].split(',')[0]
              try {
                value = this.applyTransform(value, transformStr, {
                  ...context,
                  index: results.length,
                  items: results
                })
              } catch (e) {
                console.error('Error applying transform in while loop:', transformStr, e)
              }
            }
          }

          results.push(value)
        }

        if (!whileCondition.peekField) {
          const lastItem = results[results.length - 1]
          const evalContext = {
            ...context,
            lastValue: lastItem,
            index: results.length - 1,
            items: results
          }
          if (!this.evaluateWhileCondition(whileCondition.condition, evalContext, reader)) {
            break
          }
        }
      }
    } catch (error) {
      console.error(`Error in while loop parsing ${field.name}:`, error)
    }

    return results
  }

  parseArrayCount(options, context) {
    if (!options.includes('count=')) return 0

    const countStr = options.match(REGEX.COUNT)[1]
    if (countStr.startsWith('{') && countStr.endsWith('}')) {
      const varName = countStr.slice(1, -1)
      return this.evaluateExpression(varName, context)
    }
    return parseInt(countStr)
  }

  parseLength(options, context) {
    if (!options.includes('length=')) return 0

    const lengthStr = options.match(REGEX.LENGTH)[1]
    if (lengthStr.startsWith('{') && lengthStr.endsWith('}')) {
      return this.evaluateExpression(lengthStr.slice(1, -1), context)
    }
    return parseInt(lengthStr)
  }

  extractWhileCondition(options) {
    const whileStr = options.match(REGEX.WHILE)[1]
    const peekMatch = whileStr.match(REGEX.PEEK)
    return {
      peekField: !!peekMatch,
      condition: peekMatch ? peekMatch[1] : whileStr
    }
  }

  evaluateCondition(condition, context, reader) {
    try {
      if (condition === 'true') return true
      if (condition === 'false') return false

      if (!this._conditionFuncCache) {
        this._conditionFuncCache = new Map()
      }

      const cacheKey = condition

      let func = this._conditionFuncCache.get(cacheKey)
      if (!func) {
        func = function (contextArg) {
          try {
            const evalFunc = new Function(
              'context',
              `
              with (context) {
                return ${condition};
              }
            `
            )
            return evalFunc(contextArg)
          } catch (e) {
            console.error('Error in condition evaluation:', condition, e)
            return false
          }
        }

        if (this._conditionFuncCache.size < 100) {
          this._conditionFuncCache.set(cacheKey, func)
        }
      }

      return func({ ...context, reader })
    } catch (e) {
      console.error('Error evaluating condition:', condition, e)
      return false
    }
  }

  evaluateWhileCondition(condition, context, reader) {
    return this.evaluateCondition(condition, context, reader)
  }

  evaluateExpression(expression, context) {
    try {
      if (!this._expressionFuncCache) {
        this._expressionFuncCache = new Map()
      }

      const cacheKey = expression

      let func = this._expressionFuncCache.get(cacheKey)
      if (!func) {
        func = function (contextArg) {
          try {
            const evalFunc = new Function(
              'context',
              `
              with (context) {
                return ${expression};
              }
            `
            )
            return evalFunc(contextArg)
          } catch (e) {
            console.error('Error in expression evaluation:', expression, e)
            return 0
          }
        }

        if (this._expressionFuncCache.size < 100) {
          this._expressionFuncCache.set(cacheKey, func)
        }
      }

      return func(context)
    } catch (e) {
      console.error('Error evaluating expression:', expression, e)
      return 0
    }
  }

  applyTransform(value, transform, context = {}) {
    try {
      let safeContext = context

      if (transform.includes('externalData') && !safeContext.externalData) {
        safeContext = Object.create(context)
        safeContext.externalData = {}
      }

      if (!this._transformFuncCache) {
        this._transformFuncCache = new Map()
      }

      const cacheKey = transform

      let func = this._transformFuncCache.get(cacheKey)
      if (!func) {
        func = function (contextArg, valueArg) {
          try {
            const evalFunc = new Function(
              'context',
              'value',
              `
              with (context) {
                try { 
                  return ${transform}; 
                } catch(e) { 
                  console.error("Transform error:", e); 
                  return value; 
                }
              }
            `
            )
            return evalFunc(contextArg, valueArg)
          } catch (e) {
            console.error('Error in transform:', transform, e)
            return valueArg
          }
        }

        if (this._transformFuncCache.size < 100) {
          this._transformFuncCache.set(cacheKey, func)
        }
      }

      return func(safeContext, value)
    } catch (e) {
      console.error('Error applying transform:', transform, e)
      return value
    }
  }

  applyPostProcess(result, postProcess, reader) {
    const context = Object.create(null)

    for (const key in result) {
      context[key] = result[key]
    }

    for (const key in this.internalFields) {
      if (typeof this.internalFields[key] === 'function') {
        context[key] = this.internalFields[key]
      } else {
        context[key] = this.internalFields[key]
      }
    }

    if (reader && !context.reader) {
      context.reader = reader
    }

    if (!this._postProcessFuncCache) {
      this._postProcessFuncCache = new Map()
    }

    for (const postProcessItem of postProcess) {
      try {
        // Handle both array format and object format
        let field, expression, options

        if (Array.isArray(postProcessItem)) {
          // Old array format: [field, expression, options]
          field = postProcessItem[0]
          expression = postProcessItem[1]
          options = postProcessItem[2]
        } else {
          // New object format: { field, expression, condition, ...otherOptions }
          field = postProcessItem.field
          expression = postProcessItem.expression
          options = { ...postProcessItem }
          // Remove field and expression from options since they're not actual options
          delete options.field
          delete options.expression
          // If options is now empty, set to null
          if (Object.keys(options).length === 0) {
            options = null
          }
        }

        if (options && options.condition) {
          const condition = this.evaluateCondition(options.condition, context, reader)
          if (!condition) continue
        }

        const safeExpression = this.makeSafeExpression(expression)

        try {
          const evalFunc = new Function(
            'context',
            `
            with (context) {
              try {
                return ${safeExpression};
              } catch (e) {
                console.error("Post-process error:", e);
                return null;
              }
            }
          `
          )
          result[field] = evalFunc(context)
        } catch (e) {
          console.error('Error executing post-process:', field, safeExpression, e)
          result[field] = null
        }
      } catch (e) {
        console.error('Error in post-processing:', postProcessItem, e)
      }
    }
  }

  makeSafeExpression(expression) {
    if (!this._expressionCache) {
      this._expressionCache = new Map()
    }

    let safeExpr = this._expressionCache.get(expression)
    if (safeExpr) return safeExpr

    if (expression.includes('.filter(function(')) {
      if (expression.includes('_users.filter')) {
        safeExpr = expression.replace(
          REGEX.FILTER_USERS,
          `(_users || []).filter(function($1){try{return $1 && $2}catch(e){return false}})`
        )

        if (this._expressionCache.size < 100) {
          this._expressionCache.set(expression, safeExpr)
        }

        return safeExpr
      }
    }

    return expression
  }

  static MD5(username) {
    const value = Buffer.from(username, 'utf8')
    const cryptoer = crypto.createHash('MD5')
    cryptoer.update(value)
    return cryptoer.digest('hex')
  }

  /**
   * Static method to convert packet definition fields from array format to object format
   * @param {Array} arrayFields - Fields in array format
   * @returns {Array} - Fields in object format
   */
  static convertFieldsToObjectFormat(arrayFields) {
    if (!arrayFields || !Array.isArray(arrayFields)) return []

    return arrayFields.map((field) => {
      if (!Array.isArray(field)) return field // Already in object format

      const fieldObj = {
        name: field[0],
        type: field[1],
        options: field[2] || ''
      }

      // Handle subfields for array type
      if (field[4] && fieldObj.type === 'array') {
        fieldObj.subFields = PacketParser.convertFieldsToObjectFormat(field[4])
      }

      // Add any extra options from field[5]
      if (field[5]) {
        Object.assign(fieldObj, field[5])
      }

      return fieldObj
    })
  }

  getContextWithReader(reader) {
    const context = this.getContext()
    if (reader && !context.reader) {
      context.reader = reader
    }
    return context
  }
}

module.exports = PacketParser

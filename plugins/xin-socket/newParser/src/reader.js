class Reader {
  constructor(buffer) {
    this.buffer = buffer
    this.offset = 0
    this.view = new DataView(buffer instanceof ArrayBuffer ? buffer : buffer.buffer)
  }

  // 預覽下一個字節但不移動指針
  peekUint8() {
    if (this.offset >= this.buffer.byteLength) {
      throw new Error('End of buffer')
    }
    return this.view.getUint8(this.offset)
  }

  // 檢查是否已到達數據結尾
  isEOF() {
    return this.offset >= this.buffer.byteLength
  }

  EOFCount() {
    return this.buffer.byteLength - this.offset
  }

  skip(bytes) {
    this.offset += bytes
  }

  getBytes(length) {
    const value = this.view.getUint8(this.offset, length)
    this.offset += length
    return value
  }

  // 獲取當前位置的 UInt8
  getUint8() {
    if (this.offset >= this.buffer.byteLength) {
      throw new Error('End of buffer')
    }
    const value = this.view.getUint8(this.offset)
    this.offset += 1
    return value
  }

  // 獲取當前位置的 Int8
  getInt8() {
    const value = this.view.getInt8(this.offset)
    this.offset += 1
    return value
  }

  // 獲取當前位置的 UInt16
  getUint16(littleEndian = false) {
    const value = this.view.getUint16(this.offset, littleEndian)
    this.offset += 2
    return value
  }

  // 獲取當前位置的 Int16
  getInt16(littleEndian = false) {
    const value = this.view.getInt16(this.offset, littleEndian)
    this.offset += 2
    return value
  }

  // 獲取當前位置的 UInt24 (3 bytes)
  getUint24(littleEndian = false) {
    let value = 0
    if (littleEndian) {
      value =
        this.view.getUint8(this.offset) |
        (this.view.getUint8(this.offset + 1) << 8) |
        (this.view.getUint8(this.offset + 2) << 16)
    } else {
      value =
        (this.view.getUint8(this.offset) << 16) |
        (this.view.getUint8(this.offset + 1) << 8) |
        this.view.getUint8(this.offset + 2)
    }
    this.offset += 3
    return value
  }

  // 獲取當前位置的 UInt32
  getUint32(littleEndian = false) {
    const value = this.view.getUint32(this.offset, littleEndian)
    this.offset += 4
    return value
  }

  // 獲取當前位置的 Int32
  getInt32(littleEndian = false) {
    const value = this.view.getInt32(this.offset, littleEndian)
    this.offset += 4
    return value
  }

  // 獲取當前位置的 Float32
  getFloat32(littleEndian = false) {
    const value = this.view.getFloat32(this.offset, littleEndian)
    this.offset += 4
    return value
  }

  // 獲取當前位置的 Float64
  getFloat64(littleEndian = false) {
    const value = this.view.getFloat64(this.offset, littleEndian)
    this.offset += 8
    return value
  }

  // 獲取 Long (8 bytes)
  getLong(littleEndian = false) {
    const low = this.view.getUint32(this.offset + (littleEndian ? 0 : 4), littleEndian)
    const high = this.view.getInt32(this.offset + (littleEndian ? 4 : 0), littleEndian)
    this.offset += 8

    // 注意: JavaScript 不能精確表示超過 53 位的整數，可能會有精度丟失
    // 對於需要完整精度的場景，應該使用 BigInt 或專門的庫
    return high * 0x100000000 + low
  }

  // 獲取固定長度的字符串
  getString(size) {
    return String.fromCharCode(...this.getByteArray(size))
  }

  getString16(size) {
    let charCode = []
    for (let i = 0; i < size; i += 2) {
      // little endian
      let L = this.getUint8()
      let H = this.getUint8()
      charCode.push((H << 8) | L)
    }

    return String.fromCharCode(...charCode)
  }

  // 獲取帶長度前綴的字符串
  getStringWithLength() {
    const length = this.getUint16()
    return this.getString(length)
  }

  // 獲取帶長度前綴的 UTF-16 字符串
  getString16WithLength() {
    const length = this.getUint16()
    return this.getString16(length)
  }

  // 獲取帶字節計數的 UTF-16 字符串
  getString16WithByteCount() {
    const length = this.getUint8()
    return this.getString16(length)
  }

  // 讀取剩餘所有字節作為 UTF-16 字符串
  getString16Remaining() {
    const remainingBytes = this.buffer.byteLength - this.offset
    const charCount = remainingBytes
    return this.getString16(charCount)
  }

  // 獲取預定義類型數組
  getPredefinedTypeArray() {
    const count = this.getUint8()
    const result = []
    for (let i = 0; i < count; i++) {
      result.push(this.getUint8())
    }
    return result
  }
  getUint8Color() {
    const red = this.getUint8()
    const green = this.getUint8()
    const blue = this.getUint8()
    return { red, green, blue }
  }
}

module.exports = Reader

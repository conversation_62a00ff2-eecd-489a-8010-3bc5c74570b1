{"version": "1.0.0", "packets": {"Ability": {"name": "Ability", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [999, 999, 999], "isFeature": [171]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "count", "type": "uint8", "options": ""}, {"name": "abilities", "type": "array", "options": "count={count}", "subFields": [{"name": "id", "type": "uint8", "options": ""}, {"name": "increases", "type": "uint8", "options": ""}]}]}, "BuyRebateCard": {"name": "BuyRebateCard", "identify": {"protocolId": 30, "serviceId": 60002, "typeArray": [5], "isFeature": [84]}, "fields": [{"name": "packetpacketSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "isSuccess", "type": "uint8", "options": "transform=value==1"}, {"name": "money", "type": "<PERSON><PERSON>", "options": "littleEndian", "condition": "isSuccess == true"}, {"name": "point", "type": "<PERSON><PERSON>", "options": "littleEndian", "condition": "isSuccess == true"}, {"name": "expiryAt", "type": "string", "options": "withLength16", "condition": "isSuccess == true"}]}, "ExchangePoint": {"name": "ExchangePoint", "identify": {"protocolId": 30, "serviceId": 60002, "typeArray": [40], "isFeature": [84]}, "fields": [{"name": "packetpacketSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "exchangedPoint", "type": "uint24", "options": "bigEndian"}, {"name": "money", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "point", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}, "Info": {"name": "Info", "identify": {"protocolId": 30, "serviceId": 60002, "typeArray": [1], "isFeature": [81]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "_bankData", "type": "string", "options": "utf16,remaining"}], "postProcess": [{"field": "money", "expression": "parseInt(_bankData.split(',')[0])"}, {"field": "point", "expression": "parseInt(_bankData.split(',')[1])"}, {"field": "rewardMoney", "expression": "parseInt(_bankData.split(',')[2])"}, {"field": "safe", "expression": "parseInt(_bankData.split(',')[3])"}, {"field": "payMonthlyExpireAt", "expression": "_bankData.split(',')[4]"}, {"field": "rebateCardExpireAt", "expression": "_bankData.split(',')[5]"}, {"field": "isEnableQpp", "expression": "_bankData.split(',')[6]==1"}]}, "PasswordType": {"name": "PasswordType", "identify": {"protocolId": 30, "serviceId": 60002, "typeArray": [5], "isFeature": [82]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "passwordType", "type": "uint8", "options": ""}]}, "SafeBox": {"name": "SafeBox", "identify": {"protocolId": 30, "serviceId": 60002, "typeArray": [33], "isFeature": [88, 89]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "safe", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "money", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}, "SuccessfullyStored": {"name": "SuccessfullyStored", "identify": {"protocolId": 30, "serviceId": 60002, "typeArray": [50], "typeArrayAliases": [[51]], "isFeature": [86]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "storeType", "type": "uint8", "options": ""}, {"name": "storedPoint", "type": "uint24", "options": "bigEndian", "condition": "storeType !== 99"}, {"name": "point", "type": "uint24", "options": "bigEndian", "condition": "storeType !== 99"}]}, "UseGashCard": {"name": "useGashCard", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 186, 52], "isFeature": [85]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "isSuccess", "type": "uint8", "options": "transform=value==1"}, {"name": "storedPoint", "type": "uint32", "options": "littleEndian", "condition": "isSuccess === true"}, {"name": "point", "type": "uint32", "options": "littleEndian", "condition": "isSuccess === true"}, {"name": "message", "type": "string", "options": "withLength16", "condition": "isSuccess === false"}, {"name": "isGash", "type": "derived", "options": "transform=true"}]}, "YoeCardMessage": {"name": "YoeCardMessage", "identify": {"protocolId": 30, "serviceId": 60002, "typeArray": [49], "isFeature": [85]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "isSuccess", "type": "uint8", "options": "transform=value==1"}, {"name": "message", "type": "string", "options": "remaining"}]}, "ChannelInfo": {"name": "ChannelInfo", "identify": {"protocolId": 30, "serviceId": 60902, "aliases": [60000, 60901, 60903], "typeArray": [0, 18, 12], "isFeature": [121]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "name", "type": "string", "options": "withLength16"}, {"name": "users", "type": "array", "options": "while=reader.offset < packetSize", "subFields": [{"name": "_usernameLength", "type": "uint8", "options": ""}, {"name": "_rawUsername", "type": "string", "options": "utf16,length={_usernameLength*2}"}, {"name": "_rawRank", "type": "uint8", "options": ""}, {"name": "vipLevel", "type": "uint8", "options": ""}, {"name": "_isHighRank", "type": "derived", "options": "transform=_rawRank>=100"}, {"name": "rank", "type": "derived", "options": "transform=_isHighRank?_rawRank-100:_rawRank"}, {"name": "gender", "type": "derived", "options": "transform=_isHighRank ? 2 : 0"}, {"name": "_usernameParts", "type": "derived", "options": "transform=_rawUsername.split('\\t')", "condition": "_isHighRank===true"}, {"name": "username", "type": "derived", "options": "transform=_usernameParts[0]", "condition": "_isHighRank===true"}, {"name": "facebookId", "type": "derived", "options": "transform=_usernameParts.length>1?_usernameParts[1].substring(2):null", "condition": "_isHighRank===true"}, {"name": "username", "type": "derived", "options": "transform=_rawUsername", "condition": "_isHighRank===false"}, {"name": "facebookId", "type": "derived", "options": "transform=undefined", "condition": "_isHighRank===false"}]}]}, "ChannelList": {"name": "ChannelList", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 18, 5], "isFeature": [122]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "channels", "type": "array", "options": "while=reader.offset < packetSize", "subFields": [{"name": "name", "type": "string", "options": "withLength16"}, {"name": "population", "type": "uint16", "options": "bigEndian"}]}]}, "ChannelRemove": {"name": "ChannelRemove", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 18, 7], "isFeature": [123]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "name", "type": "string", "options": "withLength16"}]}, "ChannelUpdate": {"name": "ChannelUpdate", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 18, 6], "isFeature": [124]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "channels", "type": "array", "options": "while=reader.offset < packetSize", "subFields": [{"name": "name", "type": "string", "options": "withLength16"}, {"name": "population", "type": "uint16", "options": "bigEndian"}]}]}, "ChannelUserExit": {"name": "ChannelUserExit", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 18, 0], "isFeature": [125]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "users", "type": "array", "options": "while=reader.offset < packetSize", "subFields": [{"name": "username", "type": "string", "options": "withLength16"}]}]}, "ChannelUserJoin": {"name": "ChannelUserJoin", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 18, 11], "isFeature": [126]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "users", "type": "array", "options": "while=reader.offset < packetSize", "subFields": [{"name": "_usernameLength", "type": "uint8", "options": ""}, {"name": "_rawUsername", "type": "string", "options": "utf16,length={_usernameLength*2}"}, {"name": "_rawRank", "type": "uint8", "options": ""}, {"name": "vipLevel", "type": "uint8", "options": ""}, {"name": "_isHighRank", "type": "derived", "options": "transform=_rawRank>=100"}, {"name": "rank", "type": "derived", "options": "transform=_isHighRank?_rawRank-100:_rawRank"}, {"name": "gender", "type": "derived", "options": "transform=_isHighRank ? 2 : 0"}, {"name": "_usernameParts", "type": "derived", "options": "transform=_rawUsername.split('\\t')", "condition": "_isHighRank===true"}, {"name": "username", "type": "derived", "options": "transform=_usernameParts[0]", "condition": "_isHighRank===true"}, {"name": "facebookId", "type": "derived", "options": "transform=_usernameParts.length>1?_usernameParts[1].substring(2):null", "condition": "_isHighRank===true"}, {"name": "username", "type": "derived", "options": "transform=_rawUsername", "condition": "_isHighRank===false"}, {"name": "facebookId", "type": "derived", "options": "transform=undefined", "condition": "_isHighRank===false"}]}]}, "ChatRoomMessage": {"name": "ChatRoomMessage", "identify": {"protocolId": 30, "serviceId": 60902, "aliases": [60000, 60901, 60903, 60904], "typeArray": [0, 17]}, "externalData": {"source": "PROVIDERS", "path": "config"}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "messageType", "type": "uint8", "options": ""}, {"name": "_items", "type": "array", "options": "while=reader.offset < packetSize", "subFields": [{"name": "contentType", "type": "uint8", "options": ""}, {"name": "userColorRed", "type": "uint8", "options": "", "condition": "contentType === 1 || contentType === 2 || contentType === 3 || contentType === 5"}, {"name": "userColorGreen", "type": "uint8", "options": "", "condition": "contentType === 1 || contentType === 2 || contentType === 3 || contentType === 5"}, {"name": "userColorBlue", "type": "uint8", "options": "", "condition": "contentType === 1 || contentType === 2 || contentType === 3 || contentType === 5"}, {"name": "username", "type": "string", "options": "withLength16", "condition": "contentType === 1"}, {"name": "usernameLength", "type": "uint8", "options": "", "condition": "contentType === 2"}, {"name": "rawUsername", "type": "string", "options": "utf16,length={usernameLength*2}", "condition": "contentType === 2"}, {"name": "rank", "type": "uint8", "options": "", "condition": "contentType === 2"}, {"name": "message", "type": "string", "options": "withLength16", "condition": "contentType === 3"}, {"name": "stickerId", "type": "uint32", "options": "bigEndian", "condition": "contentType === 4"}, {"name": "cardId", "type": "uint16", "options": "bigEndian", "condition": "contentType === 5"}, {"name": "customMessageLength", "type": "uint16", "options": "bigEndian", "condition": "contentType === 6"}, {"name": "customMessage", "type": "string", "options": "utf16,length={customMessageLength}", "condition": "contentType === 6"}, {"name": "customUrlLength", "type": "uint16", "options": "bigEndian", "condition": "contentType === 6"}, {"name": "customUrl", "type": "string", "options": "utf16,length={customUrlLength}", "condition": "contentType === 6"}, {"name": "soundUrlLength", "type": "uint16", "options": "bigEndian", "condition": "contentType === 7"}, {"name": "soundUrl", "type": "string", "options": "utf16,length={soundUrlLength}", "condition": "contentType === 7"}, {"name": "cloudMessage", "type": "string", "options": "withLength16", "condition": "contentType === 8"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "uint8", "options": "", "condition": "contentType === 100"}]}, {"name": "_isContentType1", "type": "derived", "options": "transform=function() { if(!_items || !Array.isArray(_items)) return false; for(var i = 0; i < _items.length; i++) { if(_items[i].contentType === 1) return true; } return false; }()"}, {"name": "_isContentType2", "type": "derived", "options": "transform=function() { if(!_items || !Array.isArray(_items)) return false; for(var i = 0; i < _items.length; i++) { if(_items[i].contentType === 2) return true; } return false; }()"}, {"name": "_username1", "type": "derived", "options": "transform=function() { if(!_items || !Array.isArray(_items)) return ''; for(var i = 0; i < _items.length; i++) { if(_items[i].contentType === 1) return _items[i].username; } return ''; }()", "condition": "_isContentType1 === true"}, {"name": "_rawUsername2", "type": "derived", "options": "transform=function() { if(!_items || !Array.isArray(_items)) return ''; for(var i = 0; i < _items.length; i++) { if(_items[i].contentType === 2) return _items[i].rawUsername; } return ''; }()", "condition": "_isContentType2 === true"}, {"name": "_rawRank2", "type": "derived", "options": "transform=function() { if(!_items || !Array.isArray(_items)) return 0; for(var i = 0; i < _items.length; i++) { if(_items[i].contentType === 2) return _items[i].rank; } return 0; }()", "condition": "_isContentType2 === true"}, {"name": "_isHighRank", "type": "derived", "options": "transform=_rawRank2>=100", "condition": "_isContentType2 === true"}, {"name": "_usernameParts", "type": "derived", "options": "transform=_rawUsername2.split('\\t')", "condition": "_isContentType2 === true && _isHighRank === true"}, {"name": "_username2", "type": "derived", "options": "transform=_isHighRank ? _usernameParts[0] : _rawUsername2", "condition": "_isContentType2 === true"}, {"name": "_userName", "type": "derived", "options": "transform=function() { if(_isContentType1) return _username1 || ''; if(_isContentType2) return _username2 || ''; return ''; }()"}, {"name": "_userPath", "type": "md5", "options": "source=_userName,prefix=星城_"}], "postProcess": [{"field": "user", "expression": "function() { var result = { name: _userName, path: _userPath, color: { red: 0, green: 0, blue: 0 } }; if(!_items || !Array.isArray(_items)) return result; for(var i = 0; i < _items.length; i++) { var item = _items[i]; if(item.contentType === 1) { result.color.red = item.userColorRed ; result.color.green = item.userColorGreen ; result.color.blue = item.userColorBlue ; break; } else if(item.contentType === 2) { var rank = item.rank; if(rank >= 100) { result.facebookId = _usernameParts.length > 1 ? _usernameParts[1].replace('2-', '') : ''; rank = rank - 100; } result.rank = rank; result.color.red = item.userColorRed ; result.color.green = item.userColorGreen ; result.color.blue = item.userColorBlue ; break; } } return result; }()"}, {"field": "content", "expression": "function() { var storageBaseURL = externalData.storageBaseURL; var result = { type: -1, color: { red: 255, green: 255, blue: 0 }, allMessage: '' }; if(!_items || !Array.isArray(_items)) return result; var lastContentItem = null; var allMessage = ''; for(var i = 0; i < _items.length; i++) { var item = _items[i]; if(item.contentType === 1) { allMessage += item.username; } else if(item.contentType === 2) { var username = item.rawUsername; if(item.rank >= 100) { username = username.split('\\t')[0]; } allMessage += username; } else if(item.contentType === 3) { allMessage += item.message; lastContentItem = { type: item.contentType, message: item.message, color: { red: item.userColorRed, green: item.userColorGreen, blue: item.userColorBlue } }; } else if(item.contentType === 4) { allMessage += item.stickerId; lastContentItem = { type: item.contentType, stickerId: item.stickerId }; } else if(item.contentType === 5) { allMessage += item.cardId; lastContentItem = { type: item.contentType, cardId: item.cardId, color: { red: item.userColorRed, green: item.userColorGreen, blue: item.userColorBlue } }; } else if(item.contentType === 6) { allMessage += item.customMessage + ' ' + item.customUrl; lastContentItem = { type: item.contentType, message: item.customMessage, url: item.customUrl }; } else if(item.contentType === 7) { allMessage += item.soundUrl; lastContentItem = { type: item.contentType, url: item.soundUrl }; } else if(item.contentType === 8) { var [type, token, path1, path2] = item.cloudMessage.split(' '); if(type == 1) { lastContentItem = { type: item.contentType, dataType: 1, message: item.cloudMessage, color: { red: item.userColorRed, green: item.userColorGreen, blue: item.userColorBlue } }; var thumbUrl = storageBaseURL + 'userfile\\/' + _userPath + '\\/' + path2.toUpperCase() + '\\/data.png?t=' + token; var imageUrl = storageBaseURL + 'userfile\\/' + _userPath + '\\/' + path1.toUpperCase() + '\\/data.png?t=' + token; lastContentItem.thumbUrl = thumbUrl; lastContentItem.imageUrl = imageUrl; } else if(type == 2) { lastContentItem = { type: item.contentType, dataType: 2, message: item.cloudMessage, color: { red: item.userColorRed, green: item.userColorGreen, blue: item.userColorBlue } }; var voiceUrl = storageBaseURL + 'userfile\\/' + _userPath + '\\/' + path1.toUpperCase() + '\\/data.mp3?t=' + token; lastContentItem.voiceUrl = voiceUrl; } } else if(item.contentType === 100) { allMessage += item.collectedHeart; lastContentItem = { type: item.contentType, collectedHeart: item.collectedHeart, message: item.collectedHeart }; } } if(lastContentItem) { Object.assign(result, lastContentItem); } result.allMessage = allMessage; return result; }()"}]}, "ChangedRelation": {"name": "ChangedRelation", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 50, 1], "isFeature": [103]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "success", "type": "uint8", "options": "transform=value === 1"}, {"name": "online", "type": "uint8", "options": "transform=value === 1", "condition": "success"}, {"name": "relationLength", "type": "uint16", "options": "", "condition": "success"}, {"name": "relation", "type": "uint8", "options": "", "condition": "success && relationLength > 0"}, {"name": "username", "type": "string", "options": "withLength16", "condition": "!online && success"}, {"name": "usernameLength", "type": "uint8", "options": "", "condition": "success && online && relationLength > 0"}, {"name": "username", "type": "string", "options": "length={usernameLength*2},utf16", "condition": "success && online && relationLength > 0"}, {"name": "rank", "type": "uint8", "options": "", "condition": "success && online && relationLength > 0"}, {"name": "errorMessage", "type": "string", "options": "withLength16", "condition": "!success"}], "postProcess": [{"field": "gender", "expression": "rank >= 100 ? 2 : 0", "condition": "success && online && relationLength > 0"}, {"field": "rank", "expression": "rank >= 100 ? rank - 100 : rank", "condition": "success && online && relationLength > 0"}, {"field": "facebookId", "expression": "username.includes('\\t') ? username.split('\\t')[1] : ''", "condition": "success && online && relationLength > 0"}, {"field": "username", "expression": "username.includes('\\t') ? username.split('\\t')[0] : username", "condition": "success && online && relationLength > 0"}]}, "FriendOffline": {"name": "FriendOffline", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 50, 20], "isFeature": [102]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "username", "type": "string", "options": "withLength16"}]}, "FriendOnline": {"name": "FriendOnline", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 50, 21], "isFeature": [101]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "usernameLength", "type": "uint8", "options": ""}, {"name": "username", "type": "string", "options": "utf16,length={usernameLength*2}"}, {"name": "rank", "type": "uint8", "options": ""}, {"name": "gender", "type": "derived", "options": "transform=2", "condition": "rank >= 100"}, {"name": "facebookId", "type": "derived", "options": "transform=username.split('\t')[1]", "condition": "rank >= 100"}, {"name": "username", "type": "derived", "options": "transform=username.split('\t')[0]", "condition": "rank >= 100"}, {"name": "rank", "type": "derived", "options": "transform=rank - 100", "condition": "rank >= 100"}]}, "FriendInit": {"name": "FriendInit", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 50, 31], "isFeature": [70]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "count", "type": "uint16", "options": "bigEndian"}, {"name": "_users", "type": "array", "options": "count={count}", "subFields": [{"name": "online", "type": "uint8", "options": "transform=value==1"}, {"name": "_relationCount", "type": "uint16", "options": "bigEndian"}, {"name": "_relations", "type": "value_array", "options": "count={_relationCount}"}, {"name": "relation", "type": "derived", "options": "transform=function() { var r = 0; for(var i = 0; i < _relations.length; i++) { var v = _relations[i]; if(v === 0) r |= 0x02; else if(v === 1) r |= 0x01; else if(v === 3) r |= 0x04; } return r; }()"}, {"name": "_usernameLength", "type": "uint8", "options": "", "condition": "online == true"}, {"name": "_rawUsername", "type": "string", "options": "utf16,length={_usernameLength*2}", "condition": "online == true"}, {"name": "_rawRank", "type": "uint8", "options": "", "condition": "online == true"}, {"name": "username", "type": "string", "options": "withLength16", "condition": "online == false"}, {"name": "level", "type": "uint32", "options": "bigEndian"}, {"name": "vip", "type": "uint8", "options": ""}, {"name": "_isHighRank", "type": "derived", "options": "transform=_rawRank>=100", "condition": "online===true"}, {"name": "rank", "type": "derived", "options": "transform=_isHighRank?_rawRank-100:_rawRank", "condition": "online===true"}, {"name": "rank", "type": "derived", "options": "transform=0", "condition": "online===false"}, {"name": "gender", "type": "derived", "options": "transform=_isHighRank?2:0", "condition": "online===true"}, {"name": "gender", "type": "derived", "options": "transform=0", "condition": "online===false"}, {"name": "_usernameParts", "type": "derived", "options": "transform=_rawUsername.split('\\t')", "condition": "online===true"}, {"name": "username", "type": "derived", "options": "transform=_usernameParts[0]", "condition": "online===true"}, {"name": "facebookId", "type": "derived", "options": "transform=_usernameParts.length>1?_usernameParts[1].substring(2):null", "condition": "online===true"}, {"name": "facebookId", "type": "derived", "options": "transform=''", "condition": "online===false"}]}], "postProcess": [{"field": "friendList", "expression": "_users.filter(function(u){return u.relation&0x01})"}, {"field": "blockList", "expression": "_users.filter(function(u){return u.relation&0x02})"}]}, "GrandPrizeNoty": {"name": "GrandPrizeNoty", "identify": {"protocolId": 30, "serviceId": 60903, "aliases": [60000, 60901, 60902, 60891, 60904], "typeArray": [0, 47], "isFeature": [230]}, "externalData": {"source": "GCP", "path": "grandPrizeNoty.json"}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "gameId", "type": "uint16", "options": "bigEndian"}, {"name": "mappingType", "type": "derived", "options": "transform=externalData.gameMapping.find(item => item.gameUnpackIdList.includes(gameId))?.gameUnpackType || 7"}, {"name": "prizeId", "type": "uint16", "options": "", "condition": "mappingType === 0"}, {"name": "name", "type": "string", "options": "withLength16", "condition": "mappingType === 0"}, {"name": "prizeParam1", "type": "uint8", "options": "", "condition": "mappingType === 1"}, {"name": "prizeParam2", "type": "uint8", "options": "", "condition": "mappingType === 1"}, {"name": "scorePoint", "type": "uint32", "options": "", "condition": "mappingType === 1"}, {"name": "name", "type": "string", "options": "withLength16", "condition": "mappingType === 1"}, {"name": "prizeId", "type": "string", "options": "withLength16", "condition": "mappingType === 2"}, {"name": "scorePoint", "type": "uint32", "options": "", "condition": "mappingType === 2"}, {"name": "name", "type": "string", "options": "withLength16", "condition": "mappingType === 2"}, {"name": "scorePoint", "type": "uint32", "options": "", "condition": "mappingType === 3"}, {"name": "name", "type": "string", "options": "withLength16", "condition": "mappingType === 3"}, {"name": "prizeId", "type": "uint16", "options": "", "condition": "mappingType === 4"}, {"name": "prizeParam1", "type": "uint8", "options": "", "condition": "mappingType === 4"}, {"name": "prizeParam2", "type": "uint8", "options": "", "condition": "mappingType === 4"}, {"name": "scorePoint", "type": "uint32", "options": "", "condition": "mappingType === 4"}, {"name": "name", "type": "string", "options": "withLength16", "condition": "mappingType === 4"}, {"name": "fishType", "type": "uint32", "options": "", "condition": "mappingType === 5"}, {"name": "scorePoint", "type": "uint32", "options": "", "condition": "mappingType === 5"}, {"name": "name", "type": "string", "options": "withLength16", "condition": "mappingType === 5"}, {"name": "skillNo", "type": "uint16", "options": "", "condition": "mappingType === 6"}, {"name": "scorePoint", "type": "uint32", "options": "", "condition": "mappingType === 6"}, {"name": "name", "type": "string", "options": "withLength16", "condition": "mappingType === 6"}, {"name": "prizeId", "type": "uint16", "options": "", "condition": "mappingType === 7"}, {"name": "scorePoint", "type": "uint32", "options": "", "condition": "mappingType === 7"}, {"name": "name", "type": "string", "options": "withLength16", "condition": "mappingType === 7"}, {"name": "fishType", "type": "uint32", "options": "", "condition": "mappingType === 8"}, {"name": "betRange", "type": "uint8", "options": "", "condition": "mappingType === 8"}, {"name": "userTakeType", "type": "uint8", "options": "", "condition": "mappingType === 8"}, {"name": "name", "type": "string", "options": "withLength16", "condition": "mappingType === 8"}, {"name": "scorePoint", "type": "uint32", "options": "", "condition": "mappingType === 8"}, {"name": "prizeId", "type": "uint32", "options": "", "condition": "mappingType === 9"}, {"name": "scorePoint", "type": "uint32", "options": "", "condition": "mappingType === 9"}, {"name": "name", "type": "string", "options": "withLength16", "condition": "mappingType === 9"}]}, "AcceptGuildMembers": {"name": "AcceptGuildMembers", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [45], "isFeature": [187]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "success", "type": "uint8", "options": "transform=value==1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "AddApplicationNotify": {"name": "AddApplicationNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [73], "isFeature": [196]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "AddGuildVicePresident": {"name": "AddGuildVicePresident", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [48], "isFeature": [190]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "success", "type": "uint8", "options": "transform=value==1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "AddMemberNotify": {"name": "AddMemberNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [75], "isFeature": [198]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "response", "type": "string", "options": "withLength16"}, {"name": "isOnline", "type": "uint8", "options": "transform=value==1"}]}, "AddVicePresidentNotify": {"name": "AddVicePresidentNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [77], "isFeature": [200]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "AppLicantsList": {"name": "AppLicantsList", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [85], "isFeature": [208]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "userCount", "type": "uint8", "options": ""}, {"name": "username", "type": "value_array", "options": "type=withLength16,count={userCount}"}]}, "ChangeCaptionNotify": {"name": "ChangeCaptionNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [83], "isFeature": [206]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "ChangeGuildCaption": {"name": "ChangeGuildCaption", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [43], "isFeature": [185]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "success", "type": "uint8", "options": "transform=value==1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "ChangeGuildName": {"name": "ChangeGuildName", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [41], "isFeature": [183]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "success", "type": "uint8", "options": "transform=value==1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "ChangeGuildTotem": {"name": "ChangeGuildTotem", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [42], "isFeature": [184]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "success", "type": "uint8", "options": "transform=value==1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "ChangeNameNotify": {"name": "ChangeNameNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [81], "isFeature": [204]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "guildname", "type": "string", "options": "byteCount"}]}, "ChangeTotemNotify": {"name": "ChangeTotemNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [82], "isFeature": [205]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}]}, "GameMissionNotify": {"name": "GameMissionNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [88], "isFeature": [211]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "msgList", "type": "uint8", "options": ""}, {"name": "msgList", "type": "array", "options": "count={msgList}", "subFields": [{"name": "unKnow", "type": "uint16", "options": ""}, {"name": "gamemission", "type": "string", "options": "withLength16"}]}]}, "GetGuildChat": {"name": "GetGuildChat", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [70], "isFeature": [193]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "time", "type": "long", "options": "littleEndian"}, {"name": "chatType", "type": "uint8", "options": ""}, {"name": "username", "type": "string", "options": "withLength16"}, {"name": "message", "type": "string", "options": "withLength16"}]}, "GuildDetail": {"name": "GuildDetail", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [11], "isFeature": [194]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": "bigEndian"}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "success", "type": "uint8", "options": "transform=value==1"}, {"name": "guildId", "type": "<PERSON><PERSON>", "options": "littleEndian", "condition": "success == true"}, {"name": "guildname", "type": "string", "options": "withLength16", "condition": "success == true"}, {"name": "pointcount", "type": "uint8", "options": "", "condition": "success == true"}, {"name": "guildpoints", "type": "value_array", "options": "type=uint32,count={pointcount},bigEndian", "condition": "success == true"}, {"name": "presidentname", "type": "string", "options": "withLength16", "condition": "success == true"}, {"name": "guildcaption", "type": "string", "options": "withLength16", "condition": "success == true"}, {"name": "vicepresidentcount", "type": "uint8", "options": "", "condition": "success == true"}, {"name": "vicepresidentnames", "type": "value_array", "options": "type=withLength16,count={vicepresidentcount}", "condition": "success == true"}, {"name": "membercount", "type": "uint16", "options": "bigEndian", "condition": "success == true"}, {"name": "members", "type": "array", "options": "count={membercount}", "subFields": [{"name": "name", "type": "string", "options": "withLength16"}, {"name": "rank", "type": "derived", "options": "transform=name===presidentname?3:(vicepresidentnames.some(vp=>vp===name)?2:1)"}, {"name": "points", "type": "uint32", "options": "littleEndian"}, {"name": "onlines", "type": "uint8", "options": ""}], "condition": "success == true"}], "postProcess": [{"field": "members", "expression": "members.sort((a, b) => { if (a.rank !== b.rank) return b.rank - a.rank; if (a.onlines !== b.onlines) return b.onlines - a.onlines; return b.points - a.points; })", "condition": "success == true && members && members.length > 0"}]}, "GetUserGuild": {"name": "GetUserGuild", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [14], "isFeature": [181]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "username", "type": "string", "options": "withLength16"}, {"name": "guildId", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "hasGuild", "type": "derived", "options": "transform=guildId > 0"}, {"name": "guildName", "type": "string", "options": "withLength16", "condition": "hasGuild == true"}]}, "GuildAddMembership": {"name": "GuildAddMembership", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [44], "isFeature": [186]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "success", "type": "uint8", "options": "transform=value==1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "GuildChatError": {"name": "GuildChatError", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [0, 102], "isFeature": [212]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "unKnow", "type": "uint16", "options": ""}, {"name": "response", "type": "string", "options": "remaining"}]}, "GuildGetMission": {"name": "GuildGetMission", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [13]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "missionIds", "type": "uint8", "options": ""}, {"name": "missionRate", "type": "uint8", "options": "", "condition": "missionIds != 0"}, {"name": "missionLimit", "type": "uint8", "options": "", "condition": "missionIds != 0"}]}, "GuildGetRanking": {"name": "GuildGetRanking", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [12], "isFeature": [182]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "guildAward", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "time", "type": "string", "options": "withLength16"}, {"name": "count", "type": "uint16", "options": "bigEndian"}, {"name": "guildDatas", "type": "array", "options": "count={count}", "subFields": [{"name": "id", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "name", "type": "string", "options": "withLength16"}, {"name": "onlines", "type": "uint16", "options": "bigEndian"}, {"name": "members", "type": "uint16", "options": "bigEndian"}, {"name": "points", "type": "uint32", "options": "littleEndian"}]}, {"name": "unKnow", "type": "uint8", "options": ""}, {"name": "rateCount", "type": "uint8", "options": ""}, {"name": "weeklyRates", "type": "value_array", "options": "count={rateCount}"}], "postProcess": [{"field": "guildDatas", "expression": "guildDatas.sort((a, b) => { return b.points - a.points })"}]}, "GuildInit": {"name": "GuildInit", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [72]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "hasGuild", "type": "uint8", "options": "transform=value==1"}, {"name": "guildId", "type": "<PERSON><PERSON>", "options": "littleEndian", "condition": "hasGuild == true"}, {"name": "guildName", "type": "string", "options": "withLength16", "condition": "hasGuild == true"}, {"name": "rankCount", "type": "uint8", "options": "", "condition": "hasGuild == true"}, {"name": "ranks", "type": "value_array", "options": "type=uint32,count={rankCount}", "condition": "hasGuild == true"}, {"name": "owner", "type": "string", "options": "withLength16", "condition": "hasGuild == true"}, {"name": "direction", "type": "string", "options": "withLength16", "condition": "hasGuild == true"}, {"name": "vicepresidentCount", "type": "uint8", "options": "", "condition": "hasGuild == true"}, {"name": "vicepresidentNames", "type": "value_array", "options": "type=withLength16,count={vicepresidentCount}", "condition": "hasGuild == true"}, {"name": "memberCount", "type": "uint16", "options": "bigEndian", "condition": "hasGuild == true"}, {"name": "members", "type": "array", "options": "count={memberCount}", "subFields": [{"name": "username", "type": "string", "options": "withLength16"}, {"name": "value", "type": "uint32", "options": "littleEndian"}, {"name": "online", "type": "uint8", "options": ""}], "condition": "hasGuild == true"}]}, "GuildLeave": {"name": "GuildLeave", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [50], "isFeature": [192]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "success", "type": "uint8", "options": "transform=value==1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "GuildMemberLeaveNotify": {"name": "GuildMemberLeaveNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [84], "isFeature": [207]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "name", "type": "string", "options": "withLength16"}]}, "GuildMemberOnlineNotify": {"name": "GuildMemberOnlineNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [79], "isFeature": [202]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "GuildMemberOfflineNotify": {"name": "GuildMemberOfflineNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [80], "isFeature": [203]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "GuildMessageTimeoutNotify": {"name": "GuildMessageTimeoutNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [71], "isFeature": [195]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "time", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}, "PersonalBadge": {"name": "PersonalBadge", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [9], "isFeature": [47]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "skip", "type": "uint8", "options": ""}, {"name": "badges", "type": "array", "options": "while=reader.offset < packetSize", "subFields": [{"name": "id", "type": "long", "options": "littleEndian"}, {"name": "properties", "type": "uint8", "options": ""}, {"name": "propertyDetails", "type": "value_array", "options": "type=uint16,count={properties}"}]}]}, "GuildPointClearNotify": {"name": "GuildPointClearNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [87], "isFeature": [210]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "GuildPointRefreshNotify": {"name": "GuildPointRefreshNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [86], "isFeature": [209]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "pointcount", "type": "uint8", "options": ""}, {"name": "points", "type": "value_array", "options": "type=uint32,count={pointcount}"}, {"name": "username", "type": "string", "options": "withLength16"}, {"name": "userpoint", "type": "uint32", "options": "littleEndian"}]}, "GuildRejectMembership": {"name": "GuildRejectMembership", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [46], "isFeature": [188]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "success", "type": "uint8", "options": "transform=value==1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "GuildRemoveApplicationNotify": {"name": "GuildRemoveApplicationNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [74], "isFeature": [197]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "GuildRemoveMembership": {"name": "GuildRemoveMembership", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [47], "isFeature": [189]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "success", "type": "uint8", "options": "transform=value==1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "GuildRemoveVicePresident": {"name": "GuildRemoveVicePresident", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [49], "isFeature": [209]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "success", "type": "uint8", "options": "transform=value==1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "GuildRemoveMemberNotify": {"name": "GuildRemoveMemberNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [76], "isFeature": [199]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "GuildRemoveVicePresidentNotify": {"name": "GuildRemoveVicePresidentNotify", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [78], "isFeature": [201]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "response", "type": "string", "options": "withLength16"}]}, "MailCoinLimit": {"name": "MailCoinLimit", "identify": {"protocolId": 30, "serviceId": 60903, "aliases": [60000, 60901, 60902, 60891, 60904], "typeArray": [0, 170, 254], "isFeature": [75]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "coinLimit", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}, "MailDeleteAll": {"name": "MailDeleteAll", "identify": {"protocolId": 30, "serviceId": 60903, "aliases": [60000, 60901, 60902, 60891, 60904], "typeArray": [0, 170, 6], "isFeature": [74]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "isSuccess", "type": "uint8", "options": "transform=value==1"}, {"name": "message", "type": "string", "options": "remaining"}]}, "MailDelete": {"name": "MailDelete", "identify": {"protocolId": 30, "serviceId": 60903, "aliases": [60000, 60901, 60902, 60891, 60904], "typeArray": [0, 170, 3], "isFeature": [74]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "mailId", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}, "MailGetNew": {"name": "MailGetNew", "identify": {"protocolId": 30, "serviceId": 60903, "aliases": [60000, 60901, 60902, 60891, 60904], "typeArray": [0, 170, 11], "isFeature": [71]}, "externalData": {"source": "GCP", "path": "inventory_items/item_provider/{STATION}/item_provider.json"}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "mailId", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "from", "type": "string", "options": "withLength16"}, {"name": "expire", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "title", "type": "string", "options": "withLength16"}, {"name": "content", "type": "string", "options": "withLength16"}, {"name": "link", "type": "string", "options": "withLength16"}, {"name": "itemType", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "count", "type": "uint32", "options": "bigEndian"}, {"name": "status", "type": "uint8", "options": ""}, {"name": "isRead", "type": "derived", "options": "transform=status!=0"}, {"name": "creatDate", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "itemsLength", "type": "uint8", "options": ""}, {"name": "items", "type": "array", "options": "count={itemsLength}", "subFields": [{"name": "id", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "count", "type": "uint32", "options": "bigEndian"}]}, {"name": "canReceive", "type": "derived", "options": "transform=externalData.arrayData.find(item => item.id === itemType.toString())?.mail.canReceive && items.every((item) => externalData.arrayData.find(dataItem => dataItem.id === item.id.toString())?.mail.canReceive)"}], "postProcess": [{"field": "expire", "expression": "expire ? new Date(Date.UTC(1601, 0, 1) + (+expire.toString().substring(0, expire.toString().length - 4))) : null"}, {"field": "creatDate", "expression": "creatDate ? new Date(Date.UTC(1601, 0, 1) + (+creatDate.toString().substring(0, creatDate.toString().length - 4))) : null"}]}, "GetOfficialNames": {"name": "getOfficialNames", "identify": {"protocolId": 30, "serviceId": 60903, "aliases": [60000, 60901, 60902, 60891, 60904], "typeArray": [0, 170, 101], "isFeature": [75]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "list", "type": "array", "options": "while=reader.offset < packetSize", "subFields": [{"name": "id", "type": "uint8", "options": ""}, {"name": "name", "type": "string", "options": "withLength16"}]}]}, "ReadAllMail": {"name": "ReadAllMail", "identify": {"protocolId": 30, "serviceId": 60903, "aliases": [60000, 60901, 60902, 60891, 60904], "typeArray": [0, 170, 12], "isFeature": [73]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "mailIds", "type": "value_array", "options": "type=ulong,while=reader.offset < packetSize,littleEndian"}]}, "ReadMail": {"name": "ReadMail", "identify": {"protocolId": 30, "serviceId": 60903, "aliases": [60000, 60901, 60902, 60891, 60904], "typeArray": [0, 170, 2], "isFeature": [73]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "mailId", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "isRead", "type": "uint8", "options": "transform=value!=0"}, {"name": "message", "type": "string", "options": "remaining"}]}, "SendMail": {"name": "SendMail", "identify": {"protocolId": 30, "serviceId": 60903, "aliases": [60000, 60901, 60902, 60891, 60904], "typeArray": [0, 170, 4], "isFeature": [72]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "isSuccess", "type": "uint8", "options": "transform=value==1"}, {"name": "message", "type": "string", "options": "remaining"}]}, "MiniGameAnswer": {"name": "MiniGameAnswer", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 70, 3], "isFeature": [243]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "answerType", "type": "uint8", "options": ""}]}, "MiniGameClearCanvas": {"name": "MiniGameClearCanvas", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 70, 9], "isFeature": [249]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}]}, "MiniGameForceClose": {"name": "MiniGameForceClose", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 70, 10], "isFeature": [250]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}]}, "MiniGameEnd": {"name": "MiniGameEnd", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 70, 4], "isFeature": [244]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "endType", "type": "uint8", "options": ""}]}, "MiniGameStart": {"name": "MiniGameStart", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 70, 2], "isFeature": [242]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "gameType", "type": "uint8", "options": ""}, {"name": "countDownTime", "type": "uint8", "options": ""}, {"name": "moderator<PERSON><PERSON><PERSON>", "type": "uint8", "options": ""}, {"name": "moderator", "type": "string", "options": "utf16,length={moderatorLength}"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "uint8", "options": "", "condition": "gameType == 1"}, {"name": "heading", "type": "string", "options": "utf16,length={headingLength}", "condition": "gameType == 1"}]}, "MiniGameSetHost": {"name": "MiniGameSetHost", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 70, 1], "isFeature": [241]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "gameType", "type": "uint8", "options": ""}, {"name": "unknown", "type": "uint8", "options": ""}, {"name": "answerCount", "type": "uint8", "options": ""}, {"name": "answers", "type": "value_array", "options": "type=byteCount,count={answerCount}"}]}, "MiniGameUpdateCanvas": {"name": "MiniGameUpdateCanvas", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 70, 8], "isFeature": [248]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "pointSize", "type": "uint16", "options": "bigEndian"}, {"name": "answers", "type": "array", "options": "count={pointSize}", "subFields": [{"name": "x", "type": "uint8", "options": ""}, {"name": "y", "type": "uint8", "options": ""}, {"name": "color", "type": "uint8", "options": "transform=function(value) { const h = value / 255; const s = 1; const v = 1; const k = (n) => (n + h * 6) % 6; const f = (n) => v * (1 - s * Math.max(0, Math.min(k(n), 4 - k(n), 1))); return { r: Math.round(255 * f(5)), g: Math.round(255 * f(3)), b: Math.round(255 * f(1)) }; }"}]}]}, "GetMahjongRank": {"name": "GetMahjongRank", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 170, 8, 1], "isFeature": [76]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=4"}, {"name": "_rankLength", "type": "uint8", "options": ""}, {"name": "rank", "type": "string", "options": "utf16,length=_rankLength"}]}, "XinToMahjong": {"name": "XinToMahjong", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 170, 8, 2], "isFeature": [76]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=4"}, {"name": "state", "type": "uint8", "options": ""}, {"name": "msg", "type": "string", "options": "withLength16", "condition": "state===0"}]}, "RedeemCode": {"name": "RedeemCode", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [186], "isFeature": [151]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "kind", "type": "uint8", "options": ""}]}, "RedeemGift": {"name": "RedeemGift", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 4, 2], "isFeature": [152]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "coin", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}, "RedeemProperty": {"name": "RedeemProperty", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 187, 12], "isFeature": [151]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "_itemsLength", "type": "uint8", "options": ""}, {"name": "_items", "type": "array", "options": "count={_itemsLength}", "subFields": [{"name": "id", "type": "uint8", "options": ""}, {"name": "count", "type": "uint8", "options": ""}]}]}, "BindPhone": {"name": "BindPhone", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 14, 1], "isFeature": [31]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "isSuccess", "type": "uint8", "options": "transform=value==1"}, {"name": "message", "type": "string", "options": "withLength16", "condition": "isSuccess===false"}, {"name": "phoneNumber", "type": "string", "options": "withLength16", "condition": "isSuccess===true"}]}, "BindPhoneVerifiedMessage": {"name": "BindPhoneVerifiedMessage", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 14, 2], "isFeature": [110]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "isSuccess", "type": "uint8", "options": "transform=value==1"}, {"name": "message", "type": "string", "options": "withLength16", "condition": "isSuccess===false"}, {"name": "enableSafetyCode", "type": "uint8", "options": "transform=value==1", "condition": "isSuccess===true"}, {"name": "phoneNumber", "type": "string", "options": "withLength16", "condition": "isSuccess===true"}]}, "GetAchievement": {"name": "GetAchievement", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 15, 8], "isFeature": [46]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "username", "type": "string", "options": "withLength16"}, {"name": "achievements", "type": "array", "options": "while=reader.offset < packetSize", "subFields": [{"name": "gameNo", "type": "uint16", "options": ""}, {"name": "type", "type": "uint16", "options": ""}, {"name": "multiple", "type": "uint32", "options": "bigEndian"}, {"name": "score", "type": "uint32", "options": "bigEndian"}]}]}, "GetBetReport": {"name": "GetBetReport", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 15, 6], "isFeature": [45]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "username", "type": "string", "options": "withLength16"}, {"name": "_skipData", "type": "bytes", "options": "length=16"}, {"name": "daily", "type": "array", "options": "while=reader.offset < packetSize", "subFields": [{"name": "totalBet", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "totalWin", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "_currentDate", "type": "derived", "options": "transform=function(){var d=new Date();d.setDate(d.getDate()-items.length-1);return d}()"}, {"name": "date", "type": "derived", "options": "transform=new Date(_currentDate)"}]}]}, "GetHonor": {"name": "GetHonor", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 15, 5], "isFeature": [41]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "online", "type": "uint8", "options": "transform=value==1"}, {"name": "username", "type": "string", "options": "withLength16", "condition": "online===true"}, {"name": "value", "type": "<PERSON><PERSON>", "options": "littleEndian", "condition": "online===true"}]}, "GiftBagList": {"name": "GiftBagList", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 19, 1], "isFeature": [221]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "_itemCount", "type": "uint16", "options": ""}, {"name": "list", "type": "array", "options": "count={_itemCount}", "subFields": [{"name": "id", "type": "string", "options": "byteCount"}, {"name": "amount", "type": "float64", "options": "littleEndian"}, {"name": "maxBuyLimit", "type": "uint16", "options": "bigEndian"}, {"name": "userBuyCount", "type": "uint16", "options": "bigEndian"}, {"name": "_rawDate", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "date", "type": "derived", "options": "transform=function(){var unixTime=_rawDate/10000-11644473600000;return new Date(unixTime).toISOString()}()"}, {"name": "serialNumber", "type": "uint8", "options": ""}]}, {"name": "remainingBalance", "type": "long", "options": "littleEndian"}], "postProcess": [{"field": "list", "expression": "list.sort((a, b) => { return a.serialNumber - b.serialNumber })"}]}, "CheckGiftBag": {"name": "CheckGiftBag", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 19, 2], "isFeature": [222]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "success", "type": "uint8", "options": "transform=value==1"}, {"name": "errorCode", "type": "uint8", "options": "", "condition": "success===false"}, {"name": "orderErrorCode", "type": "uint8", "options": "", "condition": "success===false && !reader.isEOF()"}]}, "AdGiftBag": {"name": "AdGiftBag", "identify": {"protocolId": 30, "serviceId": 60903, "aliases": [60000, 60901, 60902, 60904], "typeArray": [0, 187, 8], "isFeature": [223]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "adName", "type": "string", "options": "withLength16"}, {"name": "gameID", "type": "uint16", "options": "bigEndian"}, {"name": "action", "type": "uint8", "options": ""}, {"name": "url", "type": "derived", "options": "transform=''", "condition": "reader.isEOF()"}, {"name": "url", "type": "string", "options": "withLength16", "condition": "!reader.isEOF()"}]}, "AddCharacter": {"name": "AddCharacter", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [250, 134], "isFeature": [91]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "photo", "type": "uint8", "options": ""}, {"name": "online", "type": "uint8", "options": "transform=value==1"}, {"name": "level", "type": "uint24", "options": "bigEndian"}, {"name": "username", "type": "string", "options": "withLength16"}]}, "AllowList": {"name": "AllowList", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [250, 150], "isFeature": [12]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "_deviceCount", "type": "uint16", "options": ""}, {"name": "deviceList", "type": "array", "options": "count={_deviceCount}", "subFields": [{"name": "id", "type": "string", "options": "withLength16"}, {"name": "name", "type": "string", "options": "withLength16"}]}]}, "Announcement": {"name": "Announcement", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 25]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "message", "type": "string", "options": "remaining"}]}, "BanSpeaking": {"name": "BanSpeaking", "identify": {"protocolId": 30, "serviceId": 60902, "aliases": [60000, 60901, 60903], "typeArray": [0, 103], "isFeature": [130]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "expiryDate", "type": "string", "options": "remaining"}]}, "BanSpeakingGuild": {"name": "BanSpeakingGuild", "identify": {"protocolId": 30, "serviceId": 60904, "typeArray": [253], "isFeature": [130]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=1"}, {"name": "_skipData", "type": "bytes", "options": "length=11"}, {"name": "content", "type": "string", "options": "withLength16"}]}, "CharacterList": {"name": "CharacterList", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [250, 133], "isFeature": [10, 30]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "accountType", "type": "uint8", "options": ""}, {"name": "hasPassword", "type": "uint8", "options": "transform=value==1"}, {"name": "timeused", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "referral", "type": "string", "options": "withLength16"}, {"name": "charsCount", "type": "uint8", "options": ""}, {"name": "chars", "type": "array", "options": "count={charsCount}", "subFields": [{"name": "photo", "type": "uint8", "options": ""}, {"name": "online", "type": "uint8", "options": ""}, {"name": "level", "type": "uint24", "options": "bigEndian"}, {"name": "username", "type": "string", "options": "withLength16"}]}]}, "ChatMessage": {"name": "ChatMessage", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [999, 999], "isFeature": [52, 53]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "messageType", "type": "uint8", "options": ""}, {"name": "red", "type": "uint8", "options": ""}, {"name": "green", "type": "uint8", "options": ""}, {"name": "blue", "type": "uint8", "options": ""}, {"name": "username", "type": "string", "options": "withLength16"}, {"name": "rank", "type": "uint8", "options": ""}, {"name": "code", "type": "uint16", "options": "bigEndian"}, {"name": "message", "type": "string", "options": "remaining"}]}, "CreatUserFail": {"name": "CreatUserFail", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [240, 22], "isFeature": [91]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "accountType", "type": "uint8", "options": ""}, {"name": "_skipData", "type": "bytes", "options": "length=1"}, {"name": "message", "type": "string", "options": "remaining"}]}, "CustomerServiceMessage": {"name": "CustomerServiceMessage", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 29], "isFeature": [140], "skipTypeZero": false}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "_colorR", "type": "uint8", "options": ""}, {"name": "_colorG", "type": "uint8", "options": ""}, {"name": "_colorB", "type": "uint8", "options": ""}, {"name": "message", "type": "string", "options": "remaining"}], "postProcess": [{"field": "color", "expression": "{r:_colorR,g:_colorG,b:_colorB}"}]}, "EnabledGameList": {"name": "EnabledGameList", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [0, 100]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "_gameCount", "type": "uint24", "options": ""}, {"name": "gameIds", "type": "value_array", "options": "type=uint16,count={_gameCount}"}], "postProcess": [{"field": "gameIds", "expression": "gameIds.sort((a, b) => { return a - b })"}]}, "GetGameLink": {"name": "GetGameLink", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [107, 0], "isFeature": [51]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "url", "type": "string", "options": "withLength16"}]}, "HealthyCheck": {"name": "HealthyCheck", "identify": {"protocolId": 30, "serviceId": 60902, "aliases": [60000, 60901, 60891, 60903], "typeArray": [0, 99]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "_year", "type": "uint8", "options": ""}, {"name": "_month", "type": "uint8", "options": ""}, {"name": "_day", "type": "uint8", "options": ""}, {"name": "_hour", "type": "uint8", "options": ""}, {"name": "_minute", "type": "uint8", "options": ""}, {"name": "_second", "type": "uint8", "options": ""}, {"name": "_fullYear", "type": "derived", "options": "transform=_year+1911"}, {"name": "serverTime", "type": "derived", "options": "transform=function(){var d=new Date();d.setFullYear(_fullYear);d.setMonth(_month-1);d.setDate(_day);d.setHours(_hour);d.setMinutes(_minute);d.setSeconds(_second);d.setMilliseconds(0);return d}()"}]}, "LoginFailed": {"name": "LoginFailed", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [250, 22], "isFeature": [10, 30]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}]}, "LoginRecord": {"name": "LoginRecord", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [250, 151], "isFeature": [13]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "_count", "type": "uint16", "options": ""}, {"name": "records", "type": "array", "options": "count={_count}", "subFields": [{"name": "_rawData", "type": "string", "options": "withLength16"}, {"name": "_parts", "type": "derived", "options": "transform=_rawData.split('\\t')"}, {"name": "date", "type": "derived", "options": "transform=_parts[0]"}, {"name": "ip", "type": "derived", "options": "transform=_parts[1]"}, {"name": "device", "type": "derived", "options": "transform=_parts[2]"}]}]}, "LoginSucceed": {"name": "LoginSucceed", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [0, 1], "isFeature": [11]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "value", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}, "NeedToAddCharacter": {"name": "NeedToAddCharacter", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [250, 132], "isFeature": [10]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "accountType", "type": "uint8", "options": ""}, {"name": "providerUserId", "type": "string", "options": "withLength16"}, {"name": "_asciiString", "type": "string", "options": "withLength16", "condition": "accountType === 2"}, {"name": "providerUsername", "type": "derived", "options": "transform=function(value) { return JSON.parse('\"' + _asciiString + '\"'); }()", "condition": "accountType === 2"}]}, "OtpMessage": {"name": "OtpMessage", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [250, 136], "isFeature": [10, 30]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "title", "type": "string", "options": "withLength16"}, {"name": "message", "type": "string", "options": "withLength16"}, {"name": "_skip", "type": "bytes", "options": "length=2"}, {"name": "_otherType", "type": "uint8", "options": ""}, {"name": "_otherImput", "type": "uint8", "options": ""}, {"name": "_otherSmsType", "type": "uint8", "options": ""}, {"name": "_otherIsSuccessed", "type": "uint8", "options": "", "condition": "_otherSmsType === 2"}, {"name": "_otherToken", "type": "string", "options": "withLength16", "condition": "_otherSmsType === 2 && _otherIsSuccessed === 1"}], "postProcess": [{"field": "other", "expression": "function() { var o = { type: _otherType, imput: _otherImput, smsType: _otherSmsType, isSuccessed: false, token: null }; if (_otherSmsType === 2) { o.isSuccessed = _otherIsSuccessed === 1; if (_otherIsSuccessed === 1 && _otherToken) { o.token = _otherToken; } } return o; }()"}]}, "PaymentUrlForVip": {"name": "PaymentUrlForVip", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 15, 21], "isFeature": [83]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "isSuccess", "type": "uint8", "options": "transform=value==1"}, {"name": "discount", "type": "uint8", "options": "", "condition": "isSuccess===true"}, {"name": "url", "type": "string", "options": "withLength16", "condition": "isSuccess===true"}, {"name": "message", "type": "string", "options": "remaining", "condition": "isSuccess===false"}]}, "PaymentUrl": {"name": "PaymentUrl", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 15, 20], "isFeature": [83]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "isSuccess", "type": "uint8", "options": "transform=value==1"}, {"name": "url", "type": "string", "options": "withLength16", "condition": "isSuccess===true"}, {"name": "message", "type": "string", "options": "remaining", "condition": "isSuccess===false"}]}, "Ranking": {"name": "Ranking", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 15, 10]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "rankType", "type": "uint8", "options": ""}, {"name": "isEmpty", "type": "uint8", "options": "transform=value!=1"}, {"name": "_count", "type": "derived", "options": "transform=0"}, {"name": "list", "type": "array", "options": "while=reader.offset < packetSize", "subFields": [{"name": "position", "type": "derived", "options": "transform=_count++"}, {"name": "name", "type": "string", "options": "withLength16"}, {"name": "winLose", "type": "long", "options": "littleEndian"}, {"name": "win", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "bet", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}]}, "SetPassword": {"name": "SetPassword", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [250, 139], "isFeature": [30]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}]}, "SystemMessage": {"name": "SystemMessage", "identify": {"protocolId": 30, "serviceId": 60000, "aliases": [60002, 60891, 60901, 60902, 60903], "typeArray": [0, 253], "typeArrayAliases": [[0, 102]], "isFeature": [51, 52, 53, 54, 87, 102, 151], "skipTypeZero": false}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "code", "type": "uint16", "options": ""}, {"name": "message", "type": "string", "options": "remaining"}]}, "UpdateGameId": {"name": "UpdateGameId", "identify": {"protocolId": 30, "serviceId": 60902, "aliases": [60000, 60901, 60903], "typeArray": [0, 4, 7]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "gameId", "type": "uint16", "options": "bigEndian"}]}, "UpdateGameState": {"name": "UpdateGameState", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [0, 999]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "isEnable", "type": "uint8", "options": "transform=value==1"}, {"name": "gameId", "type": "uint8", "options": ""}]}, "UpdateLevel": {"name": "UpdateLevel", "identify": {"protocolId": 30, "serviceId": 60902, "aliases": [60000, 60901, 60903, 60891], "typeArray": [0, 4, 4], "isFeature": [42]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "level", "type": "uint32", "options": "bigEndian"}]}, "UpdateMoney": {"name": "Update<PERSON>oney", "identify": {"protocolId": 30, "serviceId": 60902, "aliases": [60000, 60901, 60903, 60891], "typeArray": [0, 4, 5], "isFeature": [43], "skipTypeZero": false}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "money", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "silver", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}, "UpdateUserCoinItem": {"name": "UpdateUserCoinItem", "identify": {"protocolId": 30, "serviceId": 60903, "aliases": [60000, 60901, 60902, 60891, 60904], "typeArray": [0, 48, 3], "isFeature": [77]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "id", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "count", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}, "UpdateUserStatusItem": {"name": "UpdateUserStatusItem", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 4, 41], "isFeature": [50]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "id", "type": "uint16", "options": "bigEndian"}, {"name": "count", "type": "uint16", "options": "bigEndian"}]}, "InitStatusInventory": {"name": "InitStatusInventory", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [0, 4, 99], "isFeature": [48], "skipTypeZero": false}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "items", "type": "array", "options": "while=!reader.isEOF()", "subFields": [{"name": "id", "type": "uint16", "options": "bigEndian"}, {"name": "count", "type": "uint24", "options": "bigEndian"}]}]}, "getGameInventory": {"name": "getGameInventory", "identify": {"protocolId": 30, "serviceId": 60000, "aliases": [60901, 60902, 60903, 60904, 60905, 60891], "typeArray": [0, 4, 100], "isFeature": [49], "skipTypeZero": false}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "_count", "type": "uint16", "options": "bigEndian"}, {"name": "items", "type": "array", "options": "count={_count}", "subFields": [{"name": "date", "type": "<PERSON><PERSON>", "options": "littleEndian,transform=function(){return new Date(value).toISOString()}()"}, {"name": "id", "type": "uint16", "options": "bigEndian"}, {"name": "cardType", "type": "uint8", "options": ""}, {"name": "coinType", "type": "uint8", "options": ""}, {"name": "betAmount", "type": "uint24", "options": "bigEndian"}]}]}, "UpdateCopper": {"name": "UpdateCopper", "identify": {"protocolId": 30, "serviceId": 60902, "aliases": [60000, 60901, 60903, 60891], "typeArray": [0, 4, 9]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "copper", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}, "UpdateReferralMoney": {"name": "UpdateReferralMoney", "identify": {"protocolId": 30, "serviceId": 60902, "aliases": [60000, 60901, 60903], "typeArray": [0, 4, 8], "isFeature": [14]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "referral<PERSON>oney", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}, "UpdateVip": {"name": "UpdateVip", "identify": {"protocolId": 30, "serviceId": 60902, "aliases": [60901, 60903, 60891], "typeArray": [0, 98], "isFeature": [44], "skipTypeZero": false}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "_parameters", "type": "array", "options": "while=!reader.isEOF()", "subFields": [{"name": "id", "type": "uint16", "options": ""}, {"name": "value", "type": "string", "options": "withLength16"}]}], "postProcess": [{"field": "level", "expression": "_parameters.find(p => p.id === 1)?.value", "condition": "_parameters.some(p => p.id === 1)"}, {"field": "money", "expression": "_parameters.find(p => p.id === 2)?.value", "condition": "_parameters.some(p => p.id === 2)"}, {"field": "silver", "expression": "_parameters.find(p => p.id === 3)?.value", "condition": "_parameters.some(p => p.id === 3)"}, {"field": "referral<PERSON><PERSON>ny", "expression": "_parameters.find(p => p.id === 4)?.value", "condition": "_parameters.some(p => p.id === 4)"}, {"field": "safed", "expression": "_parameters.find(p => p.id === 5)?.value", "condition": "_parameters.some(p => p.id === 5)"}, {"field": "point", "expression": "_parameters.find(p => p.id === 6)?.value", "condition": "_parameters.some(p => p.id === 6)"}, {"field": "activeValue", "expression": "parseInt(_parameters.find(p => p.id === 7)?.value)", "condition": "_parameters.some(p => p.id === 7)"}, {"field": "honor", "expression": "_parameters.find(p => p.id === 8)?.value", "condition": "_parameters.some(p => p.id === 8)"}, {"field": "vip", "expression": "parseInt(_parameters.find(p => p.id === 9)?.value)", "condition": "_parameters.some(p => p.id === 9)"}, {"field": "pkValue", "expression": "_parameters.find(p => p.id === 10)?.value", "condition": "_parameters.some(p => p.id === 10)"}]}, "UserDetail": {"name": "UserDetail", "identify": {"protocolId": 30, "serviceId": 60903, "typeArray": [0, 15, 15], "isFeature": [40]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "username", "type": "string", "options": "withLength16"}, {"name": "online", "type": "uint8", "options": "transform=value==1"}, {"name": "accountType", "type": "derived", "options": "transform=0"}, {"name": "accountType", "type": "uint8", "options": "", "condition": "online===true"}, {"name": "rank", "type": "derived", "options": "transform=0"}, {"name": "rank", "type": "uint8", "options": "", "condition": "online===true"}, {"name": "level", "type": "derived", "options": "transform=0"}, {"name": "level", "type": "uint32", "options": "bigEndian", "condition": "online===true"}, {"name": "money", "type": "derived", "options": "transform=0"}, {"name": "money", "type": "<PERSON><PERSON>", "options": "littleEndian", "condition": "online===true"}, {"name": "silver", "type": "derived", "options": "transform=0"}, {"name": "silver", "type": "<PERSON><PERSON>", "options": "littleEndian", "condition": "online===true"}, {"name": "gameId", "type": "derived", "options": "transform=0"}, {"name": "gameId", "type": "uint16", "options": "bigEndian", "condition": "online===true"}, {"name": "platformId", "type": "derived", "options": "transform=0"}, {"name": "platformId", "type": "uint8", "options": "", "condition": "online===true"}, {"name": "levelVip", "type": "derived", "options": "transform=0"}, {"name": "levelVip", "type": "uint8", "options": "", "condition": "online===true"}, {"name": "isLevelUpVipFancyDiamondEnabled", "type": "derived", "options": "transform=false"}, {"name": "isLevelUpVipFancyDiamondEnabled", "type": "uint8", "options": "transform=value==1", "condition": "online===true"}, {"name": "discount", "type": "derived", "options": "transform=0"}, {"name": "discount", "type": "uint8", "options": "", "condition": "online===true"}, {"name": "limit", "type": "derived", "options": "transform=0"}, {"name": "limit", "type": "uint32", "options": "bigEndian", "condition": "online===true"}, {"name": "activeValue", "type": "derived", "options": "transform=0"}, {"name": "activeValue", "type": "uint32", "options": "bigEndian", "condition": "online===true"}, {"name": "honor", "type": "derived", "options": "transform=0"}, {"name": "honor", "type": "uint32", "options": "bigEndian", "condition": "online===true"}, {"name": "isBind", "type": "derived", "options": "transform=false"}, {"name": "isBind", "type": "uint8", "options": "transform=value==1", "condition": "online===true"}, {"name": "isEnableSafetyCode", "type": "derived", "options": "transform=false"}, {"name": "isEnableSafetyCode", "type": "uint8", "options": "transform=value==1", "condition": "online===true && isBind===true"}, {"name": "phoneNumber", "type": "string", "options": "withLength16", "condition": "online===true && isBind===true"}, {"name": "phoneNumber", "type": "derived", "options": "transform=''", "condition": "online===false || isBind===false"}]}, "UserInfo": {"name": "UserInfo", "identify": {"protocolId": 30, "serviceId": 60902, "aliases": [60000, 60901, 60903], "typeArray": [0, 4, 1], "isFeature": [10, 14], "skipTypeZero": false}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "_usernameSize", "type": "uint8", "options": ""}, {"name": "_rawUsername", "type": "string", "options": "utf16,length={_usernameSize*2}"}, {"name": "_rawRank", "type": "uint8", "options": ""}, {"name": "_isHighRank", "type": "derived", "options": "transform=_rawRank>=100"}, {"name": "rank", "type": "derived", "options": "transform=_isHighRank?_rawRank-100:_rawRank"}, {"name": "_usernameParts", "type": "derived", "options": "transform=_rawUsername.split('\\t')"}, {"name": "username", "type": "derived", "options": "transform=_isHighRank?_usernameParts[0]:_rawUsername"}, {"name": "facebookId", "type": "derived", "options": "transform=_isHighRank&&_usernameParts.length>1?_usernameParts[1].substring(2):''"}, {"name": "level", "type": "uint32", "options": "bigEndian"}, {"name": "money", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "silver", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "referral<PERSON><PERSON>ny", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "referral", "type": "string", "options": "withLength16"}, {"name": "phoneNumber", "type": "string", "options": "withLength16"}, {"name": "lastLogoutTime", "type": "string", "options": "withLength16"}, {"name": "password", "type": "uint8", "options": ""}, {"name": "liveMaster", "type": "uint8", "options": ""}, {"name": "levelVip", "type": "uint8", "options": ""}, {"name": "loginType", "type": "uint8", "options": ""}, {"name": "coinCount", "type": "uint16", "options": "bigEndian"}, {"name": "coins", "type": "array", "options": "count={coinCount+1}", "subFields": [{"name": "id", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "balance", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}]}, "UserKey": {"name": "User<PERSON>ey", "identify": {"protocolId": 30, "serviceId": 60891, "aliases": [60902], "typeArray": [0, 116], "isFeature": [54]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "key", "type": "string", "options": "withLength16"}, {"name": "userAlias", "type": "string", "options": "withLength16"}]}, "LoginVerifiedMessage": {"name": "LoginVerifiedMessage", "identify": {"protocolId": 30, "serviceId": 60000, "typeArray": [250, 135], "isFeature": [10, 20, 30, 91, 70]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=2"}, {"name": "title", "type": "string", "options": "withLength16"}, {"name": "message", "type": "string", "options": "withLength16"}, {"name": "action", "type": "uint8", "options": ""}]}, "Withdraw": {"name": "Withdraw", "identify": {"protocolId": 30, "serviceId": 60902, "typeArray": [107, 4], "isFeature": [52]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "serial", "type": "uint16", "options": "bigEndian"}, {"name": "serviceType", "type": "value_array", "options": "count=3"}, {"name": "providerId", "type": "uint32", "options": "bigEndian"}, {"name": "amount", "type": "<PERSON><PERSON>", "options": "littleEndian"}, {"name": "balance", "type": "<PERSON><PERSON>", "options": "littleEndian"}]}, "Alive": {"name": "Alive", "identify": {"protocolId": 255, "serviceId": 255, "typeArray": [0, 0], "ignoreServiceId": true, "ignoreTypeArray": true}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "type", "type": "value_array", "options": "count=2"}]}, "ConnectionSuccessed": {"name": "ConnectionSuccessed", "identify": {"protocolId": 112, "serviceId": 0, "typeArray": [0, 0], "ignoreServiceId": true, "ignoreTypeArray": true, "isFeature": [1]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "clientId", "type": "uint32", "options": "bigEndian"}, {"name": "token", "type": "uint32", "options": "bigEndian"}]}, "ReconnectionSuccessed": {"name": "ReconnectionSuccessed", "identify": {"protocolId": 113, "serviceId": 0, "typeArray": [0, 0], "ignoreServiceId": true, "ignoreTypeArray": true}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "clientId", "type": "uint32", "options": "bigEndian", "condition": "reader.EOFCount() >= 4"}, {"name": "token", "type": "uint32", "options": "bigEndian", "condition": "reader.EOFCount() >= 4"}]}, "RejoinedService": {"name": "RejoinedService", "identify": {"protocolId": 34, "serviceId": 0, "typeArray": [0, 0], "ignoreServiceId": true, "ignoreTypeArray": true}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}, {"name": "sendId", "type": "uint16", "options": "bigEndian"}]}, "Echo": {"name": "Echo", "identify": {"protocolId": 38, "serviceId": 0, "typeArray": [0, 0], "ignoreServiceId": true, "ignoreTypeArray": true}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "_timestamp", "type": "string", "options": "withLength16"}], "postProcess": [{"field": "latency", "expression": "function() { var now = new Date().getTime(); return now - parseInt(_timestamp); }()"}]}, "Logout": {"name": "Logout", "identify": {"protocolId": 33, "serviceId": 0, "typeArray": [0, 0], "ignoreServiceId": true, "ignoreTypeArray": true, "isFeature": [20]}, "fields": [{"name": "packetSize", "type": "uint24", "options": "bigEndian"}, {"name": "protocolId", "type": "uint8", "options": ""}, {"name": "serviceId", "type": "uint16", "options": "bigEndian"}]}}}
import encrypt from '~/utils/encrypt'
const STATION = process.env.STATION
const NUXT_ENV = process.env.NUXT_ENV
const loadConfig = require(`~/station/${STATION}/${NUXT_ENV}.js`).default

export default class Leaderboard {
  constructor(axios) {
    this.axios = axios
  }
  // 日榜: 查詢7天內的資料, 週榜: 查詢4週內的資料
  // type(0: 日榜, 1:周榜) 龍榜win 虎榜odds
  async getWinRanking({ type, beginAt, endAt, limit, offset }) {
    const params = {
      type,
      beginAt,
      endAt,
      limit,
      offset,
      nocache: 1
    }
    return await this.axios.get('/api/client/rank/win/list', { params })
  }

  async getOddsRanking({ type, beginAt, endAt, limit, offset }) {
    const params = {
      type,
      beginAt,
      endAt,
      limit,
      offset,
      nocache: 1
    }
    return await this.axios.get('/api/client/rank/odds/list', { params })
  }

  //個人排名數據
  async getWinUserRanking({ type, username, beginAt, endAt }) {
    const params = {
      type,
      username,
      beginAt,
      endAt,
      nocache: 1
    }
    return await this.axios.get('/api/client/rank/win/user', { params })
  }

  async getOddsUserRanking({ type, username, beginAt, endAt }) {
    const params = {
      type,
      username,
      beginAt,
      endAt,
      nocache: 1
    }
    return await this.axios.get('/api/client/rank/odds/user', { params })
  }

  //排行榜 gamelist 需要完整呈現blocked
  async leaderboardGameList(headerData, gameList, lang, client_id) {
    const encryptionKey = encrypt.getEncryptionKey({
      clientId: loadConfig.client_id,
      sercetKey: loadConfig.secret_key
    })
    const token = await encrypt.encryptAndSend({
      alias: headerData.alias,
      username: headerData.username,
      encryptionKey
    })
    const head = {
      'X-Secure-Token': token
    }
    const body = {
      client_id: client_id,
      game_ids: gameList,
      lang: lang
    }

    return this.axios.$get('/api/client/game/list', {
      headers: head,
      params: body,
      timeout: 2000
    })
  }
}

export default {
  messages: {
    nickname: (field) => `${field} must consist of 2 to 10 characters or underscores.`,
    phone_number: () => `Invalid format.`,
    date_before: (field, [target]) => `${field} must be before or equal to ${target}.`,
    date_after: (field, [target]) => `${field} must be after or equal to ${target}.`,
    date_not_same: (field, [target]) => `${field} cannot be the same as ${target}.`,
    html_limit: (field) => `${field} The number of characters has exceeded the maximum limit.`,
    html: (field) =>
      `${field} contains invalid characters. Use "Paste as plain text" from the right-click when copying text to avoid invalid characters.`,
    sns: (field) => `${field} contains invalid characters.`,
    no_zero_start: (field) => `${field} cannot start with "0".`,
    email: (field) => `Incomplete ${field} input.`,
    multiple_line: (field) => `${field} is invalid.`,
    max_line: (field, [num]) => `${field} can send up to ${num} sets of numbers at a time.`,
    repeat_line: (field) => `${field} contains duplicate phone numbers.`,
    coin_min: (field, [num]) => `Quantity must be at least ${num}`,
    coin_min_send_mail: () => `The amount of Star Coins must be at least 10,000.`,
    insufficient_quota: () => 'Insufficient Star Coins.',
    insufficient_points: () => 'Cannot exceed the current points.',
    card_number_limit: () => '8-16 alphanumeric characters (uppercase letters).',
    number21_limit: () => '21 alphanumeric characters (uppercase letters).',
    validate_length: (field, [num]) => `The verification code must be ${num} digits long.`,
    required: () => 'Required.',
    special_character: () => 'Special characters are not allowed.',
    validate_char_length: () => 'Exceeds the character limit.',
    validate_string_length: () => 'Exceeds the character limit.',
    allow_blank_required: () => 'Required.',
    transfer_min: (field, [num]) => `Transfer must be at least ${num} Star Coins.`,
    min: (field, [num]) => `Must be at least ${num} characters.`,
    max: (field, [num]) => `Maximum of ${num} characters.`,
    is_number: () => 'Must be a positive integer.',
    is_number_card: () => 'Card number must be numeric.',
    insufficient_points_vip: () => 'Cannot exceed the purchase limit.',
    insufficient_store_points: () => 'Cannot exceed the Star Coin balance.',
    insufficient_withdraw_points: () => 'Cannot exceed the stored Star Coins.',
    redeem_code_limit: () => '1-32 alphanumeric characters.',
    number4_limit: () => 'Please fill in the last 4 digits of your ID or passport.',
    is_last_name: () => 'Please enter the correct last name.',
    age_over_limit: () => 'Sorry, you are too young to access the content of this website!',
    is_correct_date_of_birth: () => 'Please enter the correct date of birth.',
    send_mail_limit_coin: (field, [num]) =>
      `Today's remaining transaction amount: ${num.toLocaleString()}`,
    guild_name_limit: () => 'Must be 2-8 Chinese characters or 2-17 alphanumeric characters.',
    guild_duplicate_name: () => 'Name taken by another Club.',
    name_validate: () => 'Special characters and symbols are not allowed.',
    text_fullwidth: () => 'Special characters and symbols are not allowed.',
    inappropriate_words: () => 'Content contains sensitive words or violates guidelines.',
    only_english_allowed: () => 'Please enter 1-12 alphanumeric characters.'
  }
}

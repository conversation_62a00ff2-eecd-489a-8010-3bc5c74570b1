<template>
  <v-container fluid :class="[containerPx]">
    <v-row
      v-for="msg in msgAry"
      :key="msg.id"
      :id="`msg-row${msg.id}`"
      no-gutters
      :class="[
        'msg-row',
        'py-2',
        'flex-nowrap',
        { 'flex-row-reverse': isMe(msg.user.name) },
        { 'pt-0': msg.messageType === -1 }
      ]"
    >
      <template v-if="msg.messageType === 1">
        <pre
          class="text-body-2 custom-text-noto"
          :style="{
            color: getTextColor(msg.msg.color.red, msg.msg.color.green, msg.msg.color.blue),
            'text-wrap': 'wrap',
            'white-space': 'pre-wrap'
          }"
          v-text="setupCustomMsg(msg.msg.message)"
        />
      </template>
      <template v-else-if="msg.messageType === -1">
        <div class="decorate-line"></div>
        <span class="material-symbols-outlined default-content--text mx-2">{{ msg.icon }}</span>
        <div class="d-flex flex-column" v-html="msg.msg"></div>
      </template>
      <template v-else-if="isItTextPhotoSoundAndSticker(msg.msg.type)">
        <!-- official -->
        <template v-if="isOfficial(msg.user.name)">
          <div v-if="msg.img !== null" class="mr-2">
            <v-img width="40" height="40" :src="msg.img">
              <template v-slot:placeholder> <imgPlaceholder width="40" height="40" /></template>
            </v-img>
          </div>
          <div class="d-flex flex-column">
            <span
              class="user-name pb-1"
              :style="{
                color: getTextColor(msg.user.color.red, msg.user.color.green, msg.user.color.blue)
              }"
            >
              {{ convertMessage(msg.user.name) }}
            </span>
            <v-row no-gutters align="end" class="flex-nowrap pr-5">
              <messageType
                :msg="msg"
                :msg-key="msgKey"
                :countdown="countdown"
                :count-progress="countProgress"
                @showImgDialog="showImgDialog"
                @playAudioEvent="playAudioEvent(msg)"
              />
              <span
                class="text-caption default-content--text custom-text-noto text-no-wrap ml-1 text-regular--text"
              >
                {{ getChineseDateTime(msg.date) }}
              </span>
            </v-row>
          </div>
        </template>
        <!-- customer service -->
        <template v-else-if="isCustomerService(msg.user.name)">
          <div v-if="msg.img !== null" class="mr-2">
            <v-img width="40" height="40" :src="msg.img" @click="setPlayerInfo(msg.user.name)">
              <template v-slot:placeholder> <imgPlaceholder width="40" height="40" /></template>
            </v-img>
          </div>
          <div class="d-flex flex-column">
            <span
              class="user-name pb-1"
              :style="{
                color: getTextColor(msg.user.color.red, msg.user.color.green, msg.user.color.blue)
              }"
              @click="setPlayerInfo(msg.user.name)"
            >
              {{ convertMessage(msg.user.name) }}
            </span>

            <v-row no-gutters align="end" class="flex-nowrap pr-5">
              <messageType
                :msg="msg"
                :msg-key="msgKey"
                :countdown="countdown"
                :count-progress="countProgress"
                @showImgDialog="showImgDialog"
                @playAudioEvent="playAudioEvent(msg)"
              />
              <span
                class="text-caption default-content--text custom-text-noto text-no-wrap ml-1 text-regular--text"
              >
                {{ getChineseDateTime(msg.date) }}
              </span>
            </v-row>
          </div>
        </template>
        <!-- other player -->
        <v-menu
          v-else-if="!isMe(msg.user.name)"
          right
          max-width="100%"
          content-class="easyPlayer-custom-border"
        >
          <template v-slot:activator="{ on, attrs }">
            <div v-if="msg.img !== null" class="mr-2" v-bind="attrs" v-on="on">
              <v-img width="40" height="40" :src="msg.img" @click="setPlayerInfo(msg.user.name)">
                <template v-slot:placeholder> <imgPlaceholder width="40" height="40" /></template>
              </v-img>
            </div>
            <div class="d-flex flex-column w-100">
              <span
                class="user-name pb-1"
                :style="{
                  color: getTextColor(msg.user.color.red, msg.user.color.green, msg.user.color.blue)
                }"
                @click="setPlayerInfo(msg.user.name)"
                v-bind="attrs"
                v-on="on"
              >
                {{ convertMessage(msg.user.name) }}
              </span>

              <v-row no-gutters align="end" class="flex-nowrap pr-5">
                <messageType
                  :msg="msg"
                  :msg-key="msgKey"
                  :countdown="countdown"
                  :count-progress="countProgress"
                  @showImgDialog="showImgDialog"
                  @playAudioEvent="playAudioEvent(msg)"
                />
                <span
                  class="text-caption default-content--text custom-text-noto text-no-wrap ml-1 text-regular--text"
                >
                  {{ getChineseDateTime(msg.date) }}
                </span>
              </v-row>
            </div>
          </template>
          <easyPlayerInfo
            tile
            report
            is-card
            action-bar
            only-coin
            :player-info="playerInfo"
            style="min-width: 300px"
            badge-type="relation"
          />
        </v-menu>

        <!-- self -->
        <template v-else>
          <div v-if="msg.img !== null" class="ml-2">
            <v-img
              width="40"
              height="40"
              :src="thumbUrl"
              class="cursor-pointer"
              @click="showPlayerInfoCardDialogStatus(msg.user.name)"
            >
              <template v-slot:placeholder> <imgPlaceholder width="40" height="40" /></template>
            </v-img>
          </div>
          <div class="d-flex flex-column align-end">
            <span
              class="cursor-pointer user-name pb-1"
              :style="{
                color: getTextColor(msg.user.color.red, msg.user.color.green, msg.user.color.blue)
              }"
              @click="showPlayerInfoCardDialogStatus(msg.user.name)"
            >
              {{ convertMessage(msg.user.name) }}
            </span>
            <v-row no-gutters align="end" class="flex-row-reverse flex-nowrap pl-5">
              <messageType
                :msg="msg"
                :msg-key="msgKey"
                :countdown="countdown"
                :count-progress="countProgress"
                @showImgDialog="showImgDialog"
                @playAudioEvent="playAudioEvent(msg)"
              />

              <span
                class="text-caption default-content--text custom-text-noto text-no-wrap mr-1 text-regular--text"
              >
                {{ getChineseDateTime(msg.date) }}
              </span>
            </v-row>
          </div>
        </template>
      </template>
    </v-row>
    <!-- 盡量不要在msg-box 當中的 container新增其他element
      因為聊天室的主體會透過 row上面所帶的ID去判斷當前高度 來達到計算未讀通知
      如果需要增加記得在chatroom/index裡的updateReadNoty將新增的element給排除掉-->
  </v-container>
</template>

<script>
  import relationship from '~/mixins/relationship'
  import chat from '~/mixins/chatroom/chat'
  import converter from '~/mixins/converter'
  export default {
    name: 'msgBox',
    mixins: [relationship, chat, converter],
    props: {
      msgAry: {
        type: Array,
        default: () => []
      },
      msgKey: {
        type: String,
        default: ''
      },
      containerPx: {
        type: String,
        default: 'px-4'
      },
      //判斷是否為線上客服
      isCustomerServiceDialog: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        playerInfo: {
          username: '',
          level: 0,
          levelVip: 0,
          money: 0,
          thumbUrl: '',
          online: false,
          guildName: ''
        },

        audioContext: null,
        audioBufferSource: null,
        countdown: 0,
        countProgress: 0,
        currentPlayingId: null,
        countdownInterval: null,
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png'
      }
    },
    components: {
      easyPlayerInfo: () => import('~/components/player_info/easyPlayerInfo'),
      imgPlaceholder: () => import('~/components/imgPlaceholder'),
      messageType: () => import('~/components/chatroom/messageType')
    },
    computed: {
      ownName({ $store }) {
        return $store.getters['role/userName']
      },
      thumbUrl({ $store }) {
        return $store.getters['role/thumbUrl']
      }
    },
    mounted() {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)()
      this.audioBufferSource = this.audioContext.createBufferSource()
    },
    methods: {
      getTextColor(r, g, b) {
        return `rgb(${r},${g},${b})`
      },
      async setPlayerInfo(userName) {
        // 先獲取空數據，以免顯示上一個用戶資訊
        this.playerInfo = {
          username: '',
          level: 0,
          levelVip: 0,
          money: 0,
          thumbUrl: '',
          online: false,
          guildName: ''
        }
        if (!this.isCustomerService(userName)) {
          this.$nuxt.$loading.start()
          const role = await this.getPlayerData(userName)
          this.playerInfo = role
          this.updatePlayerInfo(role)
          this.$nuxt.$loading.finish()
        }
      },
      async updatePlayerInfo(userdata) {
        await this.$store.dispatch('social/setSingleFriendStatus', userdata)
      },
      setupCustomMsg(msg) {
        const message = this.convertMessage(msg)
        if (message === 'online_customer_service_noty') return this.$t(message)
        return message
      },
      isCustomerService(userName) {
        //const name = this.convertMessage(userName)
        const isMe = this.isMe(userName)
        return this.isCustomerServiceDialog && !isMe // name.startsWith('【線上客服】') || name.startsWith("'Online Customer Service'")
      },
      isMe(username) {
        return this.ownName === username
      },
      getChineseDateTime(dataTime) {
        const timeString = this.$UIConfig.msgBox.dateTimeText
        return this.$moment(dataTime)
          .format(timeString)
          .replace('AM', this.$t('AM'))
          .replace('PM', this.$t('PM'))
      },
      async showPlayerInfoCardDialogStatus(userName) {
        this.setSelectPlayerInfo(await this.getPlayerData(userName))
        this.$nuxt.$emit('root:showPlayerInfoCardDialogStatus', true)
      },
      errorStickerHandler(item) {
        this.$store.commit('chat/SET_CUSTOM_IMG', {
          key: this.msgKey,
          id: item.id,
          stickerId: 'chat_sticker_default'
        })
      },
      isItTextPhotoSoundAndSticker(type) {
        return type === 3 || type === 4 || type === 8
      },
      showImgDialog(url) {
        this.$emit('showImgDialog', url)
      },
      playAudioEvent(item) {
        //如果是不同的音訊，則暫停上一個
        if (this.currentPlayingId !== null && this.currentPlayingId !== item.id) {
          this.$store.commit('chat/SET_IS_PLAYING', {
            key: this.msgKey,
            id: this.currentPlayingId,
            isPlaying: false
          })
        }

        const msg = item.msg
        this.stopAudio()
        if (msg.isPlaying) {
          this.clearCountdownInterval()
        } else {
          this.currentPlayingId = item.id
          this.playAudio(msg.audioBuffer)
          this.startCountdown(msg.duration)
        }
        this.$store.commit('chat/SET_IS_PLAYING', {
          key: this.msgKey,
          id: item.id,
          isPlaying: !msg.isPlaying
        })
      },
      playAudio(audioBuffer) {
        this.$nuxt.$emit('chat:playAudio', audioBuffer)
      },
      stopAudio() {
        this.$nuxt.$emit('chat:stopAudio')
      },
      startCountdown(end) {
        if (this.countdownInterval !== null) clearInterval(this.countdownInterval)
        this.countdown = end
        this.countdownInterval = setInterval(() => {
          if (this.countdown > 0) {
            this.countdown = this.countdown - 0.1
            this.countProgress = ((end - this.countdown) / end) * 100
          } else {
            this.$store.commit('chat/SET_IS_PLAYING', {
              key: this.msgKey,
              id: this.currentPlayingId,
              isPlaying: false
            })
            this.clearCountdownInterval()
          }
        }, 100)
      },
      clearCountdownInterval() {
        this.countProgress = 0
        this.currentPlayingId = null
        clearInterval(this.countdownInterval) // 清除定时器
      }
    }
  }
</script>
<style lang="scss" scoped>
  $default-content: map-get($colors, 'default-content');
  .user-name {
    line-height: 1;
    width: fit-content;
  }
  .decorate-line {
    background-color: $default-content;
    width: 4px;
  }
</style>

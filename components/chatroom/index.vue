<template>
  <div>
    <v-dialog
      v-model="showChatRoomDialogStatusTemp"
      max-width="800"
      :fullscreen="breakpoint.xsOnly || (breakpoint.smAndDown && orientation !== 0)"
      persistent
      transition="dialog-transition"
      :content-class="
        breakpoint.xsOnly || (breakpoint.smAndDown && orientation !== 0)
          ? 'transparent chatroom-dialog'
          : 'transparent chatroom-dialog rounded-lg'
      "
    >
      <v-card
        :class="{
          'fill-height': breakpoint.xsOnly || (breakpoint.smOnly && orientation !== 0)
        }"
        :style="{ 'background-color': bgColor }"
      >
        <customDialogTitle
          :second-icon="cardTitle.second.icon"
          :second-icon-action="cardTitle.second.action"
          :noty="allChatNoty"
          :title="$t('chat_session').toUpperCase()"
          @closeDialog="closeDialog"
          :class="['chatroom-title', { 'notch-left': hasLeftNotch }]"
        />
        <v-card-text :class="['chatroom', 'pa-0']">
          <!-- channel list -->
          <v-row no-gutters class="fill-height">
            <v-col
              v-show="breakpoint.smAndUp || !showChatMobile"
              xl="5"
              lg="5"
              md="5"
              sm="5"
              cols="12"
              class="fill-height"
            >
              <div class="d-flex flex-column left-side fill-height">
                <!-- list & textfield -->
                <div class="d-flex flex-column left-side-content">
                  <!-- text-field -->
                  <div
                    :class="[
                      'left-side-content-textfield px-3 pt-4',
                      { 'notch-left': hasLeftNotch }
                    ]"
                    :style="{ 'background-color': bgColor }"
                  >
                    <v-text-field
                      v-model="chatListKeyword"
                      :label="$t('input_secret_nickname')"
                      class="input-height"
                      :hint="isNoTarget ? $t('use_magnifying_glass_for_further_search') : ''"
                      single-line
                      rounded
                      outlined
                      dense
                      maxlength="12"
                      @input="selectFirstChat"
                      @keydown.native="preventLongInput"
                    >
                      <template v-slot:append>
                        <span
                          v-show="chatListKeyword.length > 0"
                          class="material-symbols-outlined cursor-pointer mr-1"
                          @click="chatListKeyword = ''"
                        >
                          close
                        </span>
                        <span
                          class="material-symbols-outlined cursor-pointer"
                          @click="searchPlayer"
                        >
                          search
                        </span>
                      </template>
                    </v-text-field>
                  </div>
                  <!-- list -->
                  <div
                    :class="['left-side-content-list', { 'notch-left': hasLeftNotch }]"
                    :style="{ 'background-color': bgColor }"
                  >
                    <!-- chat list -->
                    <chatList
                      ref="chatList"
                      class="scrollable"
                      :keyword="chatListKeyword"
                      :chats="chats"
                      :show-chat-room-dialog-status="showChatRoomDialogStatus"
                      @selectedChatEvent="selectedChatEvent"
                      @mobileClickedEvent="mobileClickedEvent"
                    />
                  </div>
                </div>
              </div>
            </v-col>
            <!-- chat container -->
            <v-col
              v-show="breakpoint.smAndUp || showChatMobile"
              xl="7"
              lg="7"
              md="7"
              sm="7"
              cols="12"
              class="chat-container fill-height"
            >
              <div class="d-flex flex-column fill-height">
                <!-- title & action bar -->
                <div class="dialog-fill-2">
                  <!-- title -->
                  <msgTitle
                    ref="msgTitle"
                    :current-chat="currentChat"
                    :show-chat-mobile.sync="showChatMobile"
                  />
                  <!-- action btn -->
                  <v-row
                    v-if="isStranger"
                    no-gutters
                    justify="center"
                    align="center"
                    class="action-bar"
                  >
                    <v-btn
                      depressed
                      :color="$UIConfig.defaultBtnColor"
                      class="button-content--text mr-4"
                      :disabled="addFriendBtnDisableStatus"
                      @click="checkAddFriend(currentChat.title)"
                    >
                      <v-icon class="mr-2">mdi-account-plus </v-icon>
                      {{ $t('add_friend_action') }}
                    </v-btn>

                    <v-btn
                      depressed
                      :color="$UIConfig.defaultBtnColor"
                      class="button-content--text"
                      :disabled="addBlockBtnDisableStatus"
                      @click="checkAddBlockEvent(currentChat.title)"
                    >
                      <v-icon class="mr-2">mdi-account-cancel</v-icon>
                      {{ $t('add_to_blacklist') }}
                    </v-btn>
                  </v-row>
                  <!-- remind text -->
                  <v-row
                    v-if="isOfficial(currentChat.title)"
                    no-gutters
                    justify="center"
                    align="center"
                    class="remind-bar px-4 py-2"
                  >
                    <i18n
                      path="star_treasure_reminder"
                      tag="span"
                      class="default-content--text text-body-1 custom-text-noto"
                    >
                      <template v-slot:other>
                        <template v-if="!otherStarCityOnlinePlatformsReminderDisabled">
                          <span
                            class="primary--text text-decoration-underline cursor-pointer"
                            @click="goDownload"
                          >
                            {{ $t('other_star_city_online_platforms') }}
                          </span>
                        </template>
                        <template v-else>
                          {{ $t('other_star_city_online_platforms') }}
                        </template>
                      </template>
                    </i18n>
                  </v-row>
                </div>
                <!-- Title Noty -->
                <v-expand-transition>
                  <div v-show="showMiniGame && selectedChat === 0">
                    <v-row no-gutters>
                      <div
                        :style="{ 'background-color': bgColor }"
                        class="d-flex flex-nowrap px-4 py-2"
                      >
                        <div class="decorate-line"></div>
                        <span
                          class="material-symbols-outlined default-content--text cursor-pointer mx-2"
                          @click="openRedirectDialog"
                          >{{ 'campaign' }}</span
                        >
                        <div class="d-flex flex-column cursor-pointer" @click="openRedirectDialog">
                          <pre class="text-body-2 custom-text-noto default-content--text text-wrap"
                            >{{ $t(miniGameTitleAlert) }}
                          </pre>
                        </div>
                        <v-fade-transition>
                          <div
                            v-if="miniGame.gameType === 2 && !showMiniGameCanvas"
                            @click="setShowMiniGameCanvas(true)"
                          >
                            <DrawingCanvas
                              :width="40"
                              :height="40"
                              :max-coordinate="255"
                              class="drawing-canvas ml-2"
                            />
                          </div>
                        </v-fade-transition>
                      </div>
                    </v-row>
                  </div>
                </v-expand-transition>
                <!-- Guild Pin Msg -->
                <div
                  ref="guildPinMsgWrapperRef"
                  :class="[
                    guildPinMsgWrapperShow,
                    { 'is-folding': guildPinMsg.foldingEnabled },
                    'guild-pin-msg-wrapper'
                  ]"
                >
                  <v-row no-gutters>
                    <div
                      :class="[
                        { 'pr-4': !guildOwner },
                        'guild-pin-msg-box d-flex flex-nowrap pl-4 py-3'
                      ]"
                      :style="{ 'background-color': bgColor }"
                    >
                      <div class="decorate-line"></div>
                      <span class="material-symbols-outlined default-content--text mx-2">
                        campaign
                      </span>
                      <div
                        ref="guildPinMsgContentRef"
                        :class="[
                          guildPinMsgContentIsOpened,
                          'guild-pin-msg-content d-flex flex-column default-content--text'
                        ]"
                      >
                        <pre
                          :class="[
                            { 'pr-11': guildOwner },
                            'paragraph text-body-2 custom-text-noto default-content--text'
                          ]"
                          ref="guildPinMsgTextRef"
                          @click="toogleGuildPinMsg()"
                          >{{ guildPinMsg.content }}</pre
                        >
                        <button
                          v-if="guildOwner"
                          class="edit-button mr-n2 mt-n2 rounded-sm align-self-start"
                          @click="showEditGuildPinMsgDialog()"
                        >
                          <span class="material-symbols-outlined default-content--text ma-2">
                            edit_square
                          </span>
                        </button>
                      </div>
                    </div>
                  </v-row>
                </div>
                <!-- msg-box -->
                <div
                  v-for="(value, key) in msg"
                  :key="key"
                  v-show="key === currentChat.key"
                  :id="`msg-box-${key}`"
                  :class="['msg-box', { 'notch-right': hasRightNotch }]"
                  :style="{ 'background-color': bgColor }"
                  @scroll="handleScrollDebounced(`msg-box-${key}`)"
                  @click="updateReadNoty(`msg-box-${key}`)"
                >
                  <msgBox :msg-ary="value" :msg-key="key" @showImgDialog="showImgDialog" />
                </div>
                <v-fade-transition>
                  <div
                    v-if="miniGame.gameType === 2 && showMiniGameCanvas && selectedChat === 0"
                    class="overlay-canvas"
                    @click="setShowMiniGameCanvas(false)"
                  >
                    <v-row>
                      <DrawingCanvas
                        :width="breakpoint.xsOnly ? 188 : 250"
                        :height="breakpoint.xsOnly ? 188 : 250"
                        :max-coordinate="255"
                        class="drawing-canvas"
                      >
                      </DrawingCanvas>
                      <overlayCanvas />
                    </v-row>
                  </div>
                </v-fade-transition>
                <!-- player semantic -->
                <v-expand-transition>
                  <div
                    v-show="showSemantic"
                    class="py-2 px-4"
                    :style="{ 'background-color': bgColor }"
                  >
                    <div class="semantic-bg chat-alert-fill pt-1 pb-3 px-3">
                      <v-row no-gutters justify="start">
                        <v-col cols="12" class="d-flex flex-nowrap align-center">
                          <span class="primary--text text-subtitle-2 font-weight-bold">
                            {{ $t('semantic_title') }}
                          </span>
                          <v-spacer></v-spacer>
                          <v-btn
                            icon
                            @click="
                              () => {
                                $store.commit('chat/SET_SHOW_SEMANTIC', false)
                              }
                            "
                          >
                            <v-icon color="primary"> mdi-close </v-icon>
                          </v-btn>
                        </v-col>
                        <v-col cols="12" class="d-flex flex-nowrap" align="start">
                          <div>
                            <span class="primary--text text-body-2 font-weight-medium">
                              {{ $t('semantic_noty') }}
                            </span>
                          </div>
                        </v-col>
                      </v-row>
                    </div>
                  </div>
                </v-expand-transition>
                <v-expand-transition>
                  <div v-show="showMiniGame && selectedChat === 0" class="mini-game-bg pa-1">
                    <v-row
                      v-show="miniGame.gameType === 1 && selectedChat === 0"
                      no-gutters
                      justify="start"
                    >
                      <v-col cols="12" align="start">
                        <div class="game-outline pa-4">
                          <span class="primary--text">{{ gameHeading }}</span>
                        </div>
                      </v-col>
                    </v-row>

                    <v-row no-gutters justify="center" :class="{ 'mt-2': miniGame.gameType === 1 }">
                      <v-col cols="12" align="center">
                        <div class="d-flex flex-nowrap pl-2 pr-14 mini-game-bg">
                          <span class="default-content--text">{{
                            $t('miniGameCountDown', { time: countDownTime })
                          }}</span>
                          <v-spacer></v-spacer>
                          <span class="default-content--text">{{
                            $t('miniAnswer', {
                              count: miniGameIsPlay ? answerCount : 0,
                              denominator: 3
                            })
                          }}</span>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                </v-expand-transition>
                <!-- msg-bar -->
                <div>
                  <msgBar
                    ref="msgBar"
                    only-text
                    :no-target="isNoTarget"
                    :target-offline="isTargetOffline"
                    :is-customer="isOfficial(currentChat.title)"
                    :is-chat-btns-show="true"
                    @goCustomer="goCustomer"
                    @sendMessageEvent="sendMessageEvent"
                    @sendForbiddenEvent="sendForbiddenEvent"
                    @sendAnswerEvent="sendAnswerEvent"
                    @sendStickerEvent="sendStickerEvent"
                    @sendCustomStickerEvent="sendCustomStickerEvent"
                    @click="updateReadNoty(`msg-box-${key}`)"
                  />
                </div>
                <!-- scroll to bottom -->
                <v-slide-y-reverse-transition>
                  <v-btn
                    v-show="scrollTopStatus"
                    :class="[
                      'scroll-top-btn mx-2',
                      { 'is-keyboard-close': $device.isMobile && !isKeyboardOpen },
                      { 'notch-right': hasRightNotch }
                    ]"
                    small
                    fab
                    dark
                    color="gradient-button"
                    @click="scrollToBottom"
                  >
                    <span class="material-symbols-outlined button-icon--text font-weight-medium">
                      arrow_downward
                    </span>
                  </v-btn>
                </v-slide-y-reverse-transition>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <settingDialog
      v-if="showSettingDialogStatus"
      :show-setting-dialog-status.sync="showSettingDialogStatus"
    />
    <imgDialog :img-dialog-status.sync="imgDialog.show" :img.sync="imgDialog.img" />
    <editGuildPinMsgDialog
      v-if="showEditGuildPinMsgDialogStatus"
      :show-edit-guild-pin-msg-dialog-status.sync="showEditGuildPinMsgDialogStatus"
    />
  </div>
</template>

<script>
  import orientation from '~/mixins/orientation'
  import relationship from '@/mixins/relationship.js'
  import setupScrollHtml from '~/mixins/setupScrollHtml'
  import chat from '~/mixins/chatroom/chat'
  import audioBuffer from '~/mixins/chatroom/audioBuffer'
  import guildMgr from '~/mixins/guildMgr.js'
  import debounce from 'lodash/debounce'
  import cloneDeep from 'lodash/cloneDeep'
  import scssLoader from '@/mixins/scssLoader.js'
  import miniGame from '@/mixins/miniGame.js'
  import languageOption from '@/mixins/languageOption.js'
  import converter from '~/mixins/converter'
  import analytics from '@/mixins/analytics.js'
  const STATION = process.env.STATION
  const Config = require(`@/plugins/xin-socket/protocolConfig/${STATION}/protocol.config`).default

  export default {
    name: 'chatRoomDialog',
    mixins: [
      orientation,
      chat,
      relationship,
      audioBuffer,
      scssLoader,
      guildMgr,
      setupScrollHtml,
      languageOption,
      converter,
      analytics,
      miniGame
    ],
    props: {
      showChatRoomDialogStatus: {
        type: Boolean,
        default: false
      }
    },
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      msgBar: () => import('~/components/chatroom/msgBar'),
      chatList: () => import('~/components/chatroom/chatList'),
      msgBox: () => import('~/components/chatroom/msgBox'),
      msgTitle: () => import('~/components/chatroom/msgTitle'),
      settingDialog: () => import('~/components/chatroom/settingDialog'),
      imgDialog: () => import('~/components/imgDialog'),
      DrawingCanvas: () => import('~/components/miniGame/drawCanvas'),
      overlayCanvas: () => import('~/components/miniGame/overlayCanvas'),
      editGuildPinMsgDialog: () => import(`~/components/chatroom/editGuildPinMsgDialog`)
    },
    data() {
      return {
        selectedFriendChat: '',
        chatListKeyword: '',
        scrollTopStatus: false,
        showChatMobile: false,

        showSettingDialogStatus: false,
        cardTitle: {
          second: {
            icon: 'settings',
            action: this.showSettingsDialog
          }
        },
        updateUserOnlineTimer: null, // 定義 updateUserOnlineTimer 變數
        updateUserOnlineTimerStatus: false,
        initFraudNoty: false,
        bgColor: '',
        officialName: '官方-',
        handleScrollDebounced: null, // 定義防抖函數
        addBlockBtnDisableStatus: false,
        addFriendBtnDisableStatus: false,
        showChatRoomDialogStatusTemp: false,
        imgDialog: {
          show: false,
          img: ''
        },
        showEditGuildPinMsgDialogStatus: false,
        setVHDebounced: null,
        guildOwner: false,
        guildPinMsg: {
          content: '',
          foldingEnabled: false,
          isOpened: false
        },
        widthResizeDebounced: null
      }
    },
    computed: {
      friendList({ $store }) {
        return $store.getters['social/friendList']
      },
      // call by reference
      // note: 當Object或Array發生變化時，如果使用的是“改變”（mutate）而不是“替換”（replace）的方式，
      //       舊值和新值將相同，因為它們引用相同的對象或數組。Vue不會保留先前的值的副本。
      // https://v2.vuejs.org/v2/api/#vm-watch
      friendListClone() {
        return cloneDeep(this.friendList)
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      chats({ $store }) {
        function compareFriends(a, b) {
          const checkIsWhisper = (id) => {
            return id === 0 || id === 1
          }
          // 先按照 id 值排序
          if (checkIsWhisper(a.id) && b.id !== 0) return -1
          if (a.id !== 0 && checkIsWhisper(b.id)) return 1

          // 依照 pin 值排序，true 在前
          if (a.pin && !b.pin) {
            return -1
          }
          if (!a.pin && b.pin) {
            return 1
          }

          // 依照 online 值排序，true 在前
          if (a.online && !b.online) {
            return -1
          }
          if (!a.online && b.online) {
            return 1
          }

          // 依照 date 值排序，最新的在前
          if (a.lastUpdate === null && b.lastUpdate !== null) {
            return 1
          }
          if (a.lastUpdate !== null && b.lastUpdate === null) {
            return -1
          }
          if (a.lastUpdate !== null && b.lastUpdate !== null) {
            if (a.lastUpdate > b.lastUpdate) {
              return -1
            }
            if (a.lastUpdate < b.lastUpdate) {
              return 1
            }
          }

          // 依照 title 值排序
          else {
            if (!isNaN(a.title) && !isNaN(b.title)) {
              return a.title - b.title
            } else if (!isNaN(a.title)) {
              return -1 // a 是数字，排在前面
            } else if (!isNaN(b.title)) {
              return 1 // b 是数字，排在前面
            } else if (/^\d/.test(a.title)) {
              // 使用正则表达式检查字符串是否以数字开头
              return -1
            } else {
              // 接下来比较英文
              const isStarWithEnglishA = /^[a-zA-Z]/.test(a.title)
              const isStarWithEnglishB = /^[a-zA-Z]/.test(b.title)

              if (isStarWithEnglishA && isStarWithEnglishB) {
                return a.title.localeCompare(b.title, 'en')
              } else if (isStarWithEnglishA && !isStarWithEnglishB) {
                return -1
              } else if (!isStarWithEnglishA && isStarWithEnglishB) {
                return 1
              } else {
                // 最后比较中文，按照中文第一个字的笔画排序
                return a.title.localeCompare(b.title, 'zh-Hant')
              }
            }
          }
        }
        let chats = $store.getters['chat/chats']
        let chatTmp = chats.map((item) => item)

        return chatTmp.sort(compareFriends)
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      currentChat() {
        const chat = this.chats.find((item) => item.id === this.selectedChat) || this.chats[0]
        if (chat === this.chats[0]) this.$store.commit('chat/SET_SELECTED_CHAT', 0)
        return chat
      },
      currentMsgAry() {
        let key = ''
        key = this.currentChat?.key

        if (key !== undefined && key in this.msg) return this.msg[key]
        else return []
      },
      isStranger() {
        return !this.currentChat?.isOfficial &&
          this.friendList.findIndex((item) => item.username === this.currentChat?.title) === -1
          ? true
          : false
      },
      isTargetOffline() {
        let isOnline = false

        if (this.currentChat === undefined) {
          return isOnline
        }

        isOnline =
          this.checkIsTitle(this.currentChat?.id) && !this.isOfficial(this.currentChat.title)
            ? this.currentChat?.online
            : true
        return !isOnline
      },
      isNoTarget() {
        return this.$refs.chatList?.friendListFilter.length === 0
      },
      msg({ $store }) {
        return $store.getters['chat/msg']
      },
      // call by reference
      // note: 當Object或Array發生變化時，如果使用的是“改變”（mutate）而不是“替換”（replace）的方式，
      //       舊值和新值將相同，因為它們引用相同的對象或數組。Vue不會保留先前的值的副本。
      // https://v2.vuejs.org/v2/api/#vm-watch
      msgClone() {
        return cloneDeep(this.msg)
      },
      allChatNoty({ $store }) {
        return $store.getters['chat/allChatNoty']
      },
      ownName({ $store }) {
        return $store.getters['role/userName']
      },
      setting({ $store }) {
        return $store.getters['chat/setting']
      },
      level({ $store }) {
        return $store.getters['role/level']
      },
      otherStarCityOnlinePlatformsReminderDisabled() {
        return this.$UIConfig.lock.otherStarCityOnlinePlatformsReminderDisabled
      },
      serviceMail({ $store }) {
        return $store.getters[`${STATION}/companyInfo/serviceMail`]
      },
      phoneNumber({ $store }) {
        return $store.getters[`${STATION}/companyInfo/phoneNumber`]
      },
      isKeyboardOpen({ $store }) {
        return $store.getters['chat/isKeyboardOpen']
      },
      selectedChat({ $store }) {
        return $store.getters['chat/selectedChat']
      },
      showSemantic({ $store }) {
        return $store.getters['chat/showSemantic']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      },
      guildPinMsgWrapperShow() {
        return this.selectedChat === 1 && this.$UIConfig.guildPinMsg.enable ? 'show' : ''
      },
      guildPinMsgContentIsOpened() {
        return this.guildPinMsg.isOpened && this.guildPinMsg.foldingEnabled ? 'is-opened' : ''
      }
    },
    watch: {
      showChatRoomDialogStatus: {
        handler(val) {
          this.showChatRoomDialogStatusTemp = val
          if (val) {
            this.setVH()
            window.addEventListener('resize', this.setVHDebounced)
            this.$nextTick(() => {
              requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                  //確保已渲染完畢
                  this.widthResizeDebounced()
                  this.initGuildPinMsg()
                  setTimeout(() => {
                    if (
                      this.$vuetify.breakpoint.smAndUp ||
                      (this.$vuetify.breakpoint.xsOnly && this.showChatMobile === true)
                    ) {
                      if (this.currentChat)
                        this.checkShowScrollStatusAndUpdateReadNoty(
                          `msg-box-${this.currentChat.key}`
                        )
                    }
                  }, 1000)
                })
              })
            })
          } else {
            window.removeEventListener('resize', this.setVHDebounced)
            document.documentElement.style.removeProperty('--chatroom-vh')
            this.$store.commit('chat/SET_IS_KEYBOARD_OPEN', false)
            this.$nextTick(() => {
              this.$refs.msgBar.clearMsg()
            })
          }
          this.setupScrollActive(val)
        }
      },
      'setting.opacity': {
        immediate: true,
        handler(val) {
          let hexColor = this.$vuetify.theme.defaults.dark['dialog-fill']
          hexColor = hexColor.replace('#', '')
          // 解析 R、G、B 分量
          let r = parseInt(hexColor.substring(0, 2), 16)
          let g = parseInt(hexColor.substring(2, 4), 16)
          let b = parseInt(hexColor.substring(4, 6), 16)

          this.bgColor = `rgba(${r}, ${g}, ${b}, ${val})`
        }
      },
      'setting.whisper.onlyFriend': {
        immediate: true,
        handler() {
          this.$store.dispatch('chat/countAllChatNoty')
          this.$store.dispatch('chat/countWhisperNoty')
        }
      },
      msgClone: {
        async handler(newVal, oldVal) {
          //在xs的情況下且不是顯示聊天室的情況下不自動滾動
          if (this.$vuetify.breakpoint.xsOnly && !this.showChatMobile) {
            return
          }

          const self = this
          let key = null
          const autoScroll = async function () {
            //確保元素已經被渲染
            const scrollToBottomDelay = (id) =>
              new Promise((resolve) => {
                setTimeout(async () => {
                  await self.scrollToBottomWithId(id)
                  resolve()
                }, 50)
              })

            const id = self.getCurrentMsgBoxID()
            const isNotScrollToBottom = self.isNotScrollToBottom(id)
            if (!isNotScrollToBottom) {
              const id = `msg-box-${self.currentChat.key}`
              await scrollToBottomDelay(id)
            }
            self.scrollTopStatus = isNotScrollToBottom
          }

          key = this.currentChat.key

          const isFirstMsg = !(key in oldVal) && key in newVal
          const isInMsg = key in oldVal && key in newVal
          const isAddMsg = oldVal[key]?.length < newVal[key]?.length
          const isLastMsgDiff =
            oldVal[key]?.[oldVal[key]?.length - 1]?.id !==
            newVal[key]?.[newVal[key]?.length - 1]?.id
          if (
            this.showChatRoomDialogStatusTemp &&
            (isFirstMsg || (isInMsg && (isAddMsg || isLastMsgDiff)))
          ) {
            autoScroll()
          }
        },
        deep: true
      },
      selectedChat: {
        handler(newVal) {
          if (newVal === 1) {
            this.updateGuildValue()
            this.widthResizeDebounced()
          } else if (newVal !== 1) {
            this.initGuildPinMsg()
          }
        }
      },
      guildNews: {
        handler(newVal) {
          this.guildPinMsg.content = this.replaceKeywords(newVal)
          this.widthResizeDebounced()
        }
      },
      guildMaster: {
        handler() {
          this.updateGuildValue()
        }
      },
      showChatMobile: {
        // 當 showChatMobile 改變時，檢查是否需要初始化公會置頂消息
        handler(newVal) {
          if (newVal) this.initGuildPinMsg()
        }
      }
    },
    created() {
      this.selectedFriendChat = this.friendList[0]?.username
      this.$nuxt.$on('chat:createMessage', this.createMessage)
      this.handleScrollDebounced = debounce(this.checkShowScrollStatusAndUpdateReadNoty, 500) // 使用 debounce 创建防抖函数
      this.setVHDebounced = debounce(this.setVH, 100)
      this.widthResizeDebounced = debounce(this.checkGuildPinMsgSetUp, 100)
    },
    mounted() {
      this.setSettingFromLocalStorage()
      this.updateGuildValue()
      window.addEventListener('resize', this.widthResizeDebounced)
    },
    beforeDestroy() {
      clearInterval(this.updateUserOnlineTimer) // 取消定時器
      this.$nuxt.$off('chat:createMessage', this.createMessage)
      this.$store.commit('chat/SET_IS_KEYBOARD_OPEN', false)
      window.removeEventListener('resize', this.widthResizeDebounced)
    },
    methods: {
      closeDialog() {
        this.$store.commit('chat/SET_SHOW_SEMANTIC', false)
        this.$nuxt.$emit('root:showChatRoomDialogStatus', false)
      },
      goCustomer() {
        this.$store.commit('chat/SET_SHOW_SEMANTIC', false)
        this.$nuxt.$emit('root:showCustomerServiceDialogStatusEvent', true)
      },
      checkIsTitle(id) {
        if (this.hasGuild) return id > 1
        return id > 0
      },

      showSettingsDialog() {
        this.showSettingDialogStatus = true
      },
      selectedChatEvent(id) {
        this.$refs.msgBar.clearMsg()
        this.$nextTick(() => {
          this.setGetUserDeatilTimer(id)
          //手機板透過 mobileClickedEvent 去進行檢查，手機板只有在 mobileClickedEvent 才會真正進到聊天室頁面
          if (this.$vuetify.breakpoint.smAndUp) {
            //確保渲染完畢
            setTimeout(() => {
              this.checkShowScrollStatusAndUpdateReadNoty(`msg-box-${this.currentChat.key}`)
            }, 100)
          }
        })
      },
      mobileClickedEvent() {
        this.showChatMobile = true
        this.widthResizeDebounced()
        this.$nextTick(() => {
          //確保渲染完畢
          setTimeout(() => {
            if (this.currentChat !== null)
              this.checkShowScrollStatusAndUpdateReadNoty(`msg-box-${this.currentChat.key}`)
          }, 100)
        })
      },
      setGetUserDeatilTimer(id) {
        const self = this
        if (self.updateUserOnlineTimerStatus === false) {
          //非官方頻道與非官方帳號
          if (this.checkIsTitle(id)) {
            self.updateUserOnlineTimer = setInterval(async () => {
              self.updateUserOnlineTimerStatus = true
              //1.對話窗有打開 2.所選擇的對話窗不是全頻聊天 3.非官方帳號 4.非好友
              if (
                self.showChatRoomDialogStatus &&
                this.checkIsTitle(self.currentChat.id) &&
                !self.isOfficial(self.currentChat.title) &&
                !self.currentChat.isFriend
              ) {
                const role = await self.$store.dispatch(
                  'social/getUserDetail',
                  self.currentChat.key
                )
                self.$store.commit('chat/SET_CHAT_ONLINE_STATUS_BY_ID', {
                  id: self.currentChat.id,
                  online: role.online
                })
              }
            }, 3000)
          }
        }
      },
      clearNoty() {
        if (this.currentChat !== undefined) this.clearChatNoty(this.currentChat.id)
      },
      clearChatNoty(id) {
        const self = this
        const chatsByStore = this.$store.getters['chat/chats']
        const index = chatsByStore.findIndex((item) => item.id === id)
        if (index !== -1) {
          const key = chatsByStore[index].key
          if (key in this.msg) {
            this.msg[key].forEach((item) => self.setIsRead({ id: item.id, status: true }))
          }
          this.$store.commit('chat/SET_NOTY', { index: index, payload: 0 })
          this.$store.dispatch('chat/countAllChatNoty')
        }
      },
      clearFriendNoty(name) {
        const self = this
        const index = this.friendList.findIndex((item) => item.username === name)
        if (index !== -1) {
          const key = this.friendList[index].username
          if (key in this.msg) {
            this.msg[key].forEach((item) => self.setIsRead({ id: item.id, status: true }))
          }
          this.$store.commit('social/SET_FRIEND_NOTY', { username: name, noty: 0 })
          this.$store.dispatch('chat/countAllChatNoty')
        }
      },
      async sendMessageEvent(message) {
        await this.sendMessage(message)
        this.$nextTick(() => {
          setTimeout(() => {
            this.scrollToBottom()
          }, 100)
        })
      },
      sendForbiddenEvent(message) {
        this.sendForbiddenMessage(message)
        this.$nextTick(() => {
          setTimeout(() => {
            this.scrollToBottom()
          }, 300)
        })
      },
      async sendAnswerEvent(message) {
        if (this.currentChat.id === 0 && this.answerCount > 0) {
          await this.sendAnswerNotify('msg', this.$wsPacketFactory.sendMiniGameAnswer(message))
        }
        this.$nextTick(() => {
          setTimeout(() => {
            this.scrollToBottom()
          }, 100)
        })
      },
      async sendAndNotify(type, sendPacket) {
        const systemCond = (data) =>
          data.type === 0 && data.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.SYSTEM_MESSAGE.ID
        const banSpeaking = (data) =>
          data.type === 103 && data.isFeature(this.$xinConfig.FEATURE.BAN_SPEAKING.ID)
        const channelBanSpeaking = (data) =>
          data.type === 17 && data.messageType === 1 && data.content.message.includes('禁止發言')

        this.$wsClient.send(sendPacket)
        const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
          const dmText = ['密給', '@lang_215', 'Mật ngữ với']
          const allMsg = data.content
            ? data.content.allMessage
              ? data.content.allMessage
              : ''
            : ''
          const isIncludeDMText = dmText.some((text) => {
            return allMsg.includes(text)
          })
          return (
            (data.type === 17 && (data.user.name === this.ownName || isIncludeDMText)) ||
            channelBanSpeaking(data) ||
            data.isFeature(this.$xinConfig.LOGIN_SERVICE.TYPE.SYSTEM_MESSAGE.ID) ||
            data.isFeature(this.$xinConfig.FEATURE.BAN_SPEAKING.ID)
          )
        })
        if (systemCond(res)) this.$notify.warning(this.removeDash(res.message))
        else if (banSpeaking(res)) {
          const errorText = this.$UIConfig.chatroom.mutedText.format(
            this.$t('you_are_muted_until'),
            this.convertTime(res.expiryDate)
          )

          this.$notify.warning(errorText)
        } else if (channelBanSpeaking(res))
          this.$notify.warning(`${this.$t('you_are_muted_in_this_channel')}`)
        else if (type === 'msg') this.$refs.msgBar.clearMsg()
      },

      async sendAnswerNotify(type, sendPacket) {
        const systemCond = (data) =>
          data.type === 0 && data.commandId === this.$xinConfig.LOGIN_SERVICE.TYPE.SYSTEM_MESSAGE.ID
        const banSpeaking = (data) =>
          data.type === 103 && data.isFeature(this.$xinConfig.FEATURE.BAN_SPEAKING.ID)
        const channelBanSpeaking = (data) =>
          data.type === 17 && data.messageType === 1 && data.content.message.includes('禁止發言')

        this.$wsClient.send(sendPacket)
        const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
          return (
            data.type === 17 ||
            data.isFeature(this.$xinConfig.LOGIN_SERVICE.TYPE.SYSTEM_MESSAGE.ID) ||
            data.isFeature(this.$xinConfig.FEATURE.BAN_SPEAKING.ID)
          )
        })

        if (res.content.allMessage.includes(this.ownName)) {
          this.$store.commit('miniGame/SET_ANSWER_COUNT', this.answerCount - 1)
        }

        if (systemCond(res)) this.$notify.warning(this.removeDash(res.message))
        else if (banSpeaking(res)) {
          const errorText = this.$UIConfig.chatroom.mutedText.format(
            this.$t('you_are_muted_until'),
            this.convertTime(res.expiryDate)
          )

          this.$notify.warning(errorText)
        } else if (channelBanSpeaking(res))
          this.$notify.warning(`${this.$t('you_are_muted_in_this_channel')}`)
        else if (type === 'msg') this.$refs.msgBar.clearMsg()
      },
      async sendMessage(message) {
        try {
          if (this.currentChat.id === 0) {
            await this.sendAndNotify(
              'msg',
              this.$wsPacketFactory.sendMessageToAll({ message: message })
            )
          } else if (this.currentChat.id === 1 && this.hasGuild) {
            const guildMsg = {
              Type: 0,
              Contents: {
                Text: message,
                TextColor: 'FBB459'
              }
            }
            const msgString = JSON.stringify(guildMsg)
            const sendMsgSuccess = await this.sendGuildChatMsg(msgString)
            if (sendMsgSuccess) this.$refs.msgBar.clearMsg()
          } else {
            await this.sendAndNotify(
              'msg',
              this.$wsPacketFactory.sendSecretlyMessage({
                username: this.currentChat.title,
                message: message
              })
            )
          }
        } catch (err) {
          console.log(err)
        }
      },
      sendForbiddenMessage(message) {
        this.$store.dispatch('chat/addForbiddenChat', {
          message: message,
          title: this.currentChat.title
        })
        this.$refs.msgBar.clearMsg()
      },
      async createMessage(username, timeSecond = 0) {
        const self = this
        const role = await this.$store.dispatch('social/getUserDetail', username)

        if (username === 'guild_chat') {
          checkAndOpenChat(username, false)
        } else if (!role.online) {
          this.$notify.warning(this.$t('player_offline', { player: role.username }))
        } else {
          processRole(username)
        }

        function processRole(username) {
          const index = self.getFriendIndex(username)
          if (index !== -1) {
            handleFriendRole(username)
          } else if (!self.setting.whisper.onlyFriend) {
            handleNonFriendRole(username)
          } else {
            setTimeout(() => {
              self.showNotyDialog(self.$t('reminder'), self.$t('player_not_friend'))
            }, timeSecond)
          }
        }

        function handleFriendRole(username) {
          checkAndOpenChat(username, true)
        }

        function handleNonFriendRole(username) {
          checkAndOpenChat(username, false)
        }

        async function checkAndOpenChat(username, isFriend) {
          const inChatsIndex = self.chats.findIndex((item) => item.title === username)

          if (inChatsIndex === -1) {
            const userData = { userName: username }
            const img = isFriend
              ? self.friendList[self.getFriendIndex(username)].thumbUrl
              : await self.$store.dispatch('role/getThumbUrl', userData)
            const maxId = self.chats.reduce((prev, current) =>
              prev.id > current.id ? prev : current
            ).id

            const chat = {
              title: username,
              id: maxId + 1,
              img: img,
              noty: 0,
              isOfficial: false,
              key: username,
              online: true,
              isBlock: false,
              isFriend: isFriend,
              lastUpdate: new Date(),
              pin: null
            }

            self.$store.commit('chat/ADD_CHATS', chat)

            //如果msg裡面沒有他的對話，則新增一個空的對話
            if (!(username in self.msg))
              self.$store.commit('chat/SET_MSG', { title: username, payload: [] })

            self.$store.commit('chat/SET_SELECTED_CHAT', chat.id)
          } else {
            self.$store.commit('chat/SET_SELECTED_CHAT', self.chats[inChatsIndex].id)
          }
          //依各個事件所需要的延遲時間去執行
          setTimeout(() => {
            self.selectedChatEvent(self.selectedChat)
            if (username !== 'guild_chat') self.checkFirstWispher(username)
          }, timeSecond)
          if (self.$vuetify.breakpoint.xsOnly) self.showChatMobile = true
        }
      },
      async checkAddFriendEvent(name) {
        this.addFriendBtnDisableStatus = true
        await this.checkAddFriend(name)
        this.addFriendBtnDisableStatus = false
      },
      async checkAddBlockEvent(name) {
        this.addBlockBtnDisableStatus = true
        await this.checkAddBlock(name)
        this.addBlockBtnDisableStatus = false
        if (this.$device.isMobile) this.showChatMobile = false
      },
      async scrollToBottom() {
        this.clearNoty()
        let elementID = ''
        elementID = `msg-box-${this.currentChat.key}`
        const element = document.getElementById(elementID)
        if (element)
          await this.$nextTick(() => {
            element.scrollTo({ top: element.scrollHeight, behavior: 'auto' })
          })
      },
      async scrollToBottomWithId(id) {
        this.clearNoty()
        let elementID = id
        const element = document.getElementById(elementID)
        if (element)
          await this.$nextTick(() => {
            element.scrollTo({ top: element.scrollHeight, behavior: 'auto' })
          })
      },
      getCurrentMsgBoxID() {
        let elementID = ''
        elementID = `msg-box-${this.currentChat.key}`
        return elementID
      },
      checkShowScrollStatusAndUpdateReadNoty(id) {
        this.checkShowScrollStatus(id)
        this.updateReadNoty(id)
      },
      checkShowScrollStatus(id) {
        const self = this

        this.$nextTick(() => {
          const element = document.getElementById(id)
          if (element) self.scrollTopStatus = this.isNotScrollToBottom(id)
          else self.scrollTopStatus = false
        })
      },
      checkShowScrollStatusWithId() {
        const id = this.getCurrentMsgBoxID()
        if (id.length !== 0) this.checkShowScrollStatus(id)
      },
      isNotScrollToBottom(id) {
        const element = document.getElementById(id)
        // 找不到該元素，視為不在底部
        if (!element) {
          this.scrollTopStatus = false
          return
        }
        const scrollTop = element.scrollTop
        const scrollHeight = element.scrollHeight
        const clientHeight = element.clientHeight

        //手機dpi存在誤差，故使用容許誤差的方式
        // 计算允许的最大误差和最小误差
        const maxTolerance = 1
        const minTolerance = -1

        //卷軸位置是否在底部
        return (
          scrollHeight > clientHeight &&
          !(
            scrollHeight - (scrollTop + clientHeight) >= minTolerance &&
            scrollHeight - (scrollTop + clientHeight) <= maxTolerance
          )
        )
      },
      async searchPlayer() {
        if (this.chatListKeyword.length === 0) return

        const isWhitespace = (str) => /^\s*$/.test(str)

        if (isWhitespace(this.chatListKeyword)) {
          this.$notify.warning(this.$t('nickname_cannot_contain_whitespace'))
        }
        //是不是自己
        else if (this.chatListKeyword === this.ownName)
          this.$notify.warning(this.$t('cannot_operate_on_self'))
        else if (this.checkIsBlock(this.chatListKeyword))
          this.$notify.warning(this.$t('cannot_whisper_to_blacklist'))
        else {
          if (this.$UIConfig.lock.chatroom.checkoutUserStation) {
            const res = await this.$store.dispatch('social/getUserCategory', this.chatListKeyword)
            if (!res.isSameCountry) {
              this.$notify.warning(this.$t('player_offline', { player: this.chatListKeyword }))
              return
            }
          }
          this.createMessage(this.chatListKeyword)
        }
        this.chatListKeyword = ''
      },
      showNotyDialog(title, message) {
        this.$store.dispatch('easyDialog/setDialog', {
          title: title,
          message: message
        })
        this.$nuxt.$emit('root:showNotyDialogStatus', true)
      },
      getFriendIndex(userName) {
        let index = this.friendList.findIndex((item) => item.username === userName)
        return index
      },
      checkFirstWispher() {
        const reminder = this.$UIConfig.chatroom.usePhoneNumber
          ? this.phoneNumber
          : this.serviceMail
        const noty = this.$t('official_reminder', { reminder: reminder })
        if (!this.initFraudNoty) {
          this.showNotyDialog(this.$t('reminder'), noty)
          this.initFraudNoty = true
        }
      },
      setIsRead({ id, status }) {
        if (this.currentChat !== undefined) {
          this.$store.commit('chat/SET_IS_REAED', {
            key: this.currentChat.key,
            status: status,
            id: id
          })
        }
      },
      removeDash: (str) => (str.startsWith('-') ? str.slice(1) : str),
      updateReadNoty(id) {
        const container = document.getElementById(id)
        if (!container) {
          return
        }

        const elements = container.children[0]?.children
        if (elements) {
          let lastVisibleElement = null
          const containerTop = container.getBoundingClientRect().top
          const containerBottom = containerTop + container.clientHeight
          if (containerTop != 0 && containerBottom != 0) {
            for (const element of elements) {
              const elementTop = element.getBoundingClientRect().top
              const elementBottom = elementTop + element.clientHeight
              if (elementBottom <= containerBottom) {
                lastVisibleElement = element
                continue
              }
              break
            }
            if (lastVisibleElement && lastVisibleElement?.id) {
              const numericPart = lastVisibleElement.id.match(/\d+/)[0]

              const messageList = this.msg[this.currentChat.key] || []
              messageList.forEach((item) => {
                if (!item.isRead && item.id <= parseInt(numericPart)) {
                  this.setIsRead({ id: item.id, status: true })
                }
              })
              this.countAllIsNotReadMsg()
            }
          }
        }
      },
      countAllIsNotReadMsg() {
        for (const key in this.msg) {
          const element = this.msg[key]
          const noty = element.filter((item) => !item.isRead).length

          const chatsByStore = this.$store.getters['chat/chats']
          const chatIndex = chatsByStore.findIndex((item) => item.key === key)
          const friendIndex = this.friendList.findIndex((item) => item.username === key)

          if (chatIndex !== -1) {
            this.$store.commit('chat/SET_NOTY', { index: chatIndex, payload: noty })
          }

          if (friendIndex !== -1) {
            this.$store.commit('social/SET_FRIEND_NOTY', { username: key, noty: noty })
          }
        }
        this.$store.dispatch('chat/countAllChatNoty')
        this.$store.dispatch('chat/countWhisperNoty')
      },
      goDownload() {
        this.closeDialog()
        if (this.$device.isAndroid || this.$device.isIos || this.$device.isMacOS) {
          const url = 'https://www.xin-stars.com/goStore'
          this.$lineOpenWindow.open(url)
        } else {
          this.$router.push({ path: this.localePath('/downloads'), hash: '#pc' })
        }
      },
      showImgDialog(img) {
        if (img.includes('chat_picture_default')) return
        this.imgDialog.show = true
        this.imgDialog.img = img
      },
      selectFirstChat() {
        this.$store.commit('chat/SET_SELECTED_CHAT', 0)
        this.selectedChatEvent(0)
      },
      async sendStickerEvent(stickerId) {
        await this.sendSticker(stickerId)
        this.$nextTick(() => {
          setTimeout(() => {
            this.scrollToBottom()
          }, 100)
        })
      },
      async sendSticker(stickerId) {
        if (this.currentChat.id === 0) {
          await this.sendAndNotify(
            'sticker',
            this.$wsPacketFactory.sendStickerToAll({ stickerId: stickerId })
          )
        } else if (this.currentChat.id === 1 && this.hasGuild) {
          const guildSticker = { Type: 3, Contents: { StickerName: stickerId } }
          const stickerString = JSON.stringify(guildSticker)
          await this.sendGuildChatMsg(stickerString)
        } else {
          await this.sendAndNotify(
            'sticker',
            this.$wsPacketFactory.sendSecretlySticker({
              username: this.currentChat.title,
              stickerId: stickerId
            })
          )
        }
      },
      async sendCustomStickerEvent(paths) {
        await this.sendCustomSticker(paths)
        this.$nextTick(() => {
          setTimeout(() => {
            this.scrollToBottom()
          }, 100)
        })
      },
      async sendCustomSticker(paths) {
        const { thumbPath, imgPath } = paths
        if (this.currentChat.id === 0) {
          await this.sendAndNotify(
            'customSticker',
            this.$wsPacketFactory.sendCustomStickerMessageToAll(paths)
          )
        } else if (this.currentChat.id === 1 && this.hasGuild) {
          const dataType = Config.CLOUD_DATA_TYPE.STICKER
          const ImageURL = dataType + ' ' + Date.now().toString() + ' ' + imgPath + ' ' + thumbPath
          const param = { Type: dataType, Contents: { ImageURL } }
          const paramString = JSON.stringify(param)
          await this.sendGuildChatMsg(paramString)
        } else {
          const param = { username: this.currentChat.title, thumbPath, imgPath }
          await this.sendAndNotify(
            'customSticker',
            this.$wsPacketFactory.sendSecretlyCustomSticker(param)
          )
        }
      },
      async sendGuildChatMsg(msgString) {
        this.$wsClient.send(this.$wsPacketFactory.sendGuildChat(msgString))

        const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
          return (
            data.isFeature(this.$xinConfig.FEATURE.BAN_SPEAKING.ID) ||
            data.isFeature(this.$xinConfig.FEATURE.GUILD.TYPE.GUILD_GET_CHAT) ||
            data.isFeature(this.$xinConfig.FEATURE.GUILD.TYPE.GUILD_MESSAGE_ERROR)
          )
        })

        if (res.isFeature(this.$xinConfig.FEATURE.BAN_SPEAKING.ID)) {
          const errText = this.$UIConfig.chatroom.banSpeaking.format(
            this.convertMessage(res?.content)
          )
          this.$notify.warning(errText)
          return false
        }

        if (res.isFeature(this.$xinConfig.FEATURE.GUILD.TYPE.GUILD_GET_CHAT)) {
          return res?.message === msgString
        }
      },
      updateGuildValue() {
        this.guildOwner = this.userName === this.guildMaster ? true : false
        this.guildPinMsg.content = this.replaceKeywords(this.guildNews)
      },
      initGuildPinMsg(delay = 0, scrollTransition = 'instant') {
        // 先重置展開狀態
        this.guildPinMsg.isOpened = false
        // 確保 DOM 已經 ready 再重設 scrollTop
        this.$nextTick(() => {
          setTimeout(() => {
            const contentRef = this.$refs.guildPinMsgContentRef
            if (contentRef) {
              contentRef.scrollTo({
                top: 0,
                behavior: scrollTransition
              })
            }
          }, delay)
        })
      },
      showEditGuildPinMsgDialog() {
        this.initGuildPinMsg(300, 'smooth')
        this.showEditGuildPinMsgDialogStatus = true
      },
      toogleGuildPinMsg() {
        if (!this.guildPinMsg.foldingEnabled) return
        this.guildPinMsg.isOpened = !this.guildPinMsg.isOpened
        //確認已經打開，並配合動畫後延遲滾動
        setTimeout(() => {
          if (this.guildPinMsg.isOpened) {
            this.$refs.guildPinMsgContentRef.classList.add('can-scroll')
          } else {
            this.initGuildPinMsg(300, 'smooth')
            this.$refs.guildPinMsgContentRef.classList.remove('can-scroll')
          }
        }, 300)
      },
      checkGuildPinMsgSetUp() {
        const defaultHeight = 40
        //重置是否需要摺疊
        this.guildPinMsg.foldingEnabled = false
        this.$nextTick(() => {
          requestAnimationFrame(() => {
            const height = this.$refs.guildPinMsgTextRef?.clientHeight ?? defaultHeight
            if (height > defaultHeight) this.guildPinMsg.foldingEnabled = true
          })
        })
      },
      setVH() {
        const isModernSafari =
          this.$device.isSafari &&
          parseFloat(this.$device.userAgent.match(/Version\/(\d+\.\d+)/)?.[1] || 0) > 16.3

        const vh = isModernSafari
          ? '100dvh'
          : window.innerHeight > 0
          ? `${window.innerHeight}px`
          : '100vh'

        document.documentElement.style.setProperty('--chatroom-vh', vh)
      },
      preventLongInput(e) {
        const value = e.target.value
        // 允許刪除、方向鍵等功能鍵
        if (
          e.keyCode === 8 ||
          e.keyCode === 46 ||
          e.keyCode === 37 ||
          e.keyCode === 39 ||
          e.ctrlKey ||
          e.metaKey
        ) {
          return
        }
        // 如果已經達到12個字符，阻止輸入
        if (value && value.length >= 12) {
          e.preventDefault()
        }
      },
      openRedirectDialog() {
        this.$nuxt.$emit('root:redirectDialogStatus', { show: true, drawAnalytics: true })
      }
    }
  }
</script>

<style lang="scss" scoped>
  //color
  $dialog-fill: map-get($colors, 'dialog-fill');
  $grey-5: map-get($colors, 'grey-5');
  $dialog-fill-with-opacity: rgba($dialog-fill, 0.6);
  $primary: map-get($colors, 'primary');
  $default-content: map-get($colors, 'default-content');
  //height
  $chatroom-dialog-height: 560px;
  $chatroom-dialog-bar-height: 54px;
  //聊天室左側(搜尋輸入框)
  $search-field-height: 56px;
  //聊天室右側(加好友提示/官方提示)
  $msg-stranger-height: 52px;
  $msg-official-height: 64px;
  .guild-pin-msg-wrapper {
    opacity: 0;
    pointer-events: none;
    position: absolute;
    max-width: 100%;
    &.show {
      position: relative;
      opacity: 1;
      pointer-events: auto;
    }
    &.is-folding {
      .paragraph {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        cursor: pointer;
      }
    }
  }
  .guild-pin-msg-box {
    max-width: 100%;
    width: 100%;
    cursor: default;
    .decorate-line {
      background-color: $default-content;
      width: 4px;
      flex: none;
    }
    .guild-pin-msg-content {
      flex: auto;
      min-height: 40px;
      max-height: 40px;
      overflow: hidden;
      transition: all 0.2s ease-out;
      &.is-opened {
        max-height: 100px;
        .paragraph {
          -webkit-line-clamp: unset;
          line-clamp: unset;
        }
      }
      &.can-scroll {
        overflow-y: auto;
      }
    }
    .paragraph {
      width: 100%;
      margin-bottom: 0;
      overflow-wrap: break-word;
      word-break: break-word;
      line-height: 1.25rem;
      cursor: default;
      white-space: pre-wrap;
    }
    .edit-button {
      position: absolute;
      right: 16px;
      top: 12px;
      .material-symbols-outlined {
        transition: all ease-in-out 0.2s;
      }
      &:hover {
        .material-symbols-outlined {
          color: $primary !important;
        }
      }
    }
  }

  @media screen and (min-width: 960px), screen and (min-height: 600px) and (orientation: portrait) {
    .chatroom-dialog {
      height: $chatroom-dialog-height;
      .chatroom {
        height: min(
          calc(90vh - #{$chatroom-dialog-bar-height}),
          calc(#{$chatroom-dialog-height} - #{$chatroom-dialog-bar-height})
        );
        .left-side {
          .left-side-content {
            height: 100%;
            .left-side-content-textfield {
              height: $search-field-height;
            }
            .left-side-content-list {
              flex: 1;
              overflow: auto;
            }
          }
        }

        .chat-container {
          position: relative;

          .action-bar {
            background-color: $dialog-fill-with-opacity;
            height: $msg-stranger-height;
            border-bottom: 1px solid $primary;
          }
          .remind-bar {
            background-color: $dialog-fill-with-opacity;
            height: $msg-official-height;
            border-bottom: 1px solid $primary;
          }
          .msg-box {
            flex: 1;
            overflow: auto;
          }
          .scroll-top-btn {
            position: absolute;
            right: 8px;
            bottom: 62px;
            &.is-keyboard-close {
              bottom: 94px;
            }
          }
        }

        .theme--dark.v-badge .v-badge__badge::after {
          border-color: $dialog-fill;
        }
        .decorate-line {
          background-color: $default-content;
          width: 4px;
        }
        .msg-box {
          position: relative; /* 为绝对定位的画布创建定位上下文 */
        }

        .overlay-canvas {
          position: absolute;
          left: calc(50% - 125px); /* 50% - 畫布寬度的一半 */
          top: calc(50% - 125px); /* 50% - 畫布高度的一半 */
          z-index: 10;
          pointer-events: auto;
        }

        .mini-game-bg {
          background: var(--Functional-card-fill-opacity-50, rgba(68, 1, 6, 0.5));
        }

        .game-outline {
          outline-color: $primary;
          outline-style: solid;
        }

        .semantic-bg {
          border-radius: 4px;
          position: relative;
          z-index: 11;
        }

        /* 可选：添加悬停效果 */
        .drawing-canvas:hover {
          opacity: 0.8; /* 悬停时稍微增加不透明度 */
        }
      }

      :deep(.drawing-canvas) {
        margin: 0;
      }
    }
  }
  //手機直向
  @media screen and (max-width: 599px) {
    .chatroom-dialog {
      height: 100%;
      .chatroom {
        height: calc(100% - #{$chatroom-dialog-bar-height});
        .left-side {
          .left-side-content {
            height: 100%;
            .left-side-content-textfield {
              height: $search-field-height;
            }
            .left-side-content-list {
              flex: 1;
              overflow: auto;
            }
          }
        }

        .chat-container {
          position: relative;

          .action-bar {
            background-color: $dialog-fill-with-opacity;
            height: $msg-stranger-height;
            border-bottom: 1px solid $primary;
          }
          .remind-bar {
            background-color: $dialog-fill-with-opacity;
            height: $msg-official-height;
            border-bottom: 1px solid $primary;
            @media screen and (max-width: 410px) {
              background-color: $dialog-fill-with-opacity;
              height: 85px;
              border-bottom: 1px solid $primary;
            }
          }
          .msg-box {
            flex: 1;
            overflow: auto;
          }
          .scroll-top-btn {
            position: absolute;
            right: 8px;
            bottom: 62px;
            &.is-keyboard-close {
              bottom: 94px;
            }
          }
        }

        .theme--dark.v-badge .v-badge__badge::after {
          border-color: $dialog-fill;
        }
        .decorate-line {
          background-color: $default-content;
          width: 4px;
        }
        .msg-box {
          position: relative; /* 为绝对定位的画布创建定位上下文 */
        }

        .overlay-canvas {
          position: absolute;
          left: calc(50% - 94px); /* 50% - 畫布寬度的一半 */
          top: calc(75% - 94px); /* 50% - 畫布高度的一半 */
          z-index: 10;
          pointer-events: auto;
        }

        .mini-game-bg {
          background: var(--Functional-card-fill-opacity-50, rgba(68, 1, 6, 0.5));
        }
        .game-outline {
          outline-color: $primary;
          outline-style: solid;
        }

        .semantic-bg {
          border-radius: 4px;
          position: relative;
          z-index: 11;
        }

        .drawing-canvas {
          border-radius: 8px;
        }

        /* 可选：添加悬停效果 */
        .drawing-canvas:hover {
          opacity: 0.8; /* 悬停时稍微增加不透明度 */
        }
      }
    }
  }
  //手機橫向
  @media screen and (max-width: 960px) and (orientation: landscape) {
    .chatroom-dialog {
      height: var(--chatroom-vh);
      .chatroom {
        height: calc(var(--chatroom-vh) - #{$chatroom-dialog-bar-height});

        .left-side {
          .left-side-content {
            height: 100%;
            .left-side-content-textfield {
              height: $search-field-height;
            }
            .left-side-content-list {
              flex: 1;
              overflow: auto;
            }
          }
        }

        .chat-container {
          position: relative;

          .action-bar {
            background-color: $dialog-fill-with-opacity;
            height: $msg-stranger-height;
            border-bottom: 1px solid $primary;
          }
          .remind-bar {
            background-color: $dialog-fill-with-opacity;
            height: $msg-official-height;
            border-bottom: 1px solid $primary;
          }
          .msg-box {
            flex: 1;
            overflow: auto;
          }
          .scroll-top-btn {
            position: absolute;
            right: 8px;
            bottom: 62px;
            &.is-keyboard-close {
              bottom: 94px;
            }
          }
        }

        .theme--dark.v-badge .v-badge__badge::after {
          border-color: $dialog-fill;
        }
        .decorate-line {
          background-color: $default-content;
          width: 4px;
        }
        .msg-box {
          position: relative; /* 为绝对定位的画布创建定位上下文 */
        }

        .overlay-canvas {
          position: absolute;
          left: calc(50% - 125px); /* 50% - 畫布寬度的一半 */
          top: calc(50% - 125px); /* 50% - 畫布高度的一半 */
          z-index: 10;
          pointer-events: auto;
        }

        .mini-game-bg {
          background: var(--Functional-card-fill-opacity-50, rgba(68, 1, 6, 0.5));
        }

        .game-outline {
          outline-color: $primary;
          outline-style: solid;
        }

        .semantic-bg {
          border-radius: 4px;
          position: relative;
          z-index: 11;
        }

        .drawing-canvas {
          border-radius: 8px;
        }

        /* 可选：添加悬停效果 */
        .drawing-canvas:hover {
          opacity: 0.8; /* 悬停时稍微增加不透明度 */
        }
      }
    }
  }
  @media (orientation: landscape) {
    .left-side-content-textfield {
      &.notch-left {
        padding-left: calc(12px + env(safe-area-inset-left)) !important;
      }
    }
    .chatroom-title {
      &.notch-left {
        padding-left: calc(24px + env(safe-area-inset-left)) !important;
      }
    }
    .scroll-top-btn {
      &.notch-right {
        right: calc(8px + env(safe-area-inset-right)) !important;
      }
    }
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .msg-box {
      &.notch-right {
        padding-right: env(safe-area-inset-right) !important;
      }
    }
  }
</style>

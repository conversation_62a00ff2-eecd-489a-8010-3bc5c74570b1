<template>
  <div>
    <v-dialog
      v-model="showGuildAcceptTmp"
      :fullscreen="$vuetify.breakpoint.xsOnly"
      width="460px"
      persistent
      scrollable
    >
      <v-card class="pa-0" elevation="0" color="dialog-fill" :height="contentHeight">
        <customDialogTitle
          :title="$t('guild_invite_title').toUpperCase()"
          @closeDialog="tryCloseDialog"
        />

        <v-card-text class="container">
          <v-autocomplete
            v-model="playerInfoSelected"
            :items="playerInfoList"
            class="mt-6 rounded-xxl rounded-b-0 hide-arrow"
            disabled
            filled
            chips
            :label="$t('recipient')"
            item-text="name"
            item-value="name"
            hide-details
            hide-no-data
            hide-selected
            multiple
          >
            <template v-slot:selection="{ item, selected }">
              <v-chip
                id="recipient-chip"
                :input-value="selected"
                color="grey-3"
                class="default-content--text"
              >
                <v-avatar left>
                  <v-img :src="item.avator">
                    <template v-slot:placeholder>
                      <v-row class="fill-height ma-0" align="center" justify="center">
                        <v-img :src="defaultImg" contain />
                      </v-row>
                    </template>
                  </v-img>
                </v-avatar>
                {{ item.name }}
              </v-chip>
            </template>
          </v-autocomplete>

          <div class="textarea-container mt-4">
            <v-textarea
              v-model="acceptNoty"
              :value="acceptNoty"
              @click:clear="clearNoty"
              v-validate="{
                text_fullwidth: true,
                required: true,
                validate_string_length: 100
              }"
              data-vv-scope="acceptContent"
              name="acceptNoty"
              :error-messages="errors.first('acceptContent.acceptNoty')"
              clear-icon="mdi-close-circle"
              maxlength="100"
              class="content-textarea rounded-xxl rounded-b-0 disable-hyphen"
              clearable
              no-resize
              counter
              filled
              height="185px"
              :label="$t('guild_invite_content') + '*'"
            />
            <div class="template-button-wrapper">
              <v-btn
                class="button-content--text rounded-pill template-btn"
                color="white"
                max-height="28px"
                max-width="117px"
                outlined
                @click="useTemplateNoty(playerInfoSelected.name)"
              >
                <div class="d-flex align-center justify-center">
                  <span
                    class="material-symbols-outlined default-content--text mr-2"
                    style="font-size: 18px"
                  >
                    article
                  </span>
                  <span class="default-content--text">{{
                    $t('guild_invite_template').toUpperCase()
                  }}</span>
                </div>
              </v-btn>
            </div>
          </div>
          <v-row no-gutters>
            <v-spacer v-if="$vuetify.breakpoint.smAndUp"></v-spacer>
            <v-btn
              :block="$vuetify.breakpoint.xsOnly"
              class="button-content--text mt-4"
              :color="$UIConfig.defaultBtnColor"
              :disabled="!acceptNoty || disabledSubmit"
              @click="tryAcceptClick()"
            >
              {{ $t('send_mail') }}
            </v-btn>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <confirmDialog v-model="guildConfirmStatus" dialog-width="380" :action="closeDialog">
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <div class="d-flex align-center">
        <span class="default-content--text accept-word-break">{{
          $t('guild_discard_invite')
        }}</span>
      </div>
    </confirmDialog>
    <confirmDialog
      v-model="guildSetupNotyStatus"
      dialog-width="380"
      :action="
        () => {
          acceptNoty = templateText.format({ player: playerInfoSelected.name })
        }
      "
    >
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <div class="d-flex align-center">
        <span class="default-content--text accept-word-break">{{ $t('guild_set_template') }}</span>
      </div>
    </confirmDialog>
    <confirmDialog
      v-model="guildSendNotyStatus"
      dialog-width="380"
      :action="
        () => {
          onAcceptClick(playerInfoSelected.name, acceptNoty)
        }
      "
    >
      <template v-slot:title> {{ $t('hint').toUpperCase() }}</template>
      <div class="d-flex align-center">
        <span class="default-content--text accept-word-break">{{ $t('guild_send_invite') }}</span>
      </div>
    </confirmDialog>
  </div>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import relationship from '@/mixins/relationship.js'
  import scssLoader from '@/mixins/scssLoader.js'
  import guildMgr from '~/mixins/guildMgr'
  import images from '~/mixins/images'

  export default {
    name: 'guildAcceptDialog',
    mixins: [relationship, scssLoader, images, guildMgr, hiddenScrollHtml],
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle'),
      confirmDialog: () => import('~/components/confirmDialog')
    },
    props: {
      showGuildAcceptDialogStatus: { type: Boolean, required: true, default: false },
      playerInfo: { type: Object, default: '' }
    },
    data() {
      return {
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png',
        showGuildAcceptTmp: this.showGuildAcceptDialogStatus,
        guildConfirmStatus: false,
        guildSetupNotyStatus: false,
        guildSendNotyStatus: false,
        acceptNoty: '',
        disabledSubmit: false,
        playerInfoSelected: {},
        playerInfoList: []
      }
    },
    created() {
      this.playerInfoList.push(this.playerInfo)
      this.playerInfoSelected = this.playerInfoList[0]
    },
    async mounted() {
      if (this.guildRank > 1) await this.$store.dispatch('guild/fetchGuildInvitationContent')
    },
    async destroyed() {
      await this.$store.dispatch('maintain/fetch')
      if (this.maintainSystem[0].maintaining) return
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      contentHeight() {
        return 440
      },
      captionHeight() {
        const windowsVH = window.innerHeight - 389
        return windowsVH
      },
      templateText() {
        return this.$store.getters['guild/templateText']
      }
    },
    watch: {
      showGuildAcceptDialogStatus: {
        async handler(status) {
          if (this.maintainSystem[0].maintaining) {
            return
          }
          this.showGuildAcceptTmp = status
        }
      },
      acceptNoty: {
        async handler() {
          const validateFormName = 'acceptContent.*'
          const validate = await this.$validator.validate(validateFormName)
          this.disabledSubmit = !validate
        },
        deep: true
      }
    },
    methods: {
      closeDialog() {
        this.showGuildAcceptTmp = false
        this.$emit('update:showGuildAcceptDialogStatus', false)
      },
      tryCloseDialog() {
        if (this.acceptNoty) this.guildConfirmStatus = true
        else this.closeDialog()
      },
      useTemplateNoty(userName) {
        if (this.acceptNoty) this.guildSetupNotyStatus = true
        else this.acceptNoty = this.templateText.format({ player: userName })
      },
      clearNoty() {
        this.acceptNoty = ''
      },
      tryAcceptClick() {
        this.guildSendNotyStatus = true
      },
      async onAcceptClick(userName, acceptNoty) {
        await this.acceptGuildMembership(userName, acceptNoty)
        this.closeDialog()
      }
    }
  }
</script>

<style lang="scss" scoped>
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $dialog-fill: map-get($colors, 'dialog-fill');
  $primary: map-get($colors, 'primary');

  .guild-edit-dialog-height-vertical {
    height: calc(var(--vh, 1vh) * 100 - 333px) !important;
  }
  .v-tabs-items {
    background-color: transparent !important;
  }
  .v-tab--active {
    color: $primary !important;
  }
  .container {
    height: 100vh; /* 將容器的高度設置為視窗的高度 */
    height: calc(var(--vh, 1vh) * 100);
  }
  .h-100-percent {
    height: 100% !important;
  }

  .accept-word-break {
    white-space: pre-wrap;
    word-break: keep-all;
    overflow-wrap: break-word;
  }

  .textarea-container {
    position: relative;
    width: 100%;
    .v-input {
      .v-input__control {
        .v-input__slot {
          .v-text-field__slot {
            margin-bottom: 58px !important;
          }
        }
      }
    }
  }
  .disable-hyphen ::v-deep .v-textarea {
    hyphens: none !important;
    -webkit-hyphens: none !important;
    -ms-hyphens: none !important;
    word-break: keep-all !important;
  }

  .template-button-wrapper {
    position: absolute;
    bottom: 36px; /* 調整這個值來控制按鈕與底部的距離 */
    left: 12px; /* 調整這個值來控制按鈕與左側的距離 */
    z-index: 1;
  }

  .template-btn {
    height: 36px !important;
    padding: 0 16px !important;
    background: transparent !important;
    border: 1px solid rgba(255, 255, 255, 0.7) !important;
  }

  .template-btn :deep(.v-btn__content) {
    text-transform: none;
    letter-spacing: normal;
  }

  .hide-arrow {
    :deep(.v-input__append-inner),
    :deep(.v-input__icon--append) {
      display: none !important;
      width: 0 !important;
      margin: 0 !important;
      padding: 0 !important;
    }
  }
  :deep(.v-text-field__slot) {
    margin-right: 0px !important;
  }
  :deep(.v-input__append-inner) {
    margin-top: 27px !important;
  }
  /* 確保 textarea 的內容不會被按鈕遮擋 */
  :deep(.v-text-field__slot textarea) {
    margin-bottom: 52px !important;
  }

  /* 確保文字不會被按鈕遮擋 */
  :deep(.v-text-field.v-text-field--enclosed .v-text-field__details) {
    margin-bottom: 0;
  }
</style>

<template>
  <div>
    <v-dialog v-model="dialogModel" persistent max-width="400px" content-class="rounded-lg">
      <v-card elevation="0" tile color="dialog-fill" class="pa-4 pa-sm-6">
        <v-card-title
          class="d-flex justify-center align-center text-h6 font-weight-regular custom-text-noto grey-1--text pa-0"
        >
          {{ $t('hint') }}
        </v-card-title>
        <v-card-text
          class="default-content--text text-body-2 custom-text-noto px-0 py-6"
          v-html="notyText"
        />
        <v-card-actions class="pa-0">
          <v-spacer />
          <v-btn
            depressed
            :color="$UIConfig.defaultBtnColor"
            :class="['button-content--text', breakpoint.xsOnly ? 'w-100' : '']"
            @click="closeDialog"
          >
            {{ $t('sure').toUpperCase() }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
  export default {
    name: 'redirectDemoModeDialog',
    props: {
      showRedirectDemoModeDialog: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      dialogModel: {
        get() {
          return this.showRedirectDemoModeDialog
        },
        set(value) {
          this.$emit('update:showRedirectDemoModeDialog', value)
        }
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },

    data() {
      return {
        notyText:
          '<p class="mb-0 text-wrap">' +
          this.$t('detect_not_login') +
          '</p>' +
          '<p class="mb-0 text-wrap">' +
          this.$t('free_trial_mode_has_been_opened_for_you') +
          '</p>' +
          '<br />' +
          '<p class="mb-0 text-wrap">※ ' +
          this.$t('free_trial_mode_noty') +
          '</p>'
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showRedirectDemoModeDialog', false)

        // 使用 nextTick 確保狀態更新後再觸發導航
        this.$nextTick(() => {
          this.$root.$emit('redirect-demo-dialog-closed')
        })
      }
    }
  }
</script>

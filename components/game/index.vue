<template>
  <v-container fluid v-show="!maintainSystem[0].maintaining && !status404">
    <!-- title -->
    <v-row align="center" justify="center">
      <div class="pt-2 pt-md-0 pt-lg-0 pt-xl-0 pb-6">
        <linearGradientTitle :title="linearGradientTitleText" />
      </div>
    </v-row>
    <!-- game list card -->

    <v-row justify="end" class="pt-6 align-top px-2" :class="[$UIConfig.gamePage.background]">
      <!-- filter -->
      <v-col cols="12" sm="4" md="3" lg="3" xl="2" class="py-0">
        <v-select
          v-model="gameSortType"
          :label="$t('sort_label')"
          rounded
          outlined
          dense
          attach
          height="40px"
          :items="gameHallSortList"
          item-value="sortType"
          item-text="name"
          :hide-details="$vuetify.breakpoint.xsOnly"
          @change="selectedGameSortEvent"
        />
      </v-col>
      <!-- provider -->
      <v-col
        cols="12"
        sm="4"
        md="3"
        lg="3"
        xl="2"
        class="py-0 mt-xl-0 mt-lg-0 mt-md-0 mt-sm-0 mt-4"
      >
        <v-select
          v-model="providerId"
          :key="providerId"
          :label="$t('provider_label')"
          rounded
          outlined
          attach
          dense
          height="40px"
          :items="gameProviders"
          item-value="id"
          item-text="brand"
          :hide-details="$vuetify.breakpoint.xsOnly"
          @change="handleProviderClickEvent"
        >
          <template v-slot:item="{ item, on, attrs }">
            <v-list-item v-bind="attrs" v-on="on" @click="handleSameProviderClickEvent(item.id)">
              <v-list-item-content>
                <v-list-item-title>{{ item.brand }}</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </template>
        </v-select>
      </v-col>
      <!-- search -->
      <v-col
        cols="12"
        sm="4"
        md="3"
        lg="3"
        xl="2"
        class="py-0 mt-xl-0 mt-lg-0 mt-md-0 mt-sm-0 mt-4"
      >
        <v-text-field
          v-model="searchWord"
          ref="searchWord"
          append-icon="mdi-magnify"
          clearable
          outlined
          rounded
          dense
          :label="$t('search_games')"
          class="input-height"
          @click:clear="clearSearchEvent"
          @click:append="searchWordEvent"
          @keydown.enter="searchWordEvent"
        />
      </v-col>
      <!-- game list -->
    </v-row>

    <v-row
      v-if="gameHallList.length > 0"
      class="text-left align-top justify-start px-2"
      :class="[$UIConfig.gamePage.background]"
    >
      <v-col
        v-for="(game, index) in gameHallList"
        v-show="showEnableStatus(game.enable)"
        :key="`${index}`"
        cols="6"
        sm="4"
        md="3"
        lg="2"
        class="pa-0"
      >
        <gameCard :game="game" />
      </v-col>
    </v-row>
    <!-- no_results_found notice -->

    <v-row
      v-else
      align="center"
      justify="center"
      :class="[$UIConfig.gamePage.background, 'min-height-120px']"
    >
      <span
        class="custom-text-noto text-caption grey-3--text py-4"
        style="font-size: 12px !important"
        >{{ $t('no_results_found') }}
      </span>
    </v-row>
    <!-- pagination -->

    <v-row v-if="pageTotal" :class="[$UIConfig.gamePage.background]">
      <v-col class="text-center">
        <v-pagination
          v-model="page"
          :length="pageTotal"
          total-visible="7"
          circle
          :color="$UIConfig.defaultBtnColor"
          @input="changePageEvent"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
  /**
   * 遊戲頁面更新機制流程：
   *
   * 1. 初始化階段：
   *    - created hook 中調用 initializeWithDefaults() 初始化頁面
   *    - 檢查 URL 參數是否存在所有必要參數 (gameCategory, gameSortType, searchWord, providerId, page)
   *    - 如參數不完整則添加預設值並更新 URL
   *    - 如參數已完整則直接使用並獲取遊戲列表
   *
   * 2. 數據監聽階段：
   *    - 監聽路由查詢參數變化 ($route.query)，觸發重新初始化
   *    - 監聽遊戲列表變化 (showGameList)，更新頁面數據並獲取 RTP 資訊
   *
   * 3. 用戶交互處理：
   *    - 排序變更：selectedGameSortEvent() 更新排序類型並重置頁碼
   *    - 供應商選擇：handleProviderClickEvent() 更新供應商 ID 並重置頁碼
   *    - 關鍵字搜索：searchWordEvent() 更新搜索詞並重置頁碼
   *    - 清除搜索：clearSearchEvent() 清空搜索詞並重置頁碼
   *    - 分頁切換：changePageEvent() 只更新頁碼參數
   *
   * 4. URL 更新機制：
   *    - 所有交互都通過 updatePageQuery() 更新 URL 查詢參數
   *    - 保留現有查詢參數，僅更新需要變更的參數
   *    - 觸發路由變化，進而觸發數據重新獲取
   *
   * 5. 數據獲取流程：
   *    - fetchGameListHandler() 根據最新參數獲取遊戲列表
   *    - fetchGameRtpHandler() 獲取每個遊戲的 RTP 資訊並合併到遊戲數據中
   *
   * 6. 供應商有效性檢查：
   *    - handleProviderStatus() 檢查所選供應商是否有效
   *    - 如供應商維護中，顯示警告並重置為全部供應商 (providerId = 0)
   */
  import gameList from '@/mixins/gameList'
  import analytics from '@/mixins/analytics'
  import scssLoader from '@/mixins/scssLoader.js'
  import _ from 'lodash'
  const STATION = process.env.STATION
  export default {
    mixins: [gameList, analytics, scssLoader],
    name: 'GameListIndex',
    components: {
      linearGradientTitle: () => import(`~/components_station/${STATION}/linearGradientTitle`),
      gameCard: () => import('~/components/game/gameCard')
    },
    data() {
      const gameHallSortList = [
        {
          sortType: 1,
          name: this.$t('all_game')
        },
        {
          sortType: 2,
          name: this.$t('hot_games')
        },
        {
          sortType: 3,
          name: this.$t(this.$UIConfig.gamePage.sortGameName)
        }
      ]

      // 路由參數預設值
      const defaultValues = {
        page: 1,
        gameCategory: '', // 預設值，會在 initializeWithDefaults 中動態更新
        gameSortType: 3,
        providerId: 0,
        searchWord: ''
      }

      return {
        gameHallSortList,
        gameHallList: [],
        gameDefault: require(`~/assets/image/${STATION}/game/game_default.webp`),
        showItemLimit: 36,
        page: 1,
        gameSortType: 1,
        searchWord: '',
        providerId: 0,
        gameCategory: '',
        defaultValues
      }
    },
    computed: {
      maintainSystem() {
        return this.$store.getters['maintain/system']
      },
      status404() {
        return this.$store.getters['maintain/status404']
      },
      linearGradientTitleText() {
        const category = this.currentCategory(this.gameCategory)
        if (category) {
          return this.$t(category.dict + '_game').toUpperCase()
        }
        return ''
      },
      gameListTotal({ $store }) {
        return $store.getters['gameHall/gameListTotal']
      },
      gameCategoryList({ $store }) {
        return $store.getters['gameProvider/gameCategory']
      },
      gameProviders({ $store }) {
        const currentCategoryId = Number(this.$route.query.gameCategory)

        // 使用常量儲存預設選項，提高可讀性
        const allProvider = {
          id: 0,
          brand: this.$t('all_provider')
        }

        // 使用陣列方法鏈優化過濾和排序邏輯
        const filteredProviders = $store.getters['gameProvider/providers']
          // 使用有效的類別過濾
          .filter((item) => item.categoryTypes?.includes(currentCategoryId))
          // 使用本地化比較函數進行排序
          .sort((a, b) => a.brand.localeCompare(b.brand, this.$i18n.locale))

        // 使用展開運算符合併結果
        return [allProvider, ...filteredProviders]
      },
      showGameList({ $store }) {
        return $store.getters['gameHall/gameList']
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      pageTotal() {
        return Math.ceil(this.gameListTotal / this.showItemLimit)
      },
      offset() {
        return (this.page - 1) * this.showItemLimit
      }
    },
    watch: {
      showGameList: {
        async handler(val) {
          this.$nuxt.$loading.start()
          const gameList = val.map((game) => ({
            ...game,
            categoryType: this.gameCategory
          }))

          // 處理非第一頁但遊戲清單為空的情況
          if (this.page !== 1 && gameList.length === 0) {
            this.page = this.defaultValues.page
            this.updatePageQuery({ page: this.page })
            this.$nuxt.$loading.finish()
            return
          }

          this.gameHallList = await this.fetchGameRtpHandler(gameList)
          this.$nuxt.$loading.finish()
        },
        deep: true
      },
      '$route.query': {
        handler(newQuery) {
          this.initializeWithDefaults(newQuery)
        },
        deep: true
      }
    },
    created() {
      if (process.client) {
        const currentQuery = this.$route.query
        this.initializeWithDefaults(currentQuery)
      }
    },
    beforeDestroy() {
      this.$store.commit('gameHall/SET_GAMELIST', [])
    },
    methods: {
      // 初始化頁面參數
      async initializeWithDefaults(currentQuery) {
        // 確保 gameProviders、gameCategoryList 是最新資料
        await this.$store.dispatch('gameProvider/fetch')

        // 若遊戲類別參數不存在、為空值，則不進行有效值驗證（之後會改塞預設值）
        if (currentQuery.gameCategory !== undefined && currentQuery.gameCategory !== null) {
          // 取得當前遊戲類別（gameCategory）資訊
          const gameCategoryInfo = this.gameCategoryList.find(
            (item) => item.code === Number(currentQuery.gameCategory)
          )
          // 找不到遊戲類別或 enable 為 false，都轉跳回首頁並顯示錯誤訊息
          if (!gameCategoryInfo?.enable) {
            this.$notify.error(this.$t('not_found_game_lobby'))
            this.$router.push('/')
            return
          }
        }

        // 更新 gameCategory 為有效值
        this.defaultValues.gameCategory = this.getFirstEnabledGameCategory()

        // 是否需要重定向
        let needsUpdate = false
        const updatedQuery = { ...currentQuery }

        // 第一步：檢查並設置預設值（檢查每個預設值是否存在於當前的 query 中，如果不存在則添加）
        Object.keys(this.defaultValues).forEach((key) => {
          // 檢查參數是否存在
          const paramExists =
            // 檢查searchWord參數使否存在並允許null
            (key === 'searchWord' && currentQuery[key] !== undefined) ||
            // 檢查providerId參數使否存在並允許0值
            (key === 'providerId' && (currentQuery[key] === 0 || currentQuery[key] === '0')) ||
            // 檢查其他參數使否存且不為null
            (currentQuery[key] !== undefined && currentQuery[key] !== null)
          if (!paramExists) {
            updatedQuery[key] = this.defaultValues[key]
            needsUpdate = true
          }
        })

        // 第二步：驗證參數有效性並重置無效參數
        const page = Number(updatedQuery.page)
        const gameCategory = Number(updatedQuery.gameCategory)
        const gameSortType = Number(updatedQuery.gameSortType)
        const providerId = Number(updatedQuery.providerId)
        const searchWord = updatedQuery.searchWord || ''

        // 統一驗證（page、gameSortType、providerId）並重置無效參數
        const gameSortTypeList = new Set(this.gameHallSortList.map((item) => item.sortType))
        const validProviderId = this.checkoutProviderValid(providerId)

        const validations = [
          {
            // 檢查頁碼（page）是否有效（只有正整數算有效）
            condition: !page || !Number.isInteger(page) || page < 1,
            reset: () => {
              updatedQuery.page = this.defaultValues.page
            }
          },
          {
            // 檢查遊戲排序（gameSortType）是否有效
            condition: !gameSortTypeList.has(gameSortType),
            reset: () => {
              updatedQuery.gameSortType = this.defaultValues.gameSortType
            }
          },
          {
            // 檢查供應商（providerId）是否有效
            condition: validProviderId === null,
            reset: () => {
              this.$notify.warning(this.$t('provider_maintenance_message'))
              updatedQuery.providerId = this.defaultValues.providerId
            }
          }
        ]

        // 執行所有驗證並重置無效參數
        validations.forEach(({ condition, reset }) => {
          if (condition) {
            needsUpdate = true
            reset()
          }
        })

        // 統一處理重新導向或更新組件數據
        if (needsUpdate) {
          this.updatePageQuery(updatedQuery)
        } else {
          // 直接更新組件數據，不需要再次驗證
          this.page = page
          this.gameCategory = gameCategory
          this.gameSortType = gameSortType
          this.providerId = providerId
          this.searchWord = searchWord

          // 取得遊戲列表
          await this.fetchGameListHandler()
        }
      },
      // 更新分頁參數
      updatePageQuery(params) {
        const currentQuery = this.$route.query
        const updatedQuery = { ...currentQuery, ...params }
        const isEqual = _.isEqual(currentQuery, updatedQuery)
        // 如果查詢參數有變更
        if (!isEqual) {
          // 更新路由查詢參數,這會觸發路由變化
          this.$router.push({ query: updatedQuery })
        } else {
          // 如果查詢參數沒有變更,直接使用當前參數重新初始化
          this.initializeWithDefaults(updatedQuery)
        }
      },
      // 更新遊戲列表
      async fetchGameListHandler() {
        if (this.gameCategoryList.length > 0) {
          await this.fetchGameList(
            this.gameCategory,
            this.gameSortType,
            this.offset,
            this.showItemLimit,
            this.searchWord,
            this.providerId
          )
          // 如果搜尋關鍵字不為空，則發送搜尋關鍵字到 analytics
          if (!this.stringNullOrEmpty(this.searchWord)) {
            this.sendGameSearchAnalytics(this.gameHallList, this.searchWord)
          }
        }
      },
      // 更新遊戲列表
      async fetchGameList(gameCategoryId, sortType, offset, limit, keyword, providerId) {
        const lang = this.$i18n.locale
        let params = {
          gameCategoryId,
          sortType,
          lang
        }
        if (offset) {
          params.offset = offset
        }
        if (limit) {
          params.limit = limit
        }
        if (keyword && keyword !== '') {
          params.keyword = keyword
        }
        if (providerId) {
          params.providerId = providerId
        }

        await this.$store.dispatch('gameHall/fetchGameList', params)
      },
      // 取得RTP
      async fetchGameRtpHandler(gameSourceList) {
        // 檢查輸入參數
        if (!gameSourceList?.length) {
          return gameSourceList
        }

        // 使用 map 直接提取 gameIds
        const gameIds = gameSourceList.map((game) => game.id)

        try {
          // 獲取 RTP 數據
          const { list: rtpList = [] } = (await this.$clientApi.game.gameRTPList(gameIds)) || {}

          // 如果沒有 RTP 數據，直接返回原列表
          if (!rtpList?.length) {
            return gameSourceList
          }

          // 使用 Map 結構來提高查找效率 - O(1) vs O(n)
          const rtpMap = new Map(rtpList.map((rtpItem) => [rtpItem.gameId, rtpItem]))
          // 合併 RTP 數據到遊戲列表
          return gameSourceList.map((game) => {
            const rtpData = rtpMap.get(game.id)
            if (rtpData) {
              // eslint-disable-next-line no-unused-vars
              const { gameId, ...rtpInfo } = rtpData
              return { ...game, ...rtpInfo }
            }
            return game
          })
        } catch (error) {
          console.error('RTP 數據獲取失敗:', error)
          // 錯誤情況下返回原始列表
          return gameSourceList
        }
      },
      // 處理供應商狀態
      async handleProviderStatus(id) {
        // 確保 gameProviders 是最新資料
        await this.$store.dispatch('gameProvider/fetch')
        const providerId = this.checkoutProviderValid(id)
        if (providerId === null) {
          this.$notify.warning(this.$t('provider_maintenance_message'))
          this.providerId = this.defaultValues.providerId
        } else {
          this.providerId = providerId
        }
      },
      // 選擇排序事件
      async selectedGameSortEvent() {
        this.page = this.defaultValues.page
        await this.handleProviderStatus(this.providerId)
        this.updatePageQuery({
          page: String(this.page),
          providerId: String(this.providerId),
          gameSortType: String(this.gameSortType)
        })
      },
      // 選擇供應商事件
      async handleProviderClickEvent(id) {
        this.page = this.defaultValues.page
        await this.handleProviderStatus(id)
        this.updatePageQuery({ page: String(this.page), providerId: String(this.providerId) })
      },
      handleSameProviderClickEvent(id) {
        if (id === this.providerId) this.handleProviderClickEvent(id)
      },
      // 關鍵字搜尋事件
      async searchWordEvent() {
        this.page = this.defaultValues.page
        await this.handleProviderStatus(this.providerId)
        this.updatePageQuery({
          page: String(this.page),
          providerId: String(this.providerId),
          searchWord: this.searchWord
        })
        // 加入此行，解決手機不會收起鍵盤的問題
        this.$refs.searchWord.blur()
      },
      // 清除關鍵字事件
      async clearSearchEvent() {
        this.page = this.defaultValues.page
        await this.handleProviderStatus(this.providerId)
        this.searchWord = this.defaultValues.searchWord
        this.updatePageQuery({
          page: this.page,
          providerId: String(this.providerId),
          searchWord: this.defaultValues.searchWord
        })
      },
      // 切換分頁事件
      changePageEvent() {
        this.updatePageQuery({ page: String(this.page) })
        //在 Safari 瀏覽器中，window.scrollTo()方法的behavior參數不支援'smooth'值，只支援'auto'和'instant'兩個值。
        window.scrollTo({ top: 0, behavior: 'auto' })
      },
      // 發送遊戲搜尋分析
      sendGameSearchAnalytics(gameList, searchWord) {
        if (!this.stringNullOrEmpty(searchWord)) {
          this.gameSearchAnalytics({
            username: this.userName,
            content: searchWord,
            gameIds: gameList.map((item) => item.id),
            at: this.$moment().format('YYYY-MM-DDTHH:mm:ssZ')
          })
        }
      },
      // 檢查供應商是否有停用
      checkoutProviderValid(providerId) {
        const providerIds = this.gameProviders.map((provider) => provider.id)
        return providerIds.includes(providerId) ? providerId : null
      },
      currentCategory(categoryId) {
        // 排除無效參數情況
        if (categoryId === undefined || categoryId === null || !this.gameCategoryList?.length) {
          return null
        }
        const numCategoryId = Number(categoryId)
        return this.gameCategoryList.find((item) => Number(item.code) === numCategoryId) || null
      },
      // 檢查字串是否為空
      stringNullOrEmpty(word) {
        return word === null || word === undefined || word === ''
      },
      // 取得第一個啟用的遊戲類別
      getFirstEnabledGameCategory() {
        if (!this.gameCategoryList || this.gameCategoryList.length === 0) return ''
        const firstEnabledCategory = this.gameCategoryList.find((category) => category.enable)
        return firstEnabledCategory?.code || ''
      }
    }
  }
</script>

<style lang="scss" scoped>
  .min-height-120px {
    min-height: 120px !important;
  }

  // 這style目前只有華義用到
  $card-fill-color: map-get($colors, card-fill);
  .bg-color {
    background-color: rgba($card-fill-color, 0.4);
  }

  // 修復 v-select 選項文字被切掉的問題
  ::v-deep .v-select__selections {
    line-height: 1.5 !important;
    min-height: 24px !important;
  }

  ::v-deep .v-list-item__title {
    line-height: 1.5 !important;
    padding: 4px 0 !important;
  }

  ::v-deep .v-list-item {
    min-height: 36px !important;
    padding: 4px 16px !important;
  }

  ::v-deep .v-list-item__content {
    padding: 4px 0 !important;
  }

  // 修復 v-text-field 字體被切掉的問題
  ::v-deep .v-text-field--dense .v-input__control {
    min-height: 40px !important;
  }

  ::v-deep .v-text-field--dense .v-text-field__details {
    min-height: 16px !important;
  }

  ::v-deep .v-text-field input {
    line-height: 1.5 !important;
    padding: 8px 0 !important;
  }

  ::v-deep .v-text-field--outlined .v-input__control {
    min-height: 40px !important;
  }

  ::v-deep .v-text-field--outlined.v-text-field--dense .v-label {
    top: 6px !important;
  }

  ::v-deep .v-text-field--outlined.v-text-field--dense.v-text-field--placeholder .v-label {
    top: 6px !important;
  }
</style>

<template>
  <v-container v-if="isShow" fluid class="pa-0">
    <!-- featured games -->
    <v-row no-gutters v-for="groupItem in propGameList.group" :key="groupItem" class="mt-2">
      <v-col cols="12" :class="{ 'notch-right': hasRightNotch }">
        <v-row no-gutters align="center">
          <span
            class="font-weight-bold text-h5 gradient-primary--text custom-text-noto mr-2 mr-sm-4 title-heavy--text"
          >
            {{ setBlockTitle(groupItem) }}
          </span>
          <gradientDivider />
        </v-row>
      </v-col>
      <swiper-custom
        class="mt-1"
        :game-list="filterSelectedSeriesList(groupItem)"
        :swiper-slide-style="swiperSlideStyle"
        :show-slide-btn="showSlideBtn"
        :btn-position="btnPosition"
      >
        <template v-slot:card="data">
          <specialGameCard :game="data.game" />
        </template>
      </swiper-custom>
    </v-row>
  </v-container>
</template>
<script>
  import gameRelate from '@/mixins/gameRelate.js'
  import orientation from '@/mixins/orientation.js'

  export default {
    name: 'FeaturedGameList',
    mixins: [gameRelate, orientation],
    components: {
      gradientDivider: () => import('~/components/gradientDivider.vue'),
      swiperCustom: () => import('~/components/swiperCustom.vue'),
      specialGameCard: () => import('~/components/game/specialGameCard.vue')
    },
    props: {
      propGameList: {
        type: Object,
        required: true
      },
      blockTitleCapital: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        // 是否顯示左右按鈕
        showNavigationButtons: true,
        // 左右按鈕的位置
        btnPosition: {
          left: '-15px',
          right: '-15px',
          top: '50%'
        }
      }
    },
    computed: {
      isShow() {
        return this.$UIConfig.lock.specialGame.featured
      },
      swiperSlideStyle() {
        const breakWidth = this.$vuetify.breakpoint.width
        const minWidth =
          breakWidth >= 1264 ? this.$UIConfig.swiperBox.featuredGameCardMinWidth.lg : ''
        const width =
          breakWidth >= 1264
            ? this.$UIConfig.swiperBox.featuredGameCardWidth.lg
            : breakWidth >= 960
            ? this.$UIConfig.swiperBox.featuredGameCardWidth.md
            : breakWidth >= 600
            ? this.$UIConfig.swiperBox.featuredGameCardWidth.sm
            : this.$UIConfig.swiperBox.featuredGameCardWidth.xs
        const body = { boxSizing: 'border-box', minWidth, width }

        return body
      },
      showSlideBtn() {
        const seriesListLength = this.propGameList.data.length
        return (
          this.showNavigationButtons &&
          this.$vuetify.breakpoint.lgAndUp &&
          // 卡片張數過少時不顯示左右滑動按鈕
          (this.$vuetify.breakpoint.width <= 1270 ? seriesListLength > 2 : seriesListLength > 3)
        )
      }
    },
    beforeDestroy() {
      this.$store.commit('gameHall/SET_ALL_GAME_LIST', [])
    },
    methods: {
      filterSelectedSeriesList(groupItem) {
        return this.propGameList.data.filter((game) => game.group === groupItem)
      },
      setBlockTitle(key) {
        return this.blockTitleCapital ? this.$t(key).toUpperCase() : this.$t(key)
      }
    }
  }
</script>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>

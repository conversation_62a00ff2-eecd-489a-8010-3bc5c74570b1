<template>
  <v-card class="game-card-layout transparent" elevation="0">
    <!-- background color -->
    <div class="game-card-background fill-height pt-6">
      <div
        class="game-card-background-color fill-height elevation-6"
        :style="{ background: game.bgColor }"
      />
    </div>
    <!-- game cover -->
    <div class="game-card-cover px-4 pt-9">
      <v-row no-gutters>
        <v-col cols="4" class="pr-4">
          <v-img class="cover elevation-8" :src="game.gameCover" @error="setAltGameCover"></v-img>
        </v-col>
      </v-row>
    </div>
    <!-- game img -->
    <div class="game-card-game-img rounded">
      <v-img eager class="rounded" :src="localGameImg" @error="setAltGameImg"></v-img>
    </div>
    <!-- game info -->
    <div class="game-card-game-info d-flex flex-column">
      <div class="game-card-text px-4">
        <div class="d-flex flex-column">
          <div
            class="game-card-game-title font-italic text-left white--text"
            :class="[gameCardConfig.fontWeight]"
            :style="{ 'font-size': titleFontSize }"
          >
            {{ game.title }}
          </div>
          <div class="game-card-rtp-chip d-flex align-top mt-2">
            <rtpShow
              v-if="!game.maintaining && showMainDailyRtp && showRTPStyle.dailyShowStatus"
              :background-color="$UIConfig.replaceColor.bgGamePromoteRtp"
              :rtp="game.dailyRtp"
              :icon="showRTPStyle.dailyIcon"
              :icon-color="showRTPStyle.dailyIconColor"
              :is-outer-hover="true"
              :chip-text-color="$UIConfig.replaceColor.textGamePromoteRtp"
            />
          </div>
        </div>
      </div>
      <v-row no-gutters align="center" class="game-card-action mt-1 pt-2 pb-3 px-4">
        <template v-if="isLogin">
          <v-col class="mr-2">
            <v-hover v-slot="{ hover }">
              <v-btn
                block
                rounded
                :depressed="hover"
                :outlined="!hover"
                :color="$UIConfig.replaceColor.white"
                :small="gameCardConfig.sortCategoryNameAndButton"
                :class="[
                  hover ? $UIConfig.replaceColor.bgBtnHeavy : '',
                  hover ? $UIConfig.replaceColor.textBtnHeavyText : ''
                ]"
                @click="startGameWithClick('play')"
                :disabled="disabledStatus || isVipLevelLimit"
              >
                {{ $t('play') }}
              </v-btn>
            </v-hover>
          </v-col>
          <v-col>
            <v-btn
              block
              rounded
              text
              :color="$UIConfig.replaceColor.white"
              :small="gameCardConfig.sortCategoryNameAndButton"
              @click="startGameWithClick('demo')"
              :disabled="disabledStatus"
            >
              {{ $t('freePlay') }}
            </v-btn>
          </v-col>
        </template>
        <template v-else>
          <v-col>
            <v-hover v-slot="{ hover }">
              <v-btn
                block
                rounded
                :depressed="hover"
                :outlined="!hover"
                :color="$UIConfig.replaceColor.white"
                :small="gameCardConfig.sortCategoryNameAndButton"
                :class="[
                  hover ? $UIConfig.replaceColor.bgBtnHeavy : '',
                  hover ? $UIConfig.replaceColor.textBtnHeavyText : ''
                ]"
                @click="startGameWithClick('demo')"
                :disabled="disabledStatus"
              >
                {{ $t('freePlay') }}
              </v-btn>
            </v-hover>
          </v-col>
        </template>
        <template>
          <span
            :class="['cursor-pointer', 'material-symbols-outlined']"
            class="default-content--text ml-2 white--text"
            style="font-size: 20px"
            @click="openGameIntroDialog()"
          >
            info
          </span>
        </template>
      </v-row>
      <v-divider />
      <v-row no-gutters justify="space-between" align="center" class="game-card-empty-space px-4">
      </v-row>
    </div>
    <!-- maintaining overlay -->
    <div v-if="game.maintaining" class="game-card-maintain-overlay fill-height pt-4">
      <div
        class="game-card-background-color fill-height gradient-game-maintenance-2 overlay-game-promote"
      >
        <v-row no-gutters class="fill-height" align="center" justify="center">
          <span class="material-symbols-outlined white--text" style="font-size: 60px">
            construction
          </span>
        </v-row>
      </div>
    </div>
    <!-- back to lobby -->
    <div class="game-card-back-lobby d-flex align-end">
      <v-row
        no-gutters
        justify="space-between"
        align="center"
        class="game-card-go-to-lobby cursor-pointer px-4 py-2"
        @click="goToLobbyWithGameCategory(game.category)"
      >
        <div
          v-if="gameCardConfig.sortCategoryNameAndButton"
          class="game-card-category-text font-weight-bold text-caption custom-text-noto text-left white--text"
        >
          {{ $t(game.category + '_short').toUpperCase() }}
        </div>
        <div
          v-else
          class="game-card-category-text font-weight-bold text-caption custom-text-noto text-left white--text"
        >
          {{ $t(game.category + '_game').toUpperCase() }}
        </div>
        <div class="game-card-go-to-lobby-text font-weight-medium white--text">
          <v-row no-gutters align="center">
            {{ $t('go_to_lobby').toUpperCase() }}
            <span class="material-symbols-outlined ml-1"> arrow_forward </span>
          </v-row>
        </div>
      </v-row>
    </div>
    <notyNotRealMember
      v-if="showNotyNotRealMemberDialogStatus"
      :show-noty-not-real-member-dialog-status.sync="showNotyNotRealMemberDialogStatus"
    />
    <bothRobotExpNoty
      v-if="showNotyBothRobotExpNotyDialogStatus.show"
      :show-noty-both-robot-exp-noty-dialog-status.sync="showNotyBothRobotExpNotyDialogStatus"
    />
    <noExpGainNoty
      v-else-if="showNotyNoExpGainNotyDialogStatus.show"
      :show-noty-no-exp-gain-noty-dialog-status.sync="showNotyNoExpGainNotyDialogStatus"
    />
    <hasRobotNoty
      v-else-if="showNotyHasRobotNotyDialogStatus.show"
      :show-noty-has-robot-noty-dialog-status.sync="showNotyHasRobotNotyDialogStatus"
    />
    <gameIntro
      v-if="showGameIntroDialogStatus"
      :show-game-intro-dialog-status.sync="showGameIntroDialogStatus"
      :game="game"
    />
  </v-card>
</template>

<script>
  import analytics from '@/mixins/analytics.js'
  import gameRelate from '@/mixins/gameRelate.js'
  import utilsGame from '@/utils/game.js'
  const STATION = process.env.STATION
  const playGame = require(`~/mixins_station/${STATION}/playGame`).default
  export default {
    name: 'specialGameCard',
    mixins: [analytics, gameRelate, playGame],
    components: {
      rtpShow: () => import(`~/components_station/${STATION}/rtp/rtpShow`),
      bothRobotExpNoty: () => import('~/components/notifications/bothRobotExpNoty.vue'),
      notyNotRealMember: () => import('~/components/notifications/notyNotRealMember.vue'),
      noExpGainNoty: () => import('~/components/notifications/noExpGainNoty.vue'),
      hasRobotNoty: () => import('~/components/notifications/hasRobotNoty.vue'),
      gameIntro: () => import(`~/components_station/${STATION}/game/gameIntro.vue`)
    },
    props: {
      game: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        disabledStatus: false,
        showNotyNotRealMemberDialogStatus: false,
        gameDefaultFeaturedImg: this.$store.getters['gameHall/gameDefaultFeaturedImg'],
        gameDefaultFeaturedImgPng: this.$store.getters['gameHall/gameDefaultFeaturedImgPng'],
        localGameImg: '',
        showGameIntroDialogStatus: false
      }
    },
    computed: {
      gameCardConfig() {
        return this.$UIConfig.specialGameCard
      },
      titleFontSize() {
        let fontSize = '2vw'
        const isTitleExceeds8 = this.game.title?.length > 8
        if (this.$vuetify.breakpoint.lgAndUp) {
          isTitleExceeds8 ? (fontSize = '1.6vw') : (fontSize = '2vw')
        } else if (this.$vuetify.breakpoint.mdOnly) {
          isTitleExceeds8 ? (fontSize = '24px') : (fontSize = '34px')
        } else if (this.$vuetify.breakpoint.smOnly) {
          isTitleExceeds8 ? (fontSize = '20px') : (fontSize = '29px')
        } else if (this.$vuetify.breakpoint.xsOnly) {
          isTitleExceeds8 ? (fontSize = '24px') : (fontSize = '33px')
        }
        return fontSize
      },
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      openGameLock({ $store }) {
        return $store.getters['gameHall/openGameLock']
      },
      gameCategoryExternalInfo({ $store }) {
        return $store.getters['gameProvider/gameCategoryExternalInfo']
      },
      isVipLevelLimit() {
        return (
          this.vipLevel !== 0 &&
          ((this.game.vipLevel === 1 && this.level < 10) ||
            (this.game.vipLevel !== 99 && this.vipLevel < this.game.vipLevel))
        )
      },
      hasRtp() {
        return this.game.rtp !== -1
      },
      hasRobot() {
        return this.game.hasRobot !== 0
      },
      hasExp() {
        return this.game.hasExp !== 0
      },
      showMainDailyRtp() {
        return this.game.rtp !== -1
      },
      defaultRtp() {
        return this.game.rtp
      },
      showRTPStyle() {
        return utilsGame.showRTPStyle({
          defaultRtp: this.defaultRtp,
          dailyRtp: this.game.dailyRtp,
          weeklyRtp: this.game.weeklyRtp,
          monthlyRtp: this.game.monthlyRtp,
          config: this.$UIConfig
        })
      }
    },
    watch: {
      openGameLock: {
        handler(val) {
          this.disabledStatus = val
        }
      },
      'game.gameImg': {
        handler(val) {
          if (val) this.localGameImg = val
        },
        immediate: true
      }
    },
    methods: {
      goToLobbyWithGameCategory(gameCategory) {
        let gameCode = this.gameCategoryExternalInfo.find((item) => item.dict === gameCategory).code
        // 調用 goToLobbyWithGameSortType 方法，但不傳入 gameSortType
        this.goToLobbyWithGameSortType(null, gameCode)
      },
      setAltGameImg(path) {
        // 確保如果路徑中不包含預設圖片路徑，才執行下面的邏輯
        if (
          !path.includes(this.gameDefaultFeaturedImg) &&
          !path.includes(this.gameDefaultFeaturedImgPng)
        ) {
          // 如果是 WebP 圖片且裝置不支援 WebP，則更換為 PNG 圖片
          if (this.localGameImg.endsWith('.webp')) {
            this.localGameImg = this.localGameImg.replace('.webp', '.png')
          }
          // 如果是 PNG 圖片，且依然無法載入，則更換為預設圖片
          else if (this.localGameImg.endsWith('.png')) {
            this.localGameImg = this.gameDefaultFeaturedImg
          }
        } else {
          // 如果路徑是預設圖片，則確保使用 PNG 格式的預設圖片s
          this.localGameImg = this.gameDefaultFeaturedImgPng
        }
      },
      setAltGameCover() {
        //若是裝置不支援webp 則改用png。
        this.game.gameCover = this.game.gameCover.replace('.webp', '.png')
      },
      openGameIntroDialog() {
        this.showGameIntroDialogStatus = true
      }
    }
  }
</script>
<style lang="scss" scoped>
  .game-card-layout {
    position: relative;
    width: 100%;
    min-width: 260px;
    // 寬:高 4:5 使用padding-bottom來設定高度
    padding-bottom: 125%;
    box-sizing: border-box;

    &:hover {
      .game-card-game-img {
        bottom: 0;
        left: 0;
        width: 100%;
        overflow: hidden;
        .v-image {
          transform: scale(1.05, 1.05);
        }
      }
    }
    .game-card-background {
      position: absolute;
      z-index: 1;
      bottom: 0;
      min-width: 260px;
      width: 100%;
      .game-card-background-color {
        border-radius: 20px 4px 4px 4px !important;
      }
    }
    .game-card-cover {
      position: absolute;
      z-index: 2;
      top: 0;
      left: 0;
      min-width: 260px;
      width: 100%;
      .cover {
        border-radius: 12px 12px 12px 0px;
      }
    }
    .game-card-game-img {
      position: absolute;
      bottom: 0;
      left: 0;
      z-index: 3;
      min-width: 260px;
      width: 100%;
    }
    .game-card-game-info {
      min-width: 260px;
      width: 100%;
      position: absolute;
      z-index: 4;
      bottom: 0;
      left: 0;
      .game-card-text {
        .game-card-game-title {
          line-height: 49.23px;
        }
        .game-card-rtp-chip {
          width: 90px;
          height: 24px;
        }
        .game-card-action {
          width: 100%;
          height: 60px;
        }
      }
      .game-card-empty-space {
        height: 36px;
      }
    }
    .game-card-maintain-overlay {
      min-width: 260px;
      width: 100%;
      position: absolute;
      z-index: 5;
      bottom: 0;
      left: 0;
    }

    .game-card-back-lobby {
      min-width: 260px;
      width: 100%;
      position: absolute;
      z-index: 6;
      bottom: 0;
      left: 0;
      .game-card-category-text {
        line-height: 20px;
      }
      .game-card-go-to-lobby-text {
        line-height: 20px;
        font-size: 11px;
        .material-symbols-outlined {
          font-size: 20px;
        }
      }
    }
  }
  .chipBgColor {
    background-color: rgba(0, 0, 0, 0.3) !important;
  }
  .no-exp-notice {
    background: rgba(97, 97, 97, 0.9);
  }
</style>

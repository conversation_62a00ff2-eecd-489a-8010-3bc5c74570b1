<template>
  <v-row no-gutters align="center" class="gradientDivider">
    <svg viewBox="0 0 10 10" class="squareColor" :width="squareSize">
      <path d="M5.5,0l-5,5l5,5L10,5.5v-1L5.5,0z M5.5,8.6L1.9,5l3.6-3.6L9.1,5L5.5,8.6z" />
    </svg>
    <v-divider class="primary-variant-2 border-regular"></v-divider>
    <template v-if="checkType('squareToArrow')">
      <span
        class="material-symbols-outlined primary-variant-2--text arrow-left border-regular--text"
        id="first-arrow-left"
      >
        chevron_right
      </span>
      <span
        class="material-symbols-outlined primary-variant-2--text arrow-left border-regular--text"
      >
        chevron_right
      </span>
      <span
        class="material-symbols-outlined primary-variant-2--text arrow-left border-regular--text"
        id="last-arrow-left"
      >
        chevron_right
      </span>
    </template>
    <template v-else-if="checkType('squareToSquare')">
      <svg viewBox="0 0 10 10" class="squareColor" transform="rotate(180)" :width="squareSize">
        <path d="M5.5,0l-5,5l5,5L10,5.5v-1L5.5,0z M5.5,8.6L1.9,5l3.6-3.6L9.1,5L5.5,8.6z" />
      </svg>
    </template>
  </v-row>
</template>

<script>
  export default {
    name: 'GradientDivider',
    props: {
      dividerType: {
        type: String,
        default: 'squareToArrow'
      },
      squareSize: {
        type: Number,
        default: 13
      }
    },
    methods: {
      checkType(type) {
        return this.dividerType === type
      }
    }
  }
</script>

<style lang="scss" scoped>
  $pv2-color: map-get($colors, primary-variant-2);
  $border-regular: map-get($colors, border-regular);

  .gradientDivider {
    position: relative;
    z-index: 1;

    .squareColor {
      fill: $pv2-color;
      //replaceColor
      fill: $border-regular;
    }
    .arrow-left {
      font-size: 19.5px;
      margin-left: -12px;
    }
    #first-arrow-left {
      @media screen and (max-width: 600px) {
        margin-left: -10px;
      }
    }
    #last-arrow-left {
      margin-right: -6px;
    }
  }
</style>

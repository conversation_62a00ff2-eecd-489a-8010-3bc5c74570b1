<template>
  <v-dialog
    v-model="dialogModel"
    class="ranking-hint-modal"
    content-class="rounded-lg"
    max-width="380"
  >
    <v-card>
      <v-list class="pa-6">
        <v-list-item class="pa-0">
          <v-list-item-content class="pa-0">
            <v-list-item-title class="text-center pb-6 text-h6 custom-text-noto grey-1--text">{{
              $t('reminder')
            }}</v-list-item-title>
            <p class="ma-0 mb-6 text-left text-body-2 default-content--text custom-text-noto">
              {{ $t('go_download_ask') }}？
            </p>
            <v-list-item-action class="ma-0 flex-row justify-end">
              <v-btn class="default-content--text" text max-width="62px" @click="closeDialog">{{
                $t('cancel')
              }}</v-btn>
              <v-btn
                class="ml-4"
                color="primary-variant-1 button-content--text"
                max-width="62px"
                @click="handleDownload"
                >{{ $t('sure') }}</v-btn
              >
            </v-list-item-action>
          </v-list-item-content>
        </v-list-item>
      </v-list>
    </v-card>
  </v-dialog>
</template>

<script>
  import analytics from '@/mixins/analytics.js'

  export default {
    name: 'LeaderboardAppRedirectDialog',
    mixins: [analytics],
    props: {
      showRedirectAppDialog: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      dialogModel: {
        get() {
          return this.showRedirectAppDialog
        },
        set(value) {
          this.$emit('update:showRedirectAppDialog', value)
        }
      },
      userDevice() {
        if (this.$device.isAndroid) {
          return 'phone_android'
        } else if (this.$device.isIos || this.$device.isMacOS) {
          return 'phone_ios'
        } else {
          return 'phone_pc'
        }
      }
    },
    methods: {
      closeDialog() {
        this.$emit('update:showRedirectAppDialog', false)
      },
      handleDownload() {
        if (this.userDevice === 'phone_android' || this.userDevice === 'phone_ios') {
          this.goApp('https://www.xin-stars.com/QPPandroid?action=apk')
        } else {
          this.goApp('https://www.xin-stars.com/Downloads')
        }
      },
      goApp(url) {
        this.closeDialog()
        this.$lineOpenWindow.open(url, '_blank')
      }
    }
  }
</script>

<template>
  <v-col class="pa-0">
    <swiper class="rank-swiper w-100" :options="swiperOption">
      <swiper-slide v-for="(flag, index) in setWinnerFlags" :key="index" class="flag-slide">
        <v-img
          :src="flag.webpSrc"
          :lazy-src="flag.pngSrc"
          :aspect-ratio="flagAspectRatio"
          contain
          class="flag-image w-100"
          @error="handleImageError(flag, index)"
        >
          <v-col class="flag-text w-100 h-100-percent px-4 py-0 text-center">
            <v-card-text
              @click="openPlayerInfo($event, flag.playerRankData.username)"
              class="score-player pa-0 pb-1 text-body-1 custom-text-noto text-truncate"
              contenteditable="false"
              >{{ flag?.playerRankData?.username }}</v-card-text
            >

            <v-card-text class="score-heading pa-0 d-flex justify-center align-center">
              {{ !!activeRank ? $t('win_multiplier') : $t('leaderboard_score') }}
              <div class="rect w-100 d-flex justify-space-between align-center">
                <span class="rect-left"></span>
                <span class="rect-right"></span>
              </div>
            </v-card-text>
            <v-card-text
              class="score-value custom-text-noto pa-0 text-truncate"
              contenteditable="false"
            >
              {{
                !!activeRank
                  ? flag?.playerRankData?.report?.odds
                  : flag?.playerRankData?.report?.winAmount
              }}
            </v-card-text>
            <div class="score-divide w-100 d-flex align-center">
              <div class="line"></div>
              <div class="rect w-100 d-flex justify-space-between align-center">
                <span class="rect-left"></span>
                <span class="rect-right"></span>
              </div>
            </div>
            <v-card-text
              class="pa-0"
              style="cursor: pointer"
              @click="openPlatformGame(flag.playerRankData)"
            >
              <div>
                <p
                  class="score-platform w-100 ma-0 d-flex justify-center align-center white--text text-no-wrap"
                >
                  {{
                    !!flag?.playerRankData?.platform ? $t('xincity_platform_web') : $t('xincity')
                  }}
                </p>
                <div class="score-info d-flex justify-start align-center pa-0 pt-1 ma-0">
                  <v-avatar tile>
                    <v-img :src="flag?.playerRankData?.game_logo_url"></v-img>
                  </v-avatar>
                  <span class="text-left text-body-2 custom-text-noto">
                    {{ flag?.playerRankData?.text }}
                  </span>
                </div>
              </div>
            </v-card-text>
          </v-col></v-img
        >
      </swiper-slide>
    </swiper>
    <div class="swiper-pagination" slot="pagination"></div
  ></v-col>
</template>

<script>
  import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
  import preLoginAction from '@/mixins/preLoginAction.js'
  import relationship from '~/mixins/relationship.js'
  import analytics from '@/mixins/analytics.js'
  import leaderboard from '~/mixins/leaderboard'

  export default {
    name: 'LeaderboardWinnerSwiper',
    components: {
      Swiper,
      SwiperSlide
    },
    mixins: [preLoginAction, relationship, analytics, leaderboard],
    props: {
      setWinnerFlags: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        swiperOption: {
          effect: 'coverflow',
          initialSlide: 1,
          grabCursor: true,
          centeredSlides: true,
          slidesPerView: 'auto',
          spaceBetween: 7,
          coverflowEffect: {
            rotate: 0,
            stretch: 0,
            depth: 0,
            modifier: 0,
            slideShadows: false
          },
          pagination: {
            el: '.swiper-pagination'
          }
        }
      }
    },
    computed: {
      flagAspectRatio() {
        // 旗幟的寬高比，小於460px時使用0.54/1，否則使用0.67/1
        return this.$vuetify.breakpoint.width <= 460 ? 0.54 : 0.67
      }
    },
    methods: {
      handleImageError(flag, index) {
        // 如果 WebP 加載失敗，強制使用 PNG 格式
        this.$set(this.setWinnerFlags[index], 'webpSrc', flag.pngSrc)
      },
      openPlatformGame(playerRankData) {
        this.$emit('open-platform-game', playerRankData)
      },
      openPlayerInfo(event, username) {
        this.$emit('open-player-info', event, username)
      }
    }
  }
</script>

<style lang="scss" scoped>
  $ranking-winner: map-get($colors, 'ranking-winner');
  $ranking-second: map-get($colors, 'ranking-second-place');
  $ranking-third: map-get($colors, 'ranking-third-place');
  $ranking-winner-50: map-get($colors, 'ranking-winner-opacity-50');
  $ranking-second-50: map-get($colors, 'ranking-second-place-opacity-50');
  $ranking-third-50: map-get($colors, 'ranking-third-place-opacity-50');
  $ranking-fontsize-base: (
    '.score-player': 1rem,
    '.score-heading': 12px,
    '.score-value': 1.25rem,
    '.score-platform': 10px,
    '.score-info span': 0.875rem
  );
  $ranking-fontsize-xs-560: (
    '.score-player': 14px,
    '.score-heading': 12px,
    '.score-value': 16px,
    '.score-platform': 10px,
    '.score-info span': 12px
  );

  // mixins
  @mixin ranking-color($color, $bg-color) {
    .flag-text {
      .score-player {
        color: $color;
        cursor: pointer;
      }
      .score-heading {
        color: $color;
        .rect {
          &-left,
          &-right {
            background-color: $color;
          }
        }
      }
      .score-heading::before,
      .score-heading::after {
        background-color: $color;
      }
      .score-value {
        color: $color;
      }
      .score-divide {
        background-color: $color;
        .line {
          background-color: $color;
        }
        .rect {
          &-left,
          &-right {
            background-color: $color;
          }
        }
      }
      .score-platform {
        background-color: $bg-color;
      }
      .score-info {
        span {
          color: $color;
        }
      }
    }
  }

  .flag-slide {
    // flags 文字顏色
    &:nth-child(1) {
      @include ranking-color($ranking-second, $ranking-second-50);
    }
    &:nth-child(2) {
      @include ranking-color($ranking-winner, $ranking-winner-50);
    }
    &:nth-child(3) {
      @include ranking-color($ranking-third, $ranking-third-50);
    }

    // flags 文字縮放
    .flag-text {
      @each $selector, $size in $ranking-fontsize-base {
        #{$selector} {
          font-size: calc(#{$size}) !important;
        }
      }
    }
  }

  .swiper-pagination.swiper-pagination-bullets {
    position: relative;
    height: 32px !important;
    z-index: 1;

    ::v-deep .swiper-pagination-bullet,
    .swiper-pagination-bullet-active {
      background: linear-gradient(180deg, #ffe7bd 0%, #f7b675 100%);
      margin: 0 4px;
    }
  }

  .rank-swiper {
    ::v-deep .swiper-wrapper {
      padding: 8px 0 !important;
    }
    .swiper-slide {
      display: flex;
      justify-content: center;
      align-items: center;
      max-width: 33%;
      min-width: 138px;
      height: auto;
      text-align: center;
      background-position: center;
      background-size: cover;
      transform: scale(0.95) !important;
      transition: transform 0.3s ease;

      &-active {
        transform: scale(1.05) !important;
      }

      .flag-image {
        max-height: 100%;
        width: 100%;
      }

      .flag-text {
        width: 100%;
        height: 79% !important;
        padding: 0 16px;

        .score-heading {
          letter-spacing: 0.4px;
          position: relative;

          .rect {
            position: absolute;
            &-left,
            &-right {
              width: 2.5%;
              min-width: 6px;
              aspect-ratio: 2/1;
            }
          }
        }
        .score-heading::before,
        .score-heading::after {
          content: '';
          height: 1px;
          flex-grow: 1;
        }
        .score-heading::before {
          margin-right: 4px;
        }
        .score-heading::after {
          margin-left: 4px;
        }
        .score-value {
          font-weight: 500;
          letter-spacing: 0.25px;
        }
        .score-divide {
          margin-bottom: 8px;
          position: relative;
          height: 1px;
          .line {
            height: 1px;
            width: 100%;
          }
          .rect {
            position: absolute;
            &-left,
            &-right {
              width: 2.5%;
              min-width: 6px;
              aspect-ratio: 2/1;
            }
          }
        }
        .score-platform {
          padding: 2px 0;
          height: 18px;
          border-radius: 4px 0px;
          p {
            letter-spacing: 0.4px;
          }
        }
        .score-info {
          gap: 8px;
          .v-avatar {
            border-radius: 4px 4px 4px 0px !important;
            max-width: 26.5% !important;
            min-width: 28px !important;
            height: unset !important;
            aspect-ratio: 1 / 1;
          }
          > span {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.5;
          }
        }
      }

      .flag-image {
        max-height: 100%;
        width: 100%;
      }
    }
  }

  /* 確保 v-responsive__content 內的元素不會溢出 */
  ::v-deep .v-responsive__content {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: flex-end;
  }

  @media screen and (max-width: 560px) {
    .flag-slide {
      .flag-text {
        .score-heading,
        .score-platform {
          line-height: 1 !important;
        }

        // flags 文字縮放
        @each $selector, $size in $ranking-fontsize-xs-560 {
          #{$selector} {
            font-size: calc(#{$size}) !important;
          }
        }
      }

      .score-info {
        .game-name {
          line-height: 1.25;
        }
      }
    }
  }

  @media screen and (max-width: 460px) {
    .flag-slide {
      .flag-text {
        .score-divide {
          margin-bottom: 4px !important;
        }
        .score-info {
          gap: 4px !important;
          .v-avatar {
            flex-shrink: 0;
          }
          span {
            line-height: 1.5;
          }
        }
      }
    }
  }
</style>

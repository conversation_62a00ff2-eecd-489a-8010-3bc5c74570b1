<template>
  <v-card-text class="rank-empty d-flex justify-center align-center flex-column pa-0">
    <v-img
      class="mb-6 mt-3"
      :class="{ 'mt-6': $vuetify.breakpoint.xs }"
      :src="noDataImage"
      :max-width="$vuetify.breakpoint.mdAndUp ? '572' : '343'"
      width="100%"
    >
      <template v-slot:placeholder>
        <v-row class="fill-height ma-0" align="center" justify="center">
          <v-progress-circular indeterminate color="grey lighten-5"></v-progress-circular>
        </v-row>
      </template>
    </v-img>
    <h5 class="text-h5 gradient-primary--text font-weight-bold custom-text-noto">
      {{ $t('data_error') }}
    </h5>
    <p class="my-6 text-body-1 custom-text-noto default-content--text">
      {{ $t('site_maintenance_1') }}
    </p>
    <v-btn
      class="backhome gradient-button button-content--text"
      @click="$router.push(localePath('/'))"
      >{{ $t('gohome') }}</v-btn
    >
  </v-card-text>
</template>

<script>
  const STATION = process.env.STATION

  export default {
    name: 'LeaderboardEmptyState',
    data() {
      return {
        STATION,
        noDataImage: require(`~/assets/image/${STATION}/error/maintenance.png`)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .rank-empty {
    ::v-deep .v-responsive__content {
      width: 100% !important;
    }
    ::v-deep .v-image {
      aspect-ratio: 1.587 / 1;
    }
    p {
      white-space: break-spaces;
    }
    .backhome {
      border-radius: 28px;
    }
  }
</style>

<template>
  <v-dialog
    v-model="hintDialog"
    class="ranking-hint-modal"
    content-class="rounded-lg"
    max-width="380"
  >
    <template #activator="{ on, attrs }">
      <div class="ranking-hint player-ranking-hint-bg-fill py-1 px-4" v-bind="attrs" v-on="on">
        <slot name="hintButton"></slot>
      </div>
    </template>

    <v-card>
      <v-list class="pa-6">
        <v-list-item class="pa-0">
          <v-list-item-content class="pa-0">
            <v-list-item-title class="ma-0 text-center pb-6 text-h6 custom-text-noto grey-1--text">
              {{ $t('description') }}
            </v-list-item-title>
            <p
              v-for="rule in leaderboardRules"
              :key="rule"
              class="ma-0 text-left text-body-2 custom-text-noto default-content--text"
            >
              {{ rule }}
            </p>
            <p
              class="ma-0 pt-6 text-left text-body-2 custom-text-noto warning--text"
              v-html="leaderboardDifferentDesc"
            ></p>
            <v-list-item-action class="ma-0 mt-6 d-flex justify-end">
              <v-btn color="primary-variant-1 button-content--text" @click="hintDialog = false">
                {{ $t('sure') }}
              </v-btn>
            </v-list-item-action>
          </v-list-item-content>
        </v-list-item>
      </v-list>
    </v-card>
  </v-dialog>
</template>

<script>
  export default {
    name: 'LeaderboardRulesDialog',
    props: {
      contentType: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        hintDialog: false
      }
    },
    computed: {
      leaderboardRules() {
        return this.$t(
          this.contentType === 'home' ? 'leaderboard_rules' : 'leaderboard_rules_1'
        ).split('\n')
      },
      leaderboardDifferentDesc() {
        return this.$vuetify.breakpoint.xs
          ? '※龍虎榜與星城Online APP的風雲榜屬不同系統，本榜單僅作排名展示，暫無提供獎勵服務。'
          : '※龍虎榜與星城Online APP的風雲榜屬不同系統，本<br /> 榜單僅作排名展示，暫無提供獎勵服務。'
      }
    }
  }
</script>

<style lang="scss" scoped>
  .v-list {
    .v-list-item__content {
      :nth-child(3) {
        padding-bottom: 24px;
      }
    }
  }
</style>

<template>
  <v-container class="pa-0">
    <v-card
      :color="$UIConfig.replaceColor.dailyListBackGroundColor"
      :class="[
        'w-100 px-4 py-6 px-sm-6',
        { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }
      ]"
      :elevation="$vuetify.breakpoint.smAndDown ? 0 : 4"
    >
      <v-card-title class="px-0 pt-0">
        <span
          class="custom-text-noto text-h5 default-content--text font-weight-bold title-soft--text"
          >{{ $t('daily_list') }}</span
        >
      </v-card-title>
      <v-card-text class="pa-0">
        <!-- description -->
        <v-row
          v-if="$UIConfig.dailyList.description"
          no-gutters
          id="dailyListDescription"
          class="custom-text-noto text-caption grey-3--text pb-4 text-soft--text"
        >
          <v-col cols="12">
            <span>{{ $t('daily_list_desc1') }}</span>
          </v-col>
        </v-row>
        <!-- betReport -->
        <dailyList
          :user-name="userName"
          is-info-page
          :items-per-page="15"
          :items-per-page-options="[7, 15, -1]"
        ></dailyList>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
  import { mapGetters } from 'vuex'
  import orientation from '@/mixins/orientation'
  export default {
    mixins: [orientation],
    name: 'dailyListContent',
    components: {
      dailyList: () => import('~/components/player_info/dailyList')
    },
    data() {
      return {}
    },

    computed: {
      ...mapGetters('role', ['userName'])
    },
    watch: {
      orientation: {
        handler() {
          this.scrollToTop()
        }
      }
    },
    methods: {
      goDownload() {
        if (this.$device.isAndroid || this.$device.isIos || this.$device.isMacOS) {
          const url = 'https://www.xin-stars.com/goStore'
          this.$lineOpenWindow.open(url)
        } else {
          this.$router.push({ path: this.localePath('/downloads'), hash: '#pc' })
        }
      },
      scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'auto' })
      }
    }
  }
</script>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .notch-left {
      padding-left: calc(16px + env(safe-area-inset-left)) !important;
    }
    .notch-right {
      padding-right: calc(16px + env(safe-area-inset-right)) !important;
    }
    //sm
    @media (min-width: 600px) {
      .notch-left {
        padding-left: calc(24px + env(safe-area-inset-left)) !important;
      }
      .notch-right {
        padding-right: calc(24px + env(safe-area-inset-right)) !important;
      }
    }
  }
</style>

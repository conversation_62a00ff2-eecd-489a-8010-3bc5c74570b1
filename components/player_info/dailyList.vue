<template>
  <v-row no-gutters id="daily-list-data-table">
    <v-col cols="12">
      <template>
        <v-data-table
          class="grey-6 default-content--text text-regular--text bg-table"
          fixed-header
          @pagination="scrollToTop"
          @update:page="handlePageChange"
          :hide-default-footer="$vuetify.breakpoint.xsOnly"
          :height="isCard ? height : '100%'"
          :items-per-page="itemsPage"
          :headers="dailyListHeaders"
          :page="currentPage"
          :items="betReport"
          :footer-props="{
            'items-per-page-text': $t('items_per_page'),
            'items-per-page-all-text': $t('all'),
            'page-text': `{0}-{1} ${$t('total_page')} {2} ${$t('quantity')}`,
            'items-per-page-options': itemsPerPageOptions
          }"
        >
          <template v-if="betReport.length === 0" v-slot:body>
            <tbody v-show="$vuetify.breakpoint.xsOnly" :height="isCard ? height - 48 : 'auto'">
              <tr class="v-data-table__empty-wrapper d-flex justify-center">
                <td class="d-flex align-center" colspan="5">
                  <span class="text-center">
                    {{ $t('no_data') }}
                  </span>
                </td>
              </tr>
            </tbody>
            <tbody v-show="!$vuetify.breakpoint.xsOnly" :height="isCard ? height - 48 : 'auto'">
              <tr class="v-data-table__empty-wrapper">
                <td colspan="5">
                  <span class="text-center">
                    {{ $t('no_data') }}
                  </span>
                </td>
              </tr>
            </tbody>
          </template>
          <template v-if="$vuetify.breakpoint.xsOnly" v-slot:footer="{ props: { pagination } }">
            <div class="v-data-footer">
              <v-row no-gutters>
                <v-col>
                  <div class="v-data-footer__select d-flex justify-start ml-3">
                    <span> {{ $t('items_per_page') }}</span>
                    <v-select
                      class="py-0 mt-3 mb-3"
                      v-model="select"
                      hide-details
                      height="32"
                      @input="onSelect"
                      :items="pagePaginationitem(itemsPerPageOptions)"
                    ></v-select>
                    <span class="v-data-footer__pagination">
                      {{ pagePagination(pagination) }}
                    </span>
                  </div>
                </v-col>
              </v-row>
              <v-row no-gutters>
                <v-col>
                  <v-btn
                    class="v-data-footer__icons-before"
                    icon
                    :disabled="pagination.pageStart === 0"
                    @click="currentPage = pagination.page - 1 === 0 ? 1 : pagination.page - 1"
                  >
                    <v-icon dark> mdi-chevron-left </v-icon>
                  </v-btn>
                  <v-btn
                    class="v-data-footer__icons-after"
                    icon
                    :disabled="pagination.pageStop === pagination.itemsLength"
                    @click="
                      currentPage =
                        pagination.page + 1 === pagination.pageCount
                          ? pagination.pageCount
                          : pagination.page + 1
                    "
                  >
                    <v-icon dark> mdi-chevron-right </v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </v-data-table>
      </template>
    </v-col>
  </v-row>
</template>

<script>
  import cloneDeep from 'lodash/cloneDeep'
  import scssLoader from '@/mixins/scssLoader.js'
  import convertTime from '~/utils/convertTime'
  export default {
    name: 'dailyList',
    props: {
      userName: { type: String, required: true, default: '' },
      isCard: {
        type: Boolean,
        default: false
      },
      isInfoPage: {
        type: Boolean,
        default: false
      },
      height: {
        type: [Number, String],
        default: 550
      },
      itemsPerPage: {
        type: Number,
        default: 15
      },
      itemsPerPageOptions: {
        type: Array,
        default: () => [7, 15, -1]
      }
    },
    mixins: [scssLoader],
    data() {
      return {
        betReport: [],
        select: this.itemsPerPage,
        itemsPage: this.itemsPerPage,
        currentPage: 1,
        dailyListHeaders: [
          {
            text:
              this.$t('date') + `(${convertTime.getGMTOffset(this.$UIConfig.timeStamp.timezone)})`,
            value: 'date',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          },
          {
            text: this.$t('single_day_showdown'),
            value: 'showdown',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          },
          {
            text: this.$t('score'),
            value: 'totalWin',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          },
          {
            text: this.$t('bet'),
            value: 'totalBet',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          },
          {
            text: this.$t('reporting_rate'),
            value: 'reportingRate',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          }
        ]
      }
    },
    created() {
      this.getBetReport()
    },
    methods: {
      async getBetReport() {
        // this.userName 角色名稱
        const res = await this.$store.dispatch('role/getBetReport', this.userName)
        if (res) {
          this.betReport = res.daily
            .map((item) => {
              return {
                date: this.getRemainText(item.date),
                showdown: item.totalWin - item.totalBet,
                scoreBet: `${item.totalWin} / ${item.totalBet}`,
                //日榜回報率：得分 ÷ 押注取小數第一位並無條件捨去
                reportingRate:
                  item.totalBet == 0
                    ? '0.0％'
                    : `${(Math.floor((item.totalWin / item.totalBet) * 1000) / 10).toFixed(1)}％`,
                totalWin: item.totalWin,
                totalBet: item.totalBet
              }
            })
            .slice(0, 30)
        }
      },
      goDownload() {
        if (this.$device.isAndroid || this.$device.isIos || this.$device.isMacOS) {
          const url = 'https://www.xin-stars.com/goStore'
          this.$lineOpenWindow.open(url)
        } else {
          this.$router.push({ path: this.localePath('/downloads'), hash: '#pc' })
        }
      },
      getRemainText(date) {
        const formattedTime = convertTime.convertISOTime(
          date,
          this.$UIConfig.timeStamp.formatDate,
          this.$UIConfig.timeStamp.timezone
        )
        return formattedTime
      },
      scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'auto' })
      },
      pagePaginationitem(itemArray) {
        let newItemArray = cloneDeep(itemArray)
        newItemArray[newItemArray.findIndex((x) => x === -1)] = this.$t('all')
        return newItemArray
      },
      onSelect() {
        if (this.select === this.$t('all')) this.itemsPage = -1
        else this.itemsPage = this.select
      },
      handlePageChange(page) {
        this.currentPage = page
      },
      pagePagination(pagination) {
        return pagination.pageCount === 0
          ? '-'
          : `${pagination.pageStart + 1}-${pagination.pageStop} ${this.$t('total_page')}
          ${pagination.itemsLength} ${this.$t('quantity')}`
      }
    }
  }
</script>

<style lang="scss">
  $grey-4: map-get($colors, 'grey-4');
  $grey-6: map-get($colors, 'grey-6');
  $default-content-color: map-get($colors, default-content);
  $bg-table-hover: map-get($colors, 'bg-table-hover');
  $text-regular: map-get($colors, 'text-regular');
  $btn-soft: map-get($colors, 'btn-soft');

  #daily-list-data-table {
    .v-data-table-header-mobile {
      th {
        background: $grey-4 !important;
      }
    }
    .v-data-table {
      tbody {
        tr {
          &:hover {
            background: $grey-4 !important;
            //replaceColor
            background: $bg-table-hover !important;
          }
        }
      }
    }
    .v-icon {
      color: $default-content-color;
      //replaceColor
      color: $text-regular;
    }
    .v-data-footer {
      color: $default-content-color;
      //replaceColor
      color: $text-regular;
      //左右鍵
      .v-btn__content {
        .v-icon {
          color: $btn-soft;
        }
      }
      //下底線
      .v-input__slot::before {
        border-color: $text-regular !important;
      }
    }
    .v-select__selections {
      color: $default-content-color;
      //replaceColor
      color: $text-regular;
    }
  }
</style>

<template>
  <div
    class="marquee-fill bg-marquee"
    :style="{ background: $UIConfig.marqueeColor.marqueeBackgroundColor }"
  >
    <span
      class="material-symbols-outlined marquee-icon text-marquee-medium--text"
      :style="{ color: $UIConfig.marqueeColor.marqueeFontColor }"
    >
      campaign
    </span>
    <div v-if="newsData.length" class="marquee-line">
      <swiper
        class="swiper-box"
        :options="swiperOptions"
        ref="marqueeSwiper"
        @click="handleSwiperClick"
        @mouseenter.native="
          () => {
            if (!$device.isMobile) stopAutoplay()
          }
        "
        @mouseleave.native="startAutoplay"
        @touch-start="handleTouchStart"
        @touch-end="handleTouchEnd"
      >
        <swiper-slide v-for="(news, index) in newsData" :key="news.type">
          <div class="marquee-item body-2" :class="$vuetify.breakpoint.xsOnly ? 'caption' : ''">
            <span class="marquee-content text-marquee--text">{{ news.marqueeTitle }}</span>
            <div class="marquee-link" :data-index="index">
              <span class="marquee-btn px-2 btn-regular--text">{{ $t('news_detail') }}</span>
            </div>
          </div>
        </swiper-slide>
      </swiper>
    </div>
    <div v-else class="marquee-item body-2 welcome-line">
      <swiper
        class="swiper-box"
        :options="swiperOptions"
        @mouseenter.native="
          () => {
            if (!$device.isMobile) stopAutoplay()
          }
        "
        @mouseleave.native="startAutoplay"
        @touch-start.native="handleTouchStart"
        @touch-end.native="handleTouchEnd"
      >
        <swiper-slide>
          <div class="marquee-line d-flex justify-center">
            <span class="marquee-content text-center text-marquee--text">{{
              $t('welcome_title')
            }}</span>
          </div>
        </swiper-slide>
      </swiper>
    </div>
  </div>
</template>

<script>
  import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
  import 'swiper/css/swiper.css'

  export default {
    name: 'NewsMarquee',
    components: {
      Swiper,
      SwiperSlide
    },
    data() {
      return {
        newsData: [],
        swiperOptions: {
          direction: 'vertical',
          loop: true,
          autoplay: {
            delay: 5000,
            disableOnInteraction: false
          },
          slidesPerView: 1,
          spaceBetween: 0,
          grabCursor: true
        }
      }
    },
    methods: {
      async fetchFirstNews() {
        const types = ['1', '2', '4']
        // 取得所有類型的第一則公告
        const allNewsPromises = types.map((type) =>
          this.$clientApi.news.getNews({
            type,
            lang: this.$i18n.locale,
            limit: 1,
            offset: 0
          })
        )
        const newsResults = await Promise.all(allNewsPromises)
        // 取得跑馬燈公告標題
        const marqueeResult = await this.$clientApi.news.getMarqueeTitle({
          lang: this.$i18n.locale
        })

        // 如果有取得跑馬燈標題，進行資料合併
        if (marqueeResult?.data?.list?.length > 0) {
          // 將所有公告資料整理成一個陣列
          const firstNews = []
          newsResults.forEach((result) => {
            if (result?.data?.list?.length > 0) {
              firstNews.push(result.data.list[0])
            }
          })

          // 用 marqueeResult 的 ID 去比對公告資料，進行合併
          this.newsData = marqueeResult.data.list
            .map((marqueeItem) => {
              const matchedNews = firstNews.find((news) => news.id === marqueeItem.id)
              if (matchedNews) {
                return {
                  ...matchedNews,
                  type: marqueeItem.type,
                  marqueeTitle: marqueeItem.marqueeTitle || matchedNews.title
                }
              }
              return null
            })
            .filter((item) => item !== null)
        } else {
          // 沒有跑馬燈標題則空值
          this.newsData = []
        }
      },
      stopAutoplay() {
        if (this.$refs.marqueeSwiper && this.$refs.marqueeSwiper.$swiper) {
          this.$refs.marqueeSwiper.$swiper.autoplay.stop()
        }
      },
      startAutoplay() {
        if (this.$refs.marqueeSwiper && this.$refs.marqueeSwiper.$swiper) {
          this.$refs.marqueeSwiper.$swiper.autoplay.start()
        }
      },
      handleTouchStart() {
        this.touchTimer = setTimeout(() => {
          this.stopAutoplay()
        }, 300)
      },
      handleTouchEnd() {
        if (this.touchTimer) {
          clearTimeout(this.touchTimer)
        }
        this.startAutoplay()
      },
      goToNews(news) {
        this.$router.push({
          path: this.localePath('/news'),
          query: {
            type: news.type,
            page: '1',
            newsID: news.id
          }
        })
      },
      handleSwiperClick(e) {
        // 因awesome-swiper的bug,需要自行判斷data-index
        const index = e.target.getAttribute('data-index')
        if (index !== null) {
          const news = this.newsData[index]
          if (news) {
            this.goToNews(news)
          }
        }
      }
    },
    created() {
      this.fetchFirstNews()
    },
    beforeDestroy() {
      if (this.touchTimer) {
        clearTimeout(this.touchTimer)
      }
    }
  }
</script>

<style lang="scss" scoped>
  $border-marquee: map-get($colors, border-marquee);
  .marquee-fill {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 32px;
    overflow: hidden;
    padding: 0px 5px 0px 5px;
    box-sizing: border-box;
    border-radius: 4px;
    border-left: 3px solid #ffffff80;
    border-right: 3px solid #ffffff80;
    //replaceColor
    border-left: 3px solid $border-marquee;
    border-right: 3px solid $border-marquee;
    user-select: none;
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE/Edge */
  }
  .marquee-icon {
    width: 24px;
  }
  .swiper-box {
    width: 100%;
    height: 100%;
  }
  .marquee-line {
    width: calc(100% - 24px);
    height: 100%;
  }
  .marquee-item {
    height: 100%;
    padding: 4px 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    color: #fff;
    justify-content: space-between;
  }
  .marquee-content {
    box-sizing: border-box;
    max-width: calc(100% - 45px - 16px);
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 8px;
    white-space: nowrap;
    word-break: break-word;
  }
  .marquee-link {
    height: 100%;
    margin-left: 8px;
    position: relative;
    cursor: pointer;
  }
  .marquee-btn {
    display: block;
    width: 100%;
    font-size: 12px;
    border: 1px solid;
    box-sizing: border-box;
    text-align: center;
    border-radius: 4px;
    text-transform: uppercase;
    pointer-events: none;
  }
  .marquee-item.welcome-line {
    width: calc(100% - 24px);
    text-align: center;
    justify-content: space-evenly;
  }
  @media screen and (max-width: 599px) {
    .marquee-fill {
      height: 48px;
    }
    .marquee-item {
      height: 100%;
    }
    .marquee-content {
      display: -webkit-box;
      line-clamp: 2;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      word-wrap: break-word;
      white-space: pre-wrap;
      word-break: keep-all;
    }
    .marquee-btn {
      position: relative;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .marquee-item.welcome-line {
      height: 100%;
      display: flex;
      align-items: center;
      align-items: center;
      justify-content: space-evenly;
      .marquee-line {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
</style>

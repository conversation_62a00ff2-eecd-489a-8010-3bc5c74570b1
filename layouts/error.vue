<template>
  <v-container grid-list-xl class="layouts-error">
    <v-card
      v-if="maintainSystem[0].maintaining"
      elevation="0"
      :max-width="$vuetify.breakpoint.thresholds.xs"
      color="transparent"
      class="mx-auto"
    >
      <v-layout row wrap fill-height justify-center align-content-center>
        <v-flex xs12 class="d-flex justify-center align-center w-100">
          <v-img
            v-show="imageMap[errorInfo.code]"
            :src="imageMap[errorInfo.code]"
            :max-width="$vuetify.breakpoint.smAndDown ? '343' : '572'"
            contain
            width="100vw"
            :class="[$UIConfig.error.imgClass]"
            title="maintenance"
          />
        </v-flex>
        <v-flex xs12 text-center>
          <v-card
            elevation="0"
            :max-width="$vuetify.breakpoint.thresholds.xs"
            height="50%"
            color="transparent"
            class="mx-auto"
          >
            <div
              v-if="$UIConfig.error.showErrorInfo"
              class="custom-text-noto text-h5 font-weight-bold gradient-primary--text pb-4"
            >
              {{
                maintainSystem[0].maintaining ? $t('error.website_maintenance') : errorInfo.message
              }}
            </div>
            <template v-if="$UIConfig.error.showMaintenanceTime">
              <v-card-subtitle class="pa-2">
                <span :class="[$UIConfig.error.maintenanceSubTitle]">
                  {{ $t('error.website_maintenance_content') }}
                </span>
              </v-card-subtitle>
              <v-card-text>
                <p class="text-md-body-1 mb-2">
                  <span :class="[$UIConfig.error.maintenanceText]">
                    {{ $t('error.maintenance_time') }}{{ $UIConfig.error.maintenanceColon }}</span
                  >
                  <br class="py-10" />
                </p>
                <p class="text-md-body-1">
                  <span class="warning--text">
                    <span :class="[$UIConfig.error.maintenanceTimeText]">{{
                      formatMaintenanceTime(errorInfo.params.maintainBeginAt)
                    }}</span>
                    <span :class="[$UIConfig.error.maintenanceTimeText]">~</span>
                    <span :class="[$UIConfig.error.maintenanceTimeText]">{{
                      formatMaintenanceTime(errorInfo.params.maintainEndAt)
                    }}</span>
                  </span>
                </p>
              </v-card-text>
            </template>
          </v-card>
        </v-flex>
      </v-layout>
    </v-card>

    <v-card
      v-else-if="errorInfo.code === 200"
      :elevation="0"
      :max-width="$vuetify.breakpoint.smAndDown ? '343' : '572'"
      height="50%"
      color="transparent"
      class="mx-auto"
    >
      <v-layout row wrap fill-height justify-center align-center>
        <v-flex xs12 font-weight-bold>
          <p class="text-center custom-text-noto text-h6">
            {{ $t('ie_not_support') }}
          </p>
          <p class="text-center custom-text-noto text-h6">
            <span>{{ $t('recommended_use') }}</span>
            <a :href="`https://www.google.com/intl/${$i18n.locale}/chrome/`" target="_blank">
              <span class="primary--text">
                {{ $t('chrome') }}
              </span>
            </a>
            <span>{{ $t('browser_best_experience') }}</span>
          </p>
        </v-flex>
      </v-layout>
    </v-card>
    <v-card
      v-else
      :elevation="0"
      :max-width="$vuetify.breakpoint.thresholds.xs"
      height="50%"
      color="transparent"
      class="mx-auto"
    >
      <v-layout row wrap fill-height justify-center align-content-center class="my-0">
        <v-flex xs12 class="d-flex justify-center align-center w-100">
          <v-img
            v-show="imageMap[errorInfo.code]"
            :src="imageMap[errorInfo.code]"
            :max-width="$vuetify.breakpoint.smAndDown ? '343' : '572'"
            :class="[$UIConfig.error.imgClass]"
          />
        </v-flex>
        <v-flex v-if="errorInfo.title && $UIConfig.error.showErrorInfo" xs12 text-center>
          <h2 class="text-h5 font-weight-bold gradient-primary--text">
            {{ errorInfo.title }}
          </h2>
        </v-flex>
        <v-flex v-if="$UIConfig.error.showErrorInfo" xs12 text-center>
          <span class="default-content--text">{{ errorInfo.message }}</span>
        </v-flex>
        <v-flex xs12 text-center>
          <v-btn
            depressed
            rounded
            color="gradient-primary button-content--text"
            @click="isNewsPath ? goNews() : goHome()"
          >
            {{ isNewsPath ? $t('goNews') : $t('gohome') }}
          </v-btn>
        </v-flex>
      </v-layout>
    </v-card>
  </v-container>
</template>

<script>
  const STATION = process.env.STATION
  import convertTime from '~/utils/convertTime'
  export default {
    layout: 'white',
    props: {
      error: { type: Object, default: () => {} }
    },
    data() {
      const imageMap = {
        400: require(`~/assets/image/${STATION}/error/400.png`),
        401: require(`~/assets/image/${STATION}/error/401.png`),
        403: require(`~/assets/image/${STATION}/error/403.png`),
        404: require(`~/assets/image/${STATION}/error/404.png`),
        500: require(`~/assets/image/${STATION}/error/500.png`),
        502: require(`~/assets/image/${STATION}/error/502.png`),
        503: require(`~/assets/image/${STATION}/error/maintenance.png`),
        504: require(`~/assets/image/${STATION}/error/504.png`)
      }

      const textMap = {
        400: this.$t('error.bad_request'),
        401: this.$t('error.unauthorized'),
        403: this.$t('error.forbidden'),
        404: this.$t('error.not_found'),
        500: this.$t('error.internal_server_error'),
        502: this.$t('error.bad_gateway'),
        503: this.$t('error.service_unavailable'),
        504: this.$t('error.gateway_timeout')
      }

      const contentMap = {
        400: this.$t('error.bad_request_content'),
        401: this.$t('error.unauthorized_content'),
        403: this.$t('error.forbidden_content'),
        404: this.$t('error.not_found_content'),
        500: this.$t('error.internal_server_error_content'),
        502: this.$t('error.internal_server_error_content'),
        503: this.$t('error.internal_server_error_content'),
        504: this.$t('error.internal_server_error_content')
      }

      return {
        textMap,
        imageMap,
        contentMap
      }
    },
    computed: {
      maintainSystem() {
        return this.$store.getters['maintain/system']
      },
      errorInfo() {
        let message =
          this.error.statusCode === 404 && this.isNewsPath // 判斷是否為公告頁
            ? this.$t('error.not_found_news') // 公告頁404內容
            : this.contentMap[this.error.statusCode] || '' // 一般404內容

        if (this.$te(`error.${this.error.message}`)) {
          message = this.$t(`error.${this.error.message}`)
        }

        return {
          code: this.error.statusCode,
          title: this.textMap[this.error.statusCode] || '',
          params: this.error.params || {},
          message
        }
      },
      isNewsPath() {
        // 檢查URL是否包含news
        return this.$route.path.includes('/news')
      }
    },
    created() {
      this.$store.commit('SET_IS_IN_ERROR_PAGE', true)
      // 關閉遊戲模式
    },
    beforeDestroy() {
      this.$store.commit('SET_IS_IN_ERROR_PAGE', false)
    },
    watch: {
      'error.code': {
        handler() {
          this.$nuxt.$emit('root:game', false)
          this.$nuxt.$emit('root:game.demo', false)
          this.$nuxt.$emit('root:game.id', 0)
        }
      }
    },
    methods: {
      goHome() {
        // if (this.$wsClient.isConnected) {
        //   const reqData = this.$wsPacketFactory.logout()
        //   this.$wsClient.send(reqData)
        //   this.$wsClient.disconnect()
        //   this.$nuxt.$emit('root:showLogoutDialogStatus', false)
        //   this.$nuxt.$emit('root:showRoleDialogStatus', false)
        //   this.$store.dispatch('clear')
        // }
        this.$nuxt.$loading.start()
        if (this.$route.fullPath === '/') {
          this.$router.push(this.localePath('/downloads#pc'))
          setTimeout(() => {
            this.$router.replace(this.localePath('/'))
          }, 100)
        } else {
          this.$router.push(this.localePath('/'))
        }
      },
      formatMaintenanceTime(time) {
        return convertTime
          .convertISOTime(
            time,
            this.$UIConfig.timeStamp.formatError,
            this.$UIConfig.timeStamp.timezone
          )
          .format(`(${convertTime.getGMTOffset(this.$UIConfig.timeStamp.timezone)}),`)
      },
      goNews() {
        this.$nuxt.$loading.start()
        // 導向公告預設頁面
        this.$router.push(this.localePath('/news?type=2&page=1'))
      }
    }
  }
</script>

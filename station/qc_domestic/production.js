export default {
  client_id: '11a86521',
  secret_key: '842417ef317c19b64f2d953572923f0b',
  env: {
    BASE_URL: 'https://h5.xin-stars.com/',
    NUXT_ENV: process.env.NUXT_ENV || process.env.NODE_ENV || 'development',
    IMAGE_URL: 'https://img.xinverse.xyz',
    CLIENT_VERSION: process.env.CLIENT_VERSION || '20230802100001',
    PLATFORM_URL: 'https://h5.xin-stars.com/',
    AVATAR_BASE_URL: 'https://images.xin-stars.com',
    STATION: process.env.STATION || undefined
  },
  lock: {
    guild: true,
    useQpp: true,
    literalPrison: false,
    appDownload: true,
    chatroom: {
      otherStarCityOnlinePlatformsReminderDisabled: false
    },
    specialGame: {
      live: true,
      top_and_hot: true,
      fishing: true,
      featured: true,
      newest_and_recommend: true
    }
  },
  game: {
    serverConstant: 0
  },
  customLocaleList: [
    {
      code: 'zh-tw',
      text: '繁體中文',
      src: 'tw.svg'
    }
    // { code: 'zh-cn', text: '简体中文', src: 'cn.svg' }
    // { code: 'en', text: 'English', src: 'en.svg' }
    // { code: 'vi-vn', text: 'Tiếng Việt', src:  'vietnam.svg' }
  ],
  i18n: {
    // WARN  [nuxt-i18n] Couldn't read page component file
    // 因為不是用預設路徑，所以需要禁用自动路由生成解決開發模式下的警告
    parsePages: false,
    vueI18n: {
      messages: {
        'zh-tw': require('../../locales/zh-tw')
        // 'zh-cn': require('../../locales/zh-cn'),
        // 'vi-vn': require('../../locales/vi-vn'),
        // en: require('../../locales/en')
      },
      fallbackLocale: {
        default: ['zh-tw']
      }
    },
    locales: [
      { code: 'zh-tw', iso: 'zh-TW', file: 'zh-tw.js', isCatchallLocale: true }
      // { code: 'zh-cn', iso: 'zh-CN', file: 'zh-cn.js' },
      // { code: 'vi-vn', iso: 'VN', file: 'vi-vn.js' },
      // { code: 'en', iso: 'en-US', file: 'en.js' }
    ],
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'locale',
      redirectOn: 'root'
    },
    vuex: {
      syncLocale: true
    },
    lazy: true,
    langDir: 'locales/',
    seo: false,
    defaultLocale: 'zh-tw',
    baseUrl: process.env.BASE_URL
  }
}

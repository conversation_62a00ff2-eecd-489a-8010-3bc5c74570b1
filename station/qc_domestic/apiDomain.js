export default () => {
  let backendBaseURL = ''
  let qppBaseURL = ''
  switch (process.env.NUXT_ENV) {
    // 開發站
    case 'development':
      backendBaseURL = 'https://living-room-api.xincity.xyz'
      qppBaseURL = 'https://ssl.wanin.tw'
      break
    // 測試站
    case 'staging':
      backendBaseURL = 'https://living-room-api.xincity.xyz'
      qppBaseURL = 'https://ssl.wanin.tw'
      break
    // 正式站
    case 'production':
      backendBaseURL = 'https://lr-api.xinverse.net'
      qppBaseURL = 'https://ssl.wanin.tw'
      break
  }
  return {
    backend: {
      baseURL: backendBaseURL
    },
    qpp: {
      autoGoToStatus: false,
      baseURL: qppBaseURL,
      getno: '/1604/login/getno',
      getio: '/1604/login/getio',
      vendorID: '10007'
    }
  }
}

const config = {
  //站台功能開關
  lock: {
    //公會開關
    guild: true,
    //OPP開關
    useQpp: true,
    //星城Online下載顯示開關
    appDownload: true,
    //reminder顯示開關
    otherStarCityOnlinePlatformsReminderDisabled: false,
    //華義實名
    getSlotIdentity: false,
    //啟用Station翻譯>> lang_XXX
    stationConvert: false,
    //檢查綁定手機
    checkBindPhoneNumber: true,
    //文字獄
    literalPrison: false,
    specialGame: {
      live: true,
      top_and_hot: true,
      fishing: true,
      featured: true,
      newest_and_recommend: true,
      chess_and_card: true
    },
    chatroom: {
      playerSemantic: true,
      msgSticker: true,
      msgCustomSticker: true,
      customerServiceCustomSticker: true,
      checkoutUserStation: false
    },
    // 拉彩公告
    grandPrizeNoty: true,
    leaderboard: true,
    unlockOfficial: 1
  },
  grandPrizeNoty: {
    pageClass: 'grand-prize-cards-wrapper px-4 py-2 mt-4 bg-pale',
    defaultImg: require('~/assets/image/grand_prize_default.webp'),
    defaultImgPng: require('~/assets/image/grand_prize_default.png'),
    grandPrizeItem: {
      class: 'cursor-pointer rounded-0',
      hasDownLoad: true
    },
    imgStyle: ''
  },
  vipLevel: {
    vipLevelTitle: [
      'none_vip',
      'bronze',
      'silver',
      'gold',
      'whiteGold',
      'platinum',
      'diamond',
      'fancyDiamond',
      'moonDazzle',
      'sunDazzle',
      'starDazzle'
    ],
    vipLevelImgFileName: [
      '0-1_None',
      '1-1_Bronze',
      '2-1_Silver',
      '3-1_Gold',
      '4-1_WhiteGold',
      '5-1_Platinum',
      '6-1_Diamond',
      '7-1_FancyDiamond',
      '8-1_MoonDazzle',
      '9-1_SunDazzle',
      '10-1_StarDazzle'
    ],
    vipLevelImg3xFileName: [
      '0-1_None@3x',
      '1-1_Bronze@3x',
      '2-1_Silver@3x',
      '3-1_Gold@3x',
      '4-1_WhiteGold@3x',
      '5-1_Platinum@3x',
      '6-1_Diamond@3x',
      '7-1_FancyDiamond@3x',
      '8-1_MoonDazzle@3x',
      '9-1_SunDazzle@3x',
      '10-1_StarDazzle@3x'
    ],
    vipLevelIconFileName: [
      '0_None_icon.png',
      '1_Bronze_icon.png',
      '2_Silver_icon.png',
      '3_Gold_icon.png',
      '4_WhiteGold_icon.png',
      '5_Platinum_icon.png',
      '6_Diamond_icon.png',
      '7_FancyDiamond_icon.png',
      '8_MoonDazzle_icon.png',
      '9_SunDazzle_icon.png',
      '10_StarDazzle_icon.png'
    ]
  },
  miniGame: {
    enable: false,
    keyWords: ['已配發銅幣，請點擊大廳下方【銅幣】領取']
  },
  timeStamp: {
    format: 'YYYY-MM-DD HH:mm:ss',
    formatDate: 'YYYY/MM/DD',
    formatNewsNote: 'YYYY/MM/DD HH:mm [{0}]',
    formatError: 'YYYY-MM-DD HH:mm',
    formatLogin: 'YYYY/MM/DD HH:mm:ss',
    timezone: 'Asia/Taipei'
  },
  qpp: {
    showQPPIcon: true
  },
  //站台樣式名重新命名
  replaceClassName: true,
  defaultBtnColor: 'primary-variant-1',
  //facebbok使用版本
  facebookVersion: 10,
  customerService: {
    title: 'gradient-primary-left',
    announcementColor: { red: 233, blue: 80, green: 185 } //text-medium
  },
  chatroom: {
    mutedText: '{0}{1}(GMT+8)',
    banSpeaking: '{0}(GMT+8)',
    //客服是否使用電話號碼>>電話:信箱
    usePhoneNumber: true,
    welcomeMessageContent: ''
  },
  msgBar: {
    customerBtn: { icon: 'mdi-face-agent', color: 'primary-variant-1', isVuetifyIcon: true },
    sendIconColor: 'primary-variant-1'
  },
  msgBox: {
    dateTimeText: 'A hh:mm'
  },
  specialGameCard: {
    fontWeight: 'font-weight-black'
  },
  gameCard: {
    backGroundColor: (dailyRtpResult, defaultRtp) => {
      return dailyRtpResult > defaultRtp ? 'bg-game-square-rtp-up' : 'bg-game-square-rtp-down'
    }
  },
  newLogin: {
    companyInfoList: { useHerf: true, click: false, style: '' },
    phoneIconColor: 'default-content--text',
    loginNotyArray: [
      'before_login_notice1',
      'before_login_notice2',
      'before_login_notice3',
      'before_login_notice4',
      'before_login_notice5',
      'infringement_notice',
      'free_play_no_calculate_notice'
    ],
    loginMethod: [
      {
        id: 1,
        icon: 'phone_iphone',
        iconFile: null,
        title: 'phone_number_login',
        maxWidth: '28px',
        status: 1
      },
      {
        id: 2,
        icon: null,
        iconFile: require('~/assets/image/login/apple_login.svg'),
        title: 'Apple',
        maxWidth: '28px',
        status: 1
      },
      {
        id: 3,
        icon: null,
        iconFile: require('~/assets/image/login/facebook_login.svg'),
        title: 'Facebook',
        maxWidth: '28px',
        status: 1
      },
      {
        id: 4,
        icon: null,
        iconFile: require('~/assets/image/login/google_login.svg'),
        title: 'Google',
        maxWidth: '28px',
        status: 1
      },
      {
        id: 5,
        icon: null,
        iconFile: require('~/assets/image/login/line-login.svg'),
        logoFile: require('~/assets/image/login/line-logo.svg'),
        title: 'LINE',
        maxWidth: '36px',
        status: 1
      }
    ]
  },
  guildInfoPage: {
    showLeaveCountDown: false,
    infoListOptions: [10, 20, 30, -1]
  },
  guildEditDialog: {
    totemEnable: true,
    nameEnable: true,
    notyEnable: true,
    textFullWidthValidEnable: false
  },
  guildListPage: {
    backGroundColor: 'card-fill',
    showGuildRank: true,
    guildRankExpiredDate: '2025-04-21 00:00:00',
    countDownTextSetting: '',
    countDownNumberColor: 'warning--text',
    guildBannerSetting: 'guild-banner'
  },
  guildAcceptList: {
    showAcceptNotyDialog: false
  },
  guildPinMsg: {
    enable: false,
    defaultMsg: '',
    splitMark: '－＄－'
  },
  phoneNumber: {
    dialogWidth: '720px',
    showQRCode: true,
    clearPhoneNumber: false
  },
  cookieNotice: {
    herf: 'https://www.wanin.tw/privacy?p=2',
    target: '__blank',
    click: false,
    companyInfoIdx: null,
    companySetting: null,
    reflashOnAccept: true
  },

  coffer: {
    saveStarCoin: {
      title: '{0}{1}',
      balance: '{0}{1}',
      input: '{0}{1}'
    },
    withdrawCoin: {
      title: '{0}{1}',
      balance: '{0}{1}',
      input: '{0}{1}'
    }
  },
  mailIndex: {
    fontWeightRegular: true,
    mobileSendMailBtnColor: 'primary-variant-1',
    confirmDeleteDialogWidth: '290px',
    delieteReadAllBtnColor: 'primary-variant-1',
    mailTitleClass: 'letter-info--text',
    mailTimeDay: '{1}{0}{2}',
    mailTimeHr: '{1}{0}{2}',
    sendNoty: 'mail_send_noty6',
    sendMailLevel: 3
  },
  paymentIndex: {
    title: '{0}{1}',
    xinCoinFormat: '{0}{1}+{2}{3}{4}',
    redeemPoints: '{0}{1}',
    redeemXinCoins: '{1}{2}{3}',
    convertClass: '',
    dictUpper: false,
    paymentDict: '{0}',
    showGiftPack: true,
    showVipTab: false // 因只有星城有VIP禮包，故使用UI.config做站點VIP優惠控制
  },
  dailyList: {
    backGroundColor: 'card-fill',
    description: true
  },
  role: {
    roleDialogWidth: '600px',
    userNameColor: 'grey-2--text',
    titleBackgroundColor: 'gradient-primary-left',
    roleDescriptionSetting: ''
  },
  error: {
    showErrorInfo: true,
    showMaintenanceTime: true,
    maintenanceSubTitle: 'text-md-body-1',
    maintenanceText: 'font-weight-bold',
    maintenanceTimeText: '',
    maintenanceColon: '：',
    imgClass: 'mx-auto my-16'
  },
  playerInfoCardSetting: {
    dailyListEnable: true,
    achievementEnable: true,
    dailyListTableText: 'daily_list_desc1',
    useVisitBtn: false
  },
  badge: {
    classSetting: 'card-fill'
  },
  gamePage: {
    background: 'card-fill',
    sortGameName: 'newest_games',
    resetUrlValue: false,
    inputValidate: 'special_character'
  },
  stationPage: {
    backGround: 'card-fill'
  },
  easyPlayerInfo: {
    backGround: 'card-fill',
    showLevel: true
  },
  menu: {
    backGround: 'transparent'
  },
  yoeShop: {
    url: '{0}/yoegames/main?mode=web'
  },
  gameIframe: {
    logoHeight: '20',
    showNoExpDialog: true
  },
  swiperBox: {
    gameCardWidth: {
      lg: 'calc(16.67% - 15.9px)',
      md: 'calc(24.5% - 15.9px)',
      sm: 'calc(32.25% - 16px)',
      xs: 'calc(48% - 16px)'
    },
    featuredGameCardMinWidth: {
      lg: '260px'
    },
    featuredGameCardWidth: {
      lg: 'calc(25% - 15.9px)',
      md: '320px',
      sm: '268px',
      xs: '300px'
    },
    giftPackCardWidth: {
      lg: 'calc(100%/3 - 32px/3)',
      md: 'calc(100%/3 - 32px/3)',
      sm: 'calc(100%/3 - 32px/3)',
      xs: 'calc(100%/3 - 32px/3)',
      twoGiftPack: 'calc(100%/2 - 8px/2)'
    }
  },
  giftPackLayoutColor: {
    background: '#ac2335',
    color: '#fbfbfb'
  },
  giftPackGradientBG: {
    background:
      'radial-gradient(95.52% 27.88% at 50% -12.07%,#fff 0%,rgba(255, 255, 255, 0) 100%),radial-gradient(98.24% 20.9% at 50% 100%, #926e59 0%, rgba(146, 110, 89, 0) 100%),conic-gradient(from {0}deg at 100% 100%,rgba(146, 110, 89, 0) 0deg,rgba(146, 110, 89, 0.3) 360deg),#926e59',
    vipBackground:
      'radial-gradient(95.52% 27.88% at 50% -12.07%,#fff 0%,rgba(255, 255, 255, 0) 100%),radial-gradient(98.24% 20.9% at 50% 100%, #ac2335 0%, rgba(172, 35, 53, 0) 100%),conic-gradient(from {0}deg at 100% 100%,rgba(172, 35, 53, 0) 0deg,rgba(172, 35, 53, 0.3) 360deg),#ac2335'
  },
  giftPackCurrency: 'TWD',
  footbar: {
    gameCategoryColor: 'black',
    menuBackgroundColor: ''
  },
  footer: {
    footerNotyArray: [
      'footer_warning1',
      'footer_warning2',
      'footer_warning3',
      'footer_warning4',
      'infringement_notice',
      'free_play_no_calculate_notice'
    ],
    communityList: [
      {
        name: 'facebook',
        icon: null,
        iconFile: 'facebook.svg',
        link: 'https://www.facebook.com/xinstarsonline/'
      },
      {
        name: 'instagram',
        iconFile: 'instagram.svg',
        link: 'https://www.instagram.com/xinstars.online/'
      },
      {
        name: 'youtube',
        icon: null,
        iconFile: 'youtube.svg',
        link: 'https://www.youtube.com/channel/UCX09vjW1DhfFvubptj8XXdA'
      }
    ]
  },
  characterInfo: {
    showLevel: true,
    showVipLevel: true,
    showGuild: true,
    showCoin: true,
    showHonor: true,
    showActive: true,
    showSilverCoin: false
  },
  vipLevelDescDialog: {
    vipImgTitle: 'vip'
  },
  marqueeColor: {
    marqueeBackgroundColor: '',
    marqueeFontColor: ''
  },
  gameIntro: {
    coverWidth: {
      mdAndUp: '212px',
      smAndDown: '160px'
    }
  },
  playerAppAssetStatus: true,
  // 系統限制設定
  restriction: {
    reportInterval: 15 // 檢舉冷卻時間（秒）
  },
  //站台樣式名重新命名
  replaceColor: {
    btnRegular: 'btn-regular',
    bgGamePromoteRtp: 'bg-game-promote-rtp',
    textGamePromoteRtp: 'text-game-promote-rtp',
    white: 'white',
    bgBtnHeavy: 'bg-btn-heavy',
    textBtnHeavyText: 'text-btn-heavy--text',
    bgBtn: 'bg-btn',
    tabFocused: 'tab-focused',
    bgSwitch: 'bg-switch',
    bgDialogHeader: 'bg-dialog-header',
    customDialogTitleCloseBtn: 'btn-dialog-inverse',
    dailyListBackGroundColor: 'bg-card',
    textCharacterCard: 'text-character-card--text',
    roleIndexBgBtnHeavy: 'bg-btn-heavy',
    bgSlider: 'bg-slider',
    bgSliderTrack: 'bg-slider-track',
    bgSliderThumb: 'bg-slider-thumb',
    pageBackGroundColor: 'bg-card',
    textRegular: 'text-regular',
    bgDialogMedium: 'bg-dialog-medium',
    bgDialog: 'bg-dialog',
    customDialogSendIconColor: 'btn-chat',
    customDialogSendIconColor2: 'bg-btn',
    customDialogSendIconColor3: 'bg-btn',
    customDialogbgBtn2: 'bg-btn',
    customServiceImageBtn: 'btn-soft'
  }
}

// 導出 config
export { config }

// eslint-disable-next-line no-unused-vars
export default ({ app }, inject) => {
  // 将 config 注入到应用中
  inject('UIConfig', config)
}

<template>
  <v-container v-if="this.$UIConfig.lock.leaderboard" class="pa-0" fluid>
    <v-row class="winners justify-center px-xl-0 px-lg-0 px-md-6 px-sm-4 px-4" no-gutters>
      <v-col class="col-lg-9 col-12 mw-75-v">
        <v-row no-gutters>
          <leaderboard-winners-panel
            ref="winnersSectionRef"
            @update-active-rank="updateActiveRank"
            @section-height-updated="setWinnersHeight"
            @open-player-info="handleOpenPlayerInfo"
            @open-platform-game="handleOpenPlatformGame"
            :user-player-data.sync="userPlayerData"
          />
          <leaderboard-player-list
            ref="playerRankingSectionRef"
            @open-player-info="handleOpenPlayerInfo"
            @open-platform-game="handleOpenPlatformGame"
            :user-player-data="userPlayerData"
            :winners-height="winnersHeight"
          />
          <v-menu
            v-model="playerInfoVisible"
            :position-x="menuX"
            :position-y="menuY"
            absolute
            offset-y
          >
            <easy-player-info
              report
              is-card
              :player-info.sync="selectPlayerData"
              class="elevation-4"
              tile
              only-coin
              badge-type="relation"
              action-bar
            />
          </v-menu>
          <gameIntro
            v-if="showGameIntroDialogStatus"
            :show-game-intro-dialog-status.sync="showGameIntroDialogStatus"
            :game="currGameRtp"
          />
          <leaderboard-app-redirect-dialog
            :show-redirect-app-dialog.sync="showRedirectAppDialog"
          ></leaderboard-app-redirect-dialog>
        </v-row>
      </v-col> </v-row
  ></v-container>
</template>

<script>
  import preLoginAction from '@/mixins/preLoginAction.js'
  import relationship from '~/mixins/relationship.js'
  import analytics from '@/mixins/analytics.js'
  import leaderboard from '~/mixins/leaderboard'
  const STATION = process.env.STATION

  export default {
    name: 'LeaderboardPage',
    components: {
      LeaderboardWinnersPanel: () => import('~/components/leaderboard/winnersPanel.vue'),
      LeaderboardPlayerList: () => import('~/components/leaderboard/playerList.vue'),
      easyPlayerInfo: () => import('~/components/player_info/easyPlayerInfo'),
      gameIntro: () => import(`~/components_station/${STATION}/game/gameIntro.vue`),
      LeaderboardAppRedirectDialog: () => import('~/components/leaderboard/appRedirectDialog.vue')
    },
    mixins: [preLoginAction, relationship, analytics, leaderboard],
    data() {
      return {
        userPlayerData: {},
        winnersHeight: 0,
        playerInfoVisible: false,
        showGameIntroDialogStatus: false,
        menuX: 0,
        menuY: 0,
        currGameRtp: {},
        showRedirectAppDialog: false
      }
    },
    destroyed() {
      this.$store.commit('leaderboard/SET_ACTIVE_RANK', 0)
    },
    methods: {
      updateActiveRank(newRank) {
        this.$store.commit('leaderboard/SET_ACTIVE_RANK', newRank)
        if (this.isLogin) this.$refs.playerRankingSectionRef?.resetVisibleNames()
      },
      setWinnersHeight() {
        this.$nextTick(() => {
          const element = this.$refs.winnersSectionRef?.$el
          if (element && element.offsetHeight > 0) {
            this.winnersHeight = element.offsetHeight - 20
          }
        })
      },
      async handleOpenPlayerInfo(event, username) {
        this.playerInfoVisible = false
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        if (!this.isLogin) {
          this.setPreLoginAction('playerInfo', this.handleOpenPlayerInfo, event, username)
          this.$nuxt.$emit('root:showLoginDialogStatus', { show: true })
          return
        }
        this.getPlayerInfo(username)
        if (username === this.userName) return
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            const rect = event.target.getBoundingClientRect()
            // 設置選單位置
            this.menuX = rect.left
            this.menuY = rect.top
            this.playerInfoVisible = true
          })
        })
      },
      async handleOpenPlatformGame(playerRankData) {
        // 取得點擊的遊戲列表
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        if (!playerRankData?.platform) {
          if (playerRankData.text.indexOf('game_not_found_placeholder') === -1)
            this.showRedirectAppDialog = true
          return
        }
        // 黑名單或遊戲停用則notify警告
        const game = this.webGameList.find(({ id }) => id === playerRankData.gameId)
        if (!game) return this.$notify.warning(this.$t('game_not_available'))
        if (game.blocked) return this.$notify.warning(this.$t('game_access_denied_message'))
        this.currGameRtp = null
        this.currGameRtp = this.platformChecker(playerRankData)
        this.showGameIntroDialogStatus = true
      }
    },
    watch: {
      isLogin: {
        async handler(val) {
          if (!val) {
            this.userPlayerData = {}
            this.$refs.playerRankingSectionRef.resetVisibleNames()
          } else {
            this.$refs.winnersSectionRef.updateUserLeaderboardData()
          }
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .text-wrap {
    word-break: break-all;
    white-space: break-spaces;
  }

  .winners {
    padding: 64px 8.4%;
  }
</style>

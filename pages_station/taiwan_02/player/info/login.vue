<template>
  <v-container class="pt-0 pt-md-16 px-0 pb-0 pb-md-15">
    <v-row no-gutters justify="center" :class="$vuetify.breakpoint.smAndDown ? '' : 'pt-6'">
      <v-col xl="8" lg="10" cols="12">
        <v-row no-gutters justify="start" class="pl-0 pl-md-1">
          <loginInfoContent
            v-if="isLogin"
            style="width: 100%"
            :login-headers="loginHeaders"
            :white-device="whiteDevice"
          />
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
  import menu from '~/mixins/menu.js'
  import convertTime from '~/utils/convertTime'
  export default {
    name: 'LoginInfoPage',
    mixins: [menu],
    components: {
      loginInfoContent: () => import('@/components/player_info/loginInfoContent')
    },
    data() {
      return {
        loginHeaders: [
          {
            text:
              this.$t('at') + `(${convertTime.getGMTOffset(this.$UIConfig.timeStamp.timezone)})`,
            value: 'date',
            width: '33%',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          },
          {
            text: 'IP' + this.$t('address'),
            value: 'ip',
            width: '33%',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          },
          {
            text: this.$t('device'),
            value: 'device',
            width: '34%',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          }
        ],
        whiteDevice: [
          {
            text: this.$t('device'),
            value: 'name',
            width: '86%',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          },
          {
            text: this.$t('operation'),
            value: 'id',
            width: '14%',
            class: 'grey-4 primary--text text-medium--text bg-table-medium',
            sortable: false
          }
        ]
      }
    },
    created() {
      this.$store.dispatch('role/updateUserDetail')
    },
    computed: {
      isLogin() {
        return this.$store.getters['role/isLogin']
      }
    },
    watch: {
      isLogin: {
        handler(val) {
          if (!val) this.$router.push({ path: this.localePath('/') })
        }
      }
    },
    mounted() {
      if (!this.isLogin) {
        this.$nextTick(() => {
          this.$notify.info(this.$t('plz_login'))
          this.$router.push({ path: this.localePath('/') })
        })
      }
    },
    methods: {}
  }
</script>

<style></style>

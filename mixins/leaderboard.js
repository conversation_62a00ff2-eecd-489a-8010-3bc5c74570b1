import convertTime from '~/utils/convertTime'

export default {
  data() {
    return {
      weeksDay: ['日', '一', '二', '三', '四', '五', '六'],
      oneDayInSeconds: 24 * 60 * 60,
      nowTimestamp: Math.floor(Date.now() / 1000)
    }
  },
  computed: {
    maintainSystem({ $store }) {
      return $store.getters['maintain/system']
    },
    isLogin({ $store }) {
      return $store.getters['role/isLogin']
    },
    userName({ $store }) {
      return $store.getters['role/userName']
    },
    topPlayersData({ $store }) {
      return $store.getters['leaderboard/topPlayersData']
    },
    activeRank({ $store }) {
      return $store.getters['leaderboard/activeRank']
    },
    selectPlayerData({ $store }) {
      return $store.getters['leaderboard/selectPlayerData']
    },
    lobbyGameList({ $store }) {
      return $store.getters['allStar/lobbys']
    },
    webGameList({ $store }) {
      return $store.getters['leaderboard/webGameList']
    },
    isLoading({ $store }) {
      return $store.getters['leaderboard/isLoading']
    },
    isWinsRank() {
      return this.activeRank === 0 || this.activeRank === 2
    },
    isDayRank() {
      return this.activeRank === 0 || this.activeRank === 1
    },
    isWinRankSelected() {
      return /0|2/.test(this.activeRank)
    },
    isBreakpoint() {
      return {
        homeRankingData: {
          sm: this.$vuetify.breakpoint.width <= 840,
          xs: this.$vuetify.breakpoint.width <= 560
        },
        pageRankingData: {
          lg: this.$vuetify.breakpoint.width >= 1400,
          sm: this.$vuetify.breakpoint.width <= 780
        }
      }
    },
    isPastToday1230() {
      const todayDate = this.dateFormat(this.nowTimestamp, 'YYYY-MM-DD')
      return this.nowTimestamp > Math.floor(new Date(`${todayDate}T12:30:00`).getTime() / 1000)
    },
    isMonday() {
      return new Date().getDay() === 1
    },
    dayTimestamp() {
      const yesterdayTimestamp = this.nowTimestamp - this.oneDayInSeconds
      const dayBeforeYesterdayTimestamp = yesterdayTimestamp - this.oneDayInSeconds

      return {
        today: this.dateFormat(this.nowTimestamp, 'YYYY-MM-DD'),
        todaySec: this.dateFormat(this.nowTimestamp, 'YYYY-MM-DD HH:mm:ss'),
        yesterday: this.dateFormat(yesterdayTimestamp, 'YYYY-MM-DD'),
        dayBeforeYesterday: this.dateFormat(dayBeforeYesterdayTimestamp, 'YYYY-MM-DD')
      }
    },
    lastWeekDayBeforeToday() {
      // 日榜中午12:30後可查詢昨天資料，12:30前只能查詢前天資料
      const lastWeek = []
      const initalDay = this.isPastToday1230 ? 1 : 2
      for (let i = initalDay; i < initalDay + 7; i++) {
        const timestamp = this.nowTimestamp - i * this.oneDayInSeconds
        const dateStr = this.dateFormat(timestamp, 'YYYY/MM/DD')
        const day = new Date(timestamp * 1000).getDay()
        const d = {}
        d.date = `${dateStr}(${this.weeksDay[day]})`
        d.beginAt = this.dateFormat(timestamp, 'YYYY-MM-DD')
        d.endAt = this.dateFormat(timestamp, 'YYYY-MM-DD')

        lastWeek.push(d)
      }
      return lastWeek
    },
    fourWeeksAgoDate() {
      // 週榜每個禮拜一中午12:30後可查詢上週資料
      let adjustedTimestamp =
        this.isMonday && !this.isPastToday1230
          ? this.nowTimestamp - 7 * this.oneDayInSeconds
          : this.nowTimestamp

      const fourWeekArr = []
      for (let i = 0; fourWeekArr.length < 4; i++) {
        const timestamp = adjustedTimestamp - i * this.oneDayInSeconds
        const day = new Date(timestamp * 1000).getDay()

        if (day === 0) {
          const startTimestamp = timestamp - 6 * this.oneDayInSeconds
          fourWeekArr.push({
            date: `${this.dateFormat(startTimestamp, 'YYYY/MM/DD')}(${
              this.weeksDay[1]
            })~${this.dateFormat(timestamp, 'YYYY/MM/DD')}(${this.weeksDay[day]})`,
            beginAt: this.dateFormat(startTimestamp, 'YYYY-MM-DD'),
            endAt: this.dateFormat(timestamp, 'YYYY-MM-DD')
          })
        }
      }
      return fourWeekArr
    }
  },
  methods: {
    dateFormat(timestamp, format = 'YYYY/MM/DD') {
      return convertTime.convertUnixTime(timestamp, format)
    },
    async getWebGameList(gameId) {
      // 只處理 user 個人排行榜 gameId
      if (this.isLogin && gameId) {
        await this.$store.dispatch('leaderboard/getWebGameList', [gameId])
        return
      }
      // 過濾已存在的遊戲
      const gameIds =
        this.topPlayersData?.list
          ?.filter((item) => item.platform)
          ?.map((item) => item.gameId)
          ?.filter(Boolean) || []

      if (gameIds.length === 0) return
      await this.$store.dispatch('leaderboard/getWebGameList', gameIds)
    },
    async fetchGameRtpHandler() {
      const gameIds =
        this.topPlayersData?.count &&
        this.topPlayersData?.list
          .map((item) => {
            if (item.platform) return item.gameId
          })
          .filter(Boolean)
      if (!gameIds?.length) return
      await this.$store.dispatch('leaderboard/fetchGameRtp', gameIds)
    },
    hasLeaderboardCache(dateParams) {
      dateParams.key = this.isWinRankSelected ? 'rank' : 'odds'
      const serializeKey = (obj) => {
        return Object.keys(obj)
          .sort()
          .map((k) => `${k}:${obj[k]}`)
          .join('|')
      }
      const cacheKey = serializeKey(dateParams)
      const cacheExist = this.$store.getters['leaderboard/leaderboardCache'].has(cacheKey)
      if (cacheExist) {
        this.$store.commit(
          'leaderboard/SET_TOP_PLAYERS_DATA',
          this.$store.getters['leaderboard/leaderboardCache'].get(cacheKey)
        )
        return true
      } else return false
    },
    async getTopPlayersData(dateParams) {
      await this.$store.dispatch('maintain/fetch')
      if (this.maintainSystem[0].maintaining)
        return this.$store.commit('leaderboard/RESET_TOP_PLAYERS_DATA')
      // 取得排行榜資料
      if (this.hasLeaderboardCache(dateParams)) return
      await this.$store.dispatch(
        this.isWinRankSelected ? 'leaderboard/getWinRanking' : 'leaderboard/getOddsRanking',
        dateParams
      )
    },
    async getUserPlayer(dateParams) {
      if (!this.isLogin) return null

      try {
        const apiMethod = this.isWinRankSelected ? 'getWinUserRanking' : 'getOddsUserRanking'
        const { data } = await this.$clientApi.leaderboard[apiMethod]({
          username: this.userName,
          ...dateParams
        })

        if (!data?.count) return data

        await this.getWebGameList(data.list.gameId)
        const platformDetails = this.setPlatformGameDetails(data.list)

        return {
          ...data,
          list: {
            ...data.list,
            ...platformDetails
          }
        }
      } catch (error) {
        console.error('Failed to fetch user leaderboard data:', error)
      }
    },
    setPlatformGameDetails({ gameId, platform }) {
      // 取得不同平台的遊戲的圖片跟名稱
      if (!gameId) return {}
      let game = null

      const defaultImageUrl = require('~/assets/image/leaderboard/platform-default.png')
      const createPlaceholder = (textKey, isAppPlatform = false) => ({
        text: textKey,
        game_logo_url: defaultImageUrl,
        isAppPlatform
      })

      if (platform) {
        if (Array.isArray(this.webGameList) && this.webGameList.length > 0) {
          game = this.webGameList.find((game) => game.id === gameId)
          if (!game) {
            return createPlaceholder('game_farewell_placeholder')
          }
          if (game.blocked) {
            return createPlaceholder('access_pending_placeholder')
          }
          return {
            game_logo_url: game.thumbPath
              ? `${process.env.IMAGE_URL}${game.thumbPath}`
              : defaultImageUrl,
            text: game.name,
            code: game.id,
            ...game
          }
        } else return createPlaceholder('game_farewell_placeholder')
      } else {
        const lobbyGameFound = this.lobbyGameList.find((game) => {
          const platformId = game.code.split('-')[2]
          return platformId === String(gameId).padStart(3, 0).padStart(4, 2)
        })
        // 處理lobbyGameList無返回遊戲圖片
        if (!lobbyGameFound) {
          return createPlaceholder('game_not_found_placeholder')
        }
        return {
          ...lobbyGameFound,
          game_logo_url: lobbyGameFound?.game_logo_url || defaultImageUrl,
          isAppPlatform: true
        }
      }
    },
    async getPlayerInfo(username) {
      if (username === this.userName) {
        // 玩家是自己
        this.$nuxt.$loading.start()
        const role = await this.getPlayerData(username)
        this.$nuxt.$loading.finish()
        this.setSelectPlayerInfo(role)
        await this.$store.dispatch('social/setSingleFriendStatus', role)
        this.$nuxt.$emit('root:showPlayerInfoCardDialogStatus', {
          show: true,
          fromLeaderboard: true
        })
      } else {
        // 玩家是別人
        this.$store.commit('leaderboard/RESET_SELECT_PLAYER_DATA')
        const uData = await this.getPlayerData(username)
        this.$store.commit('leaderboard/SET_SELECT_PLAYER_DATA', uData)
        await this.$store.dispatch('social/setSingleFriendStatus', this.selectPlayerData)
      }
    },
    platformChecker(playerRankData) {
      if (!playerRankData?.platform) return
      const rtpData = this.$store.getters['leaderboard/getRtpData'](playerRankData.gameId)
      // 設置當前遊戲RTP數據
      return {
        ...playerRankData,
        ...rtpData,
        maintaining: playerRankData?.maintaining
      }
    }
  }
}

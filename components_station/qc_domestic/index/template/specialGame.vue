<template>
  <v-container fluid class="pa-0">
    <!-- recent cookie -->
    <v-row v-if="!acceptCookiePolicy || (!isLogin && recentGameList.length === 0)" no-gutter>
      <v-col class="pa-0" cols="12">
        <div class="pa-0 my-4 my-md-6">
          <span class="font-weight-bold grey-3--text custom-text-noto text-caption">
            {{ $t(!acceptCookiePolicy ? 'accept_cookie_noty' : 'recent_game_noty') }}
          </span>
        </div>
      </v-col>
    </v-row>
    <!-- recent games -->
    <v-row v-if="acceptCookiePolicy && recentGameList.length > 0" no-gutters id="recentGames">
      <v-col cols="12">
        <v-row no-gutters align="center">
          <span class="material-symbols-outlined gradient-primary--text mail-button-icon-size">
            history
          </span>
          <span class="font-weight-bold gradient-default--text custom-text-noto ml-2 mr-4 mb-1">
            {{ $t('recent_games') }}
          </span>
          <gradientDivider prop-type="squareToArrow" />
        </v-row>
      </v-col>
      <v-col cols="12" class="swiper-container mt-4">
        <div class="top-games-swiper-item mr-lg-0 mr-md-n6 mr-sm-n4 mr-n4">
          <swiper
            class="swiper"
            :options="gameCardswiperOption"
            ref="recentGamesSwiper"
            @transitionEnd="swiperIndexChangeEvent('recentGamesSwiper')"
          >
            <swiper-slide
              v-for="(game, index) in recentGameList"
              :key="`gamecard_${game.id}_${index}`"
              :class="{ 'mr-lg-0 mr-md-6 mr-sm-4 mr-4': index === recentGameList.length - 1 }"
            >
              <gameCard
                :game-category-id="game.categoryType"
                :index="index"
                :game="game"
                :show-main-daily-rtp="true"
                :v-card-margin-x="'mx-0'"
              />
            </swiper-slide>
          </swiper>
        </div>
        <v-btn
          v-show="
            $vuetify.breakpoint.lgAndUp &&
            btnStatus.recentGamesSwiper.pre &&
            recentGameList.length > 6
          "
          fab
          min-width="0"
          small
          class="game-card-swiper-custom-button-prev button-content--text"
          color="gradient-button"
          @click="slideGameCardButton('recentGamesSwiper', 'prev')"
        >
          <span class="material-symbols-outlined"> navigate_before </span>
        </v-btn>
        <v-btn
          v-show="
            $vuetify.breakpoint.lgAndUp &&
            btnStatus.recentGamesSwiper.next &&
            recentGameList.length > 6
          "
          fab
          min-width="0"
          small
          class="game-card-swiper-custom-button-next button-content--text"
          color="gradient-button"
          @click="slideGameCardButton('recentGamesSwiper', 'next')"
        >
          <span class="material-symbols-outlined"> navigate_next </span>
        </v-btn>
      </v-col>
    </v-row>
    <!--live games -->
    <v-row no-gutters id="liveGames" v-if="liveGames.length !== 0">
      <v-col cols="12">
        <v-row no-gutters align="center">
          <span
            class="font-weight-bold text-h5 gradient-primary--text custom-text-noto mr-2 mr-sm-4"
          >
            {{ $t('live_human') }}
          </span>
          <template v-if="liveGames.length > 6">
            <gradientDivider prop-type="squareToArrow" class="mr-4" />
            <v-btn
              class="my-2"
              outlined
              color="primary"
              :small="$vuetify.breakpoint.smAndDown"
              @click="goToLobbyWithGameSortType(2, 200)"
            >
              {{ $t('explore_all') }}
            </v-btn>
          </template>
          <template v-else>
            <gradientDivider prop-type="squareToArrow" />
          </template>
        </v-row>
      </v-col>
      <v-col cols="12" class="swiper-container mt-4">
        <div class="live-games-swiper-item mr-lg-0 mr-md-n6 mr-sm-n4 mr-n4">
          <swiper
            class="swiper"
            :options="gameCardswiperOption"
            ref="liveGamesSwiper"
            @transitionEnd="swiperIndexChangeEvent('liveGamesSwiper')"
          >
            <swiper-slide
              v-for="(game, index) in liveGames"
              :key="`gamecard_${game.id}_${index}`"
              :class="{ 'mr-lg-0 mr-md-6 mr-sm-4 mr-4': index === liveGames.length - 1 }"
            >
              <gameCard
                :game-category-id="200"
                :index="index"
                :game="game"
                :show-main-daily-rtp="false"
                :v-card-margin-x="'mx-0'"
              />
            </swiper-slide>
          </swiper>
        </div>
        <v-btn
          v-show="
            $vuetify.breakpoint.lgAndUp &&
            btnStatus.liveGamesSwiper.pre &&
            showGameSlideBtn(liveGames, 4, 6)
          "
          fab
          min-width="0"
          small
          class="game-card-swiper-custom-button-prev button-content--text"
          color="gradient-button"
          @click="slideGameCardButton('liveGamesSwiper', 'prev')"
        >
          <span class="material-symbols-outlined"> navigate_before </span>
        </v-btn>
        <v-btn
          v-show="
            $vuetify.breakpoint.lgAndUp &&
            btnStatus.liveGamesSwiper.next &&
            showGameSlideBtn(liveGames, 4, 6)
          "
          fab
          min-width="0"
          small
          class="game-card-swiper-custom-button-next button-content--text"
          color="gradient-button"
          @click="slideGameCardButton('liveGamesSwiper', 'next')"
        >
          <span class="material-symbols-outlined"> navigate_next </span>
        </v-btn>
      </v-col>
    </v-row>
    <!-- top games -->
    <v-row no-gutters id="topGames">
      <v-col cols="12">
        <v-row no-gutters align="center">
          <span
            class="font-weight-bold text-h5 gradient-primary--text custom-text-noto mr-2 mr-sm-4"
          >
            {{ $t('top_games') }}
          </span>
          <gradientDivider prop-type="squareToArrow" class="mr-4" />
          <v-btn
            class="my-2"
            outlined
            color="primary"
            :small="$vuetify.breakpoint.smAndDown"
            @click="goToLobbyWithGameSortType(2)"
          >
            {{ $t('explore_all') }}
          </v-btn>
        </v-row>
      </v-col>
      <v-col cols="12" class="swiper-container mt-4">
        <div class="top-games-swiper-item mr-lg-0 mr-md-n6 mr-sm-n4 mr-n4">
          <swiper
            class="swiper"
            :options="gameCardswiperOption"
            ref="topGamesSwiper"
            @transitionEnd="swiperIndexChangeEvent('topGamesSwiper')"
          >
            <swiper-slide
              v-for="(game, index) in topGames"
              :key="`gamecard_${game.id}_${index}`"
              :class="{ 'mr-lg-0 mr-md-6 mr-sm-4 mr-4': index === topGames.length - 1 }"
            >
              <gameCard
                :game-category-id="100"
                :index="index"
                :game="game"
                :show-main-daily-rtp="true"
                :v-card-margin-x="'mx-0'"
              />
            </swiper-slide>
          </swiper>
        </div>
        <v-btn
          v-show="
            $vuetify.breakpoint.lgAndUp &&
            btnStatus.topGamesSwiper.pre &&
            showGameSlideBtn(topGames, 4, 6)
          "
          fab
          min-width="0"
          small
          class="game-card-swiper-custom-button-prev button-content--text"
          color="gradient-button"
          @click="slideGameCardButton('topGamesSwiper', 'prev')"
        >
          <span class="material-symbols-outlined"> navigate_before </span>
        </v-btn>
        <v-btn
          v-show="
            $vuetify.breakpoint.lgAndUp &&
            btnStatus.topGamesSwiper.next &&
            showGameSlideBtn(topGames, 4, 6)
          "
          fab
          min-width="0"
          small
          class="game-card-swiper-custom-button-next button-content--text"
          color="gradient-button"
          @click="slideGameCardButton('topGamesSwiper', 'next')"
        >
          <span class="material-symbols-outlined"> navigate_next </span>
        </v-btn>
      </v-col>
    </v-row>
    <!-- special games -->
    <v-row no-gutters v-for="groupItem in group" :key="groupItem" class="mt-2">
      <v-col cols="12">
        <v-row no-gutters align="center">
          <span
            class="font-weight-bold text-h5 gradient-primary--text custom-text-noto mr-2 mr-sm-4"
          >
            {{ $t(groupItem) }}
          </span>
          <gradientDivider prop-type="squareToArrow" />
        </v-row>
      </v-col>
      <v-col cols="12" class="swiper-container mt-1">
        <div class="swiper-item pb-4 mr-lg-0 mr-md-n6 mr-sm-n4 mr-n4">
          <swiper
            class="swiper"
            :options="providerSwiperOption"
            :ref="groupItem"
            @transitionEnd="swiperIndexChangeEvent(groupItem)"
          >
            <swiper-slide
              v-for="(game, index) in filterSelectedSeriesList(groupItem)"
              :key="`${groupItem}_${game.id}_${index}`"
              :class="{
                'mr-lg-0 mr-md-6 mr-sm-4 mr-4':
                  index === filterSelectedSeriesList(groupItem).length - 1
              }"
            >
              <specialGameCard :game="game" />
            </swiper-slide>
          </swiper>
        </div>
        <v-btn
          v-show="showSlideBtn(groupItem) && btnStatus[groupItem].pre"
          fab
          min-width="0"
          small
          class="provider-swiper-custom-button-prev button-content--text"
          color="gradient-button"
          @click="toSlideGameProviderButton(groupItem, 'prev')"
        >
          <span class="material-symbols-outlined"> navigate_before </span>
        </v-btn>
        <v-btn
          v-show="showSlideBtn(groupItem) && btnStatus[groupItem].next"
          fab
          min-width="0"
          small
          class="provider-swiper-custom-button-next button-content--text"
          color="gradient-button"
          @click="toSlideGameProviderButton(groupItem, 'next')"
        >
          <span class="material-symbols-outlined"> navigate_next </span>
        </v-btn>
      </v-col>
    </v-row>
    <!-- recommend games -->
    <v-row no-gutters id="recommendGames" class="mt-2">
      <v-col cols="12">
        <v-row no-gutters align="center">
          <span
            class="font-weight-bold text-h5 gradient-primary--text custom-text-noto mr-2 mr-sm-4"
          >
            {{ $t('newest_games') }}
          </span>
          <gradientDivider prop-type="squareToArrow" class="mr-4" />
          <v-btn
            class="my-2"
            outlined
            color="primary"
            :small="$vuetify.breakpoint.smAndDown"
            @click="goToLobbyWithGameSortType(3)"
            >{{ $t('explore_all') }}</v-btn
          >
        </v-row>
      </v-col>
      <v-col cols="12" class="swiper-container mt-4">
        <div class="hot-games-swiper-item mr-lg-0 mr-md-n6 mr-sm-n4 mr-n4">
          <swiper
            class="swiper"
            :options="gameCardswiperOption"
            ref="recommendGamesSwiper"
            @transitionEnd="swiperIndexChangeEvent('recommendGamesSwiper')"
          >
            <swiper-slide
              v-for="(game, index) in recommendGames"
              :key="`gamecard_${game.id}_${index}`"
              :class="{ 'mr-lg-0 mr-md-6 mr-sm-4 mr-4': index === recommendGames.length - 1 }"
            >
              <gameCard
                :game-category-id="100"
                :index="index"
                :game="game"
                :show-main-daily-rtp="true"
                :v-card-margin-x="'mx-0'"
              />
            </swiper-slide>
          </swiper>
        </div>
        <v-btn
          v-show="
            $vuetify.breakpoint.lgAndUp &&
            btnStatus.recommendGamesSwiper.pre &&
            showGameSlideBtn(recommendGames, 4, 6)
          "
          fab
          min-width="0"
          small
          class="game-card-swiper-custom-button-prev button-content--text"
          color="gradient-button"
          @click="slideGameCardButton('recommendGamesSwiper', 'prev')"
        >
          <span class="material-symbols-outlined"> navigate_before </span>
        </v-btn>
        <v-btn
          v-show="
            $vuetify.breakpoint.lgAndUp &&
            btnStatus.recommendGamesSwiper.next &&
            showGameSlideBtn(recommendGames, 4, 6)
          "
          fab
          min-width="0"
          small
          class="game-card-swiper-custom-button-next button-content--text"
          color="gradient-button"
          @click="slideGameCardButton('recommendGamesSwiper', 'next')"
        >
          <span class="material-symbols-outlined"> navigate_next </span>
        </v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>
<script>
  // 請參考以下網址 https://v1.github.surmon.me/vue-awesome-swiper/
  //Q:Swiper.js使用遇到的问题总结onSlideChangeEnd回调偶尔触发，偶尔不触发等
  //A:https://blog.csdn.net/xm1037782843/article/details/87981190
  import { Swiper, SwiperSlide } from 'vue-awesome-swiper'
  import analytics from '@/mixins/analytics.js'

  const NUXT_ENV = process.env.NUXT_ENV
  const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default

  export default {
    name: 'specialGame',
    mixins: [analytics],
    components: {
      Swiper,
      SwiperSlide,
      specialGameCard: () => import('~/components/game/specialGameCard.vue'),
      gradientDivider: () => import('~/components/gradientDivider.vue'),
      gameCard: () => import('~/components/game/gameCard.vue')
    },
    data() {
      return {
        topGamesCount: 15,
        recommendGamesCount: 15,
        recentGamesCount: 10,
        liveGamesCount: 15,
        acceptCookiePolicy: true,
        btnStatus: {
          topGamesSwiper: {
            pre: false,
            next: true
          },
          recentGamesSwiper: {
            pre: false,
            next: true
          },
          recommendGamesSwiper: {
            pre: false,
            next: true
          },
          liveGamesSwiper: {
            pre: false,
            next: true
          }
        },
        gameProviderList: [],
        topGames: [],
        recommendGames: [],
        recentGameList: [],
        liveGames: [],
        tmpRecentGameList: [],
        tmpGameProviderList: [],
        tmpRecommendGames: [],
        tmpTopGames: [],
        tmpLiveGames: [],
        gameCardswiperOption: {
          allowTouchMove: true,
          slidesPerView: 'auto',
          spaceBetween: 16,
          fade: {
            crossFade: true
          },
          observer: true,
          observeParents: true
        },
        providerSwiperOption: {
          allowTouchMove: true,
          slidesPerView: 'auto',
          spaceBetween: 16,
          fade: {
            crossFade: true
          },
          observer: true,
          observeParents: true
        }
      }
    },
    computed: {
      isLogin({ $store }) {
        return $store.getters['role/isLogin']
      },
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      allGameList({ $store }) {
        return $store.getters['gameHall/allGameList']
      },
      playedGameList({ $store }) {
        return $store.getters['gameHall/playedGameList']
      },
      group() {
        const uniqueGroups = new Set(this.gameProviderList.map((game) => game.group))
        const uniqueGroupsArray = [...uniqueGroups]
        return uniqueGroupsArray
      },
      providers({ $store }) {
        return $store.getters['gameProvider/providers']
      }
    },
    watch: {
      group: {
        handler(val) {
          val.forEach((item) => {
            this.$set(this.btnStatus, item, {
              pre: false,
              next: true
            })
          })
        }
      },
      providers: {
        handler(val) {
          this.getAllGamesEvent(val)
        },
        immediate: true
      },
      allGameList: {
        handler(val) {
          const updateGameList = (list) => {
            return list
              .map((x) => {
                const gameData = val.find((game) => game.id === x.id)
                if (gameData) {
                  x.maintaining = gameData.maintaining
                  return x
                } else return undefined
              })
              .filter((notUndefined) => notUndefined !== undefined)
          }
          this.tmpTopGames = updateGameList(this.topGames)
          this.tmpRecentGameList = updateGameList(this.recentGameList)
          this.tmpRecommendGames = updateGameList(this.recommendGames)
          this.tmpGameProviderList = updateGameList(this.gameProviderList)
          this.tmpLiveGames = updateGameList(this.liveGames)
          this.topGames = []
          this.recentGameList = []
          this.recommendGames = []
          this.gameProviderList = []
          this.liveGames = []
          this.topGames = this.tmpTopGames
          this.recentGameList = this.tmpRecentGameList
          this.recommendGames = this.tmpRecommendGames
          this.gameProviderList = this.tmpGameProviderList
          this.liveGames = this.tmpLiveGames
        },
        deep: true
      },
      playedGameList: {
        async handler(val) {
          if (val) {
            await this.setupRecentGames()
            this.recentGameList = await this.fetchGameRtpHandler(
              this.recentGameList.slice(0, this.recentGamesCount)
            )
            this.tmpRecentGameList = this.recentGameList
          }
        }
      },
      isLogin: {
        async handler(val) {
          if (val) {
            const serverGamesId = await this.$clientApi.game.gamePlayed(this.userName)
            const gameIds = serverGamesId.list.map(Number)
            this.$store.commit('gameHall/SET_PLAYED_GAME_LIST', gameIds)
          }
        }
      }
    },
    async created() {
      this.$nuxt.$on('game:acceptCookiePolicyMessage', this.setupAcceptCookiePolicy)
    },
    mounted() {
      //Q:關於vue-awesome-swiper使用遇到的一些問題和心得（手動設定寬、手機翻轉問題、回呼函數、滑動）
      //A:https://blog.csdn.net/qq_27339423/article/details/80683656
      const self = this
      window.addEventListener(
        'orientationchange',
        () => {
          if (self.topGames > 0 && self.topGames > 0 && self.topGames > 0 && self.topGames > 0) {
            self.topGames = []
            self.recentGameList = []
            self.recommendGames = []
            self.gameProviderList = []
            self.$nuxt.$loading.start()
            setTimeout(() => {
              self.topGames = self.tmpTopGames
              self.recentGameList = self.tmpRecentGameList
              self.recommendGames = self.tmpRecommendGames
              self.gameProviderList = self.tmpGameProviderList
              self.$nuxt.$loading.finish()
            }, 200)
          }
        },
        false
      )
      this.acceptCookiePolicy = !!(
        this.$localStorage.get('accept_cookie_policy').expired &&
        this.$moment(this.$localStorage.get('accept_cookie_policy').expired).isAfter(this.$moment())
      )
    },
    beforeDestroy() {
      this.$nuxt.$off('game:acceptCookiePolicyMessage', this.setupAcceptCookiePolicy)
    },
    methods: {
      filterSelectedSeriesList(groupItem) {
        return this.gameProviderList.filter((game) => game.group === groupItem)
      },
      showSlideBtn(groupItem) {
        const seriesListLength = this.filterSelectedSeriesList(groupItem).length
        return (
          this.$vuetify.breakpoint.lgAndUp &&
          // 卡片張數過少時不顯示左右滑動按鈕
          (this.$vuetify.breakpoint.width <= 1270 ? seriesListLength > 2 : seriesListLength > 3)
        )
      },
      showGameSlideBtn(slideList, lgLength, lgUpLength) {
        const seriesListLength = slideList.length
        return (
          this.$vuetify.breakpoint.lgAndUp &&
          // 卡片張數過少時不顯示左右滑動按鈕
          (this.$vuetify.breakpoint.width <= 1270
            ? seriesListLength > lgLength
            : seriesListLength > lgUpLength)
        )
      },

      setupAcceptCookiePolicy(cookiePolicy) {
        this.acceptCookiePolicy = cookiePolicy
      },
      slideGameCardButton(refName, leftAndRight) {
        if (this.$refs[refName]) {
          if (leftAndRight === 'prev') this.$refs[refName].$swiper.slidePrev()
          if (leftAndRight === 'next') this.$refs[refName].$swiper.slideNext()
          this.swiperIndexChangeEvent(refName)
        }
      },
      toSlideGameProviderButton(refName, leftAndRight) {
        if (this.$refs[refName][0]) {
          if (leftAndRight === 'prev') this.$refs[refName][0].$swiper.slidePrev()
          if (leftAndRight === 'next') this.$refs[refName][0].$swiper.slideNext()
          this.swiperIndexChangeEvent(refName)
        }
      },
      async swiperIndexChangeEvent(refName) {
        const preActive = (value) => value > 0 && value <= 1
        const nextActive = (value) => value >= 0 && value < 1
        const setBtn = (name, active) => this.$set(this.btnStatus[refName], name, active)
        if (this.$refs[refName]) {
          switch (refName) {
            case 'liveGamesSwiper':
            case 'topGamesSwiper':
            case 'recentGamesSwiper':
            case 'recommendGamesSwiper':
              this.btnStatus[refName].pre = preActive(this.$refs[refName].$swiper.progress)
              this.btnStatus[refName].next = nextActive(this.$refs[refName].$swiper.progress)
              break
            default:
              if (this.$refs[refName][0]) {
                setBtn('pre', preActive(this.$refs[refName][0].$swiper.progress))
                setBtn('next', nextActive(this.$refs[refName][0].$swiper.progress))
              }
              break
          }
        }
      },
      async getGameProviderList() {
        let result = await this.$axios.get(
          process.env.IMAGE_URL +
            `/index/gameProvider/${loadConfig.client_id}/gameProvider.json?` +
            Math.random()
        )
        return result.data
      },
      getGameProviderTitle(gameProviderList) {
        const self = this
        gameProviderList.forEach((element) => {
          element.gameCover = process.env.IMAGE_URL + '/index/gameCover/' + element.gameCover
          element.gameImg = process.env.IMAGE_URL + '/index/gameImg/' + element.gameImg

          self.allGameList.forEach((item) => {
            if (element.id === item.id) {
              element.title = item.name
              element.rtp = item.rtp
              element.enable = item.enable
              element.vipLevel = item.vipLevel
              element.maintainBeginAt = item.maintainBeginAt
              element.maintainEndAt = item.maintainEndAt
              element.maintaining = item.maintaining
              element.platformId = item.platformId
              element.hasExp = item.hasExp
              element.hasRobot = item.hasRobot
            }
          })
        })
        //如果gameProvider沒有配對到遊戲，則不顯示
        gameProviderList = gameProviderList.filter((item) => item.title !== undefined)
        return gameProviderList
      },
      async fetchGameRtpHandler(gameList) {
        let gameIds = []
        //待後端能提供該款遊戲是否擁有rtp的資訊，需再增加遊戲是否有rtp的判斷
        gameList.forEach((element) => gameIds.push(element.id))
        if (gameIds.length !== 0) {
          // 每 20 筆 ID 發送一個請求
          let promises = []
          for (let i = 0; i < gameIds.length; i += 20) {
            let ids = gameIds.slice(i, i + 20)
            if (ids.length != 0) {
              promises.push(this.$clientApi.game.gameRTPList(ids))
            }
          }
          try {
            // 同時發送所有請求
            let results = await Promise.all(promises)
            // 處理每個請求的結果
            results.forEach((gameRTPList) => {
              if (gameRTPList)
                gameList.forEach((item) => {
                  const match = gameRTPList.list.find((obj) => obj.gameId === item.id)
                  if (match) {
                    item.dailyRtp = match.dailyRtp
                    item.weeklyRtp = match.weeklyRtp
                    item.monthlyRtp = match.monthlyRtp
                  }
                })
            })
          } catch (error) {
            console.log('error', error)
          }
        }
        return gameList
      },
      async getSortGameList(gameCategoryId, sortType) {
        const body = {
          gameCategoryId: gameCategoryId,
          sortType: sortType,
          lang: this.$i18n.locale,
          limit: 300
        }
        let gameList = await this.$clientApi.game.gameSortList(body)
        return gameList
      },
      async getTopGames() {
        let topGames = await this.getSortGameList(100, 2)
        topGames = topGames.count > 0 ? topGames.list : []
        // 取得遊戲廠商相關資訊
        if (topGames.length !== 0) {
          const platfrom = this.$store.getters['maintain/platform']
          // rerender maintain state
          platfrom.forEach((platfromItem) => {
            topGames.forEach((gameItem) => {
              if (gameItem.thumbUrl === null) {
                gameItem.thumbUrl = this.$store.getters['gameHall/gameDefaultImg']
              }
              if (platfromItem.id == gameItem.platformId && platfromItem.maintaining) {
                gameItem.maintaining = true
                gameItem.maintainBeginAt = platfromItem.maintainBeginAt
                gameItem.maintainEndAt = platfromItem.maintainEndAt
              }
            })
          })
          //篩選掉維護的遊戲
          topGames = topGames.filter((item) => item.maintaining !== true)
        }
        return topGames
      },
      async getRecommendGames() {
        let recommendGames = await this.getSortGameList(100, 3)
        recommendGames = recommendGames.count > 0 ? recommendGames.list : []
        // 取得遊戲廠商相關資訊
        if (recommendGames.length !== 0) {
          const platfrom = this.$store.getters['maintain/platform']
          // rerender maintain state
          platfrom.forEach((platfromItem) => {
            recommendGames.forEach((gameItem) => {
              if (gameItem.thumbUrl === null) {
                gameItem.thumbUrl = this.$store.getters['gameHall/gameDefaultImg']
              }
              if (platfromItem.id == gameItem.platformId && platfromItem.maintaining) {
                gameItem.maintaining = true
                gameItem.maintainBeginAt = platfromItem.maintainBeginAt
                gameItem.maintainEndAt = platfromItem.maintainEndAt
              }
            })
          })
          //篩選掉維護的遊戲
          recommendGames = recommendGames.filter((item) => item.maintaining !== true)
        }
        return recommendGames
      },
      async getLiveGames() {
        let liveGames = await this.getSortGameList(200, 2)
        liveGames = liveGames.count > 0 ? liveGames.list : []
        // 取得遊戲廠商相關資訊
        if (liveGames.length !== 0) {
          const platfrom = this.$store.getters['maintain/platform']
          // rerender maintain state
          platfrom.forEach((platfromItem) => {
            liveGames.forEach((gameItem) => {
              if (gameItem.thumbUrl === null) {
                gameItem.thumbUrl = this.$store.getters['gameHall/gameDefaultImg']
              }
              if (platfromItem.id == gameItem.platformId && platfromItem.maintaining) {
                gameItem.maintaining = true
                gameItem.maintainBeginAt = platfromItem.maintainBeginAt
                gameItem.maintainEndAt = platfromItem.maintainEndAt
              }
            })
          })
          //真人遊戲為常駐廣告，不會因為有維護而被篩選掉
        }
        return liveGames
      },
      goToLobbyWithGameSortType(gameSortType, gameCategory = null) {
        this.$router.push({
          path: this.localePath('/game'),
          query: {
            ...(gameCategory && { gameCategory }),
            gameSortType
          }
        })
      },
      getAllGamesEvent(providers) {
        if (providers.length > 0) {
          const hasRtp = (item, provider) =>
            item.platformId == provider.id && provider.hasRtp === true
          const initRtp = (item) => {
            item.dailyRtp = undefined
            item.weeklyRtp = undefined
            item.monthlyRtp = undefined
          }
          const getGames = (showGamesCount, games) => {
            games = games.filter((item) => providers.some((provider) => hasRtp(item, provider))) //篩選掉沒有提供rtp的遊戲
            games.forEach((item) => initRtp(item)) //在呈現之前需先為rtp建立property避免rtp資料更新後畫面不會更新
            return games.slice(0, showGamesCount) //先呈現 RTP最後更新
          }
          //get top Games
          this.getTopGames().then((topGames) => {
            this.topGames = getGames(this.topGamesCount, topGames)
            this.tmpTopGames = this.topGames
          })
          //getRecommendGames
          this.getRecommendGames().then((recommendGames) => {
            this.recommendGames = getGames(this.recommendGamesCount, recommendGames)
            this.tmpRecommendGames = this.recommendGames
          })
          //getLiveGams
          this.getLiveGames().then((liveGames) => {
            // 真人視訊沒有RTP，故不呼叫getGames
            this.liveGames = liveGames.slice(0, this.liveGamesCount)
            this.tmpLiveGames = this.liveGames
          })

          //getGameProvider
          this.$store.dispatch('gameHall/fetchAllGameList').then(async () => {
            let gameProviderList = await this.getGameProviderList()
            gameProviderList = this.getGameProviderTitle(gameProviderList)
            gameProviderList.forEach((item) => initRtp(item)) //在呈現之前需先為rtp建立property避免rtp資料更新後畫面不會更新
            this.gameProviderList = gameProviderList //先呈現 RTP最後更新
            this.tmpGameProviderList = this.gameProviderList
            this.$store.dispatch('gameHall/fechPlayedGameList')
            this.setupRecentGames()
            this.fetchAllGameRTPHandler()
          })
        }
      },
      async setupRecentGames() {
        const headerData = {
          username: this.$store.getters['role/userName']
            ? this.$store.getters['role/userName']
            : null,
          alias: null
        }
        let recentGameValue = await this.$clientApi.game.gameList(
          headerData,
          this.playedGameList,
          this.$i18n.locale,
          process.env.STATION
        )
        recentGameValue = recentGameValue.count > 0 ? recentGameValue.list : []
        const gameList =
          recentGameValue.length > 0
            ? this.playedGameList.map((id) => recentGameValue.find((item) => item.id === id))
            : []
        let gameListValue = []
        gameList.forEach((game) => {
          if (game)
            gameListValue.push({
              id: game.id,
              rtp: game.rtp,
              name: game.name,
              enable: game.enable,
              hasDemo: game.hasDemo,
              thumbUrl: game.thumbUrl,
              vipLevel: game.vipLevel,
              platformId: game.platformId,
              canRedirect: game.canRedirect,
              publishedAt: game.publishedAt,
              maintaining: game.maintaining,
              maintainEndAt: game.maintainEndAt,
              maintainBeginAt: game.maintainBeginAt,
              dailyRtp: undefined,
              weeklyRtp: undefined,
              monthlyRtp: undefined,
              hasActive: game.hasActive,
              hasExp: game.hasExp,
              hasRobot: game.hasRobot,
              categoryType: game.categoryType
            })
        })
        this.recentGameList = gameListValue
      },
      async fetchAllGameRTPHandler() {
        const allGames = this.topGames.concat(
          this.recommendGames,
          this.gameProviderList,
          this.recentGameList
        )
        let gamesID = allGames.map((item) => item.id)
        gamesID = gamesID.filter((item, index) => gamesID.indexOf(item) === index)
        const gameRTPList = await this.$clientApi.game.gameRTPList(gamesID)
        this.setupRTPList(this.topGames, gameRTPList)
        this.setupRTPList(this.recommendGames, gameRTPList)
        this.setupRTPList(this.gameProviderList, gameRTPList)
        this.setupRTPList(this.recentGameList, gameRTPList)
        this.tmpTopGames = this.topGames
        this.tmpRecentGameList = this.recentGameList
        this.tmpRecommendGames = this.recommendGames
        this.tmpGameProviderList = this.gameProviderList
      },
      setupRTPList(gameSourceList, gameRTPList) {
        gameSourceList = gameSourceList.map((item) => {
          if (gameRTPList) {
            const match = gameRTPList.list.find((obj) => obj.gameId === item.id)
            if (match) {
              item.dailyRtp = match.dailyRtp
              item.weeklyRtp = match.weeklyRtp
              item.monthlyRtp = match.monthlyRtp
            }
          }
          return item
        })
      }
    }
  }
</script>
<style lang="scss" scoped>
  @mixin setSwiperStyles($gameProviderWidth, $hotGameWidth) {
    position: relative;
    overflow: visible;
    .swiper-item {
      overflow: hidden;
      .swiper {
        .swiper-slide {
          width: $gameProviderWidth;
          @if $gameProviderWidth == calc(25% - 16px) {
            min-width: 260px;
          }
        }
      }
    }
    .live-games-swiper-item {
      overflow: hidden;
      .swiper {
        .swiper-slide {
          width: $hotGameWidth;
          box-sizing: border-box;
        }
      }
    }
    .top-games-swiper-item {
      overflow: hidden;
      .swiper {
        .swiper-slide {
          width: $hotGameWidth;
          box-sizing: border-box;
        }
      }
    }
    .hot-games-swiper-item {
      overflow: hidden;
      .swiper {
        .swiper-slide {
          width: $hotGameWidth;
          box-sizing: border-box;
        }
      }
    }
    .game-card-swiper-custom-button-prev {
      position: absolute;
      left: -15px;
      top: 40%;
      transform: translateY(-50%);
      z-index: 10;
    }
    .game-card-swiper-custom-button-next {
      position: absolute;
      right: -15px;
      top: 40%;
      transform: translateY(-50%);
      z-index: 10;
    }
    .provider-swiper-custom-button-prev {
      position: absolute;
      left: -15px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
    }
    .provider-swiper-custom-button-next {
      position: absolute;
      right: -15px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
    }
  }

  @media screen and (min-width: 1271px) {
    .swiper-container {
      @include setSwiperStyles(calc(25% - 16px), calc(16.67% - 16px));
    }
  }

  @media screen and (max-width: 1270px) {
    .swiper-container {
      @include setSwiperStyles(320px, calc(21% - 16px));
    }
  }

  @media screen and (max-width: 960px) {
    .swiper-container {
      @include setSwiperStyles(268px, calc(28.5% - 16px));
    }
  }

  @media screen and (max-width: 600px) {
    .swiper-container {
      @include setSwiperStyles(300px, calc(40% - 16px));
    }
  }
</style>

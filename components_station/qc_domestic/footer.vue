<template>
  <v-footer
    color="footer-fill"
    class="pt-4 pb-10 px-0"
    :class="[
      {
        'pb-0': $vuetify.breakpoint.smOnly,
        'mb-10': $vuetify.breakpoint.smAndDown,
        'notch-left': hasLeftNotch,
        'notch-right': hasRightNotch
      }
    ]"
    width="100%"
    bottom
  >
    <v-row no-gutters justify="center" class="px-xl-0 px-lg-0 px-md-6 px-sm-4 px-4">
      <v-col cols="12" lg="9" class="mw-75-v">
        <!-- news & download 2 btn -->
        <v-row no-gutters class="custom-text-noto">
          <!-- download btn -->
          <v-btn text class="small" @click="toPage('/downloads')">
            <v-row no-gutters align="center" class="text-button footer-button--text">
              <span class="material-symbols-outlined"> system_update </span>
              <span class="ml-2">{{ $t('download') }}</span>
            </v-row>
          </v-btn>
          <!-- 建立捷徑 -->
          <v-btn text class="small" @click="toPage('/teach/pwa')">
            <v-row no-gutters align="center" class="text-button footer-button--text">
              <v-icon>mdi-tray-arrow-down</v-icon>
              <span class="ml-2">{{ $t('shortcut_tutorial') }}</span>
            </v-row>
          </v-btn>
        </v-row>
        <!-- contact & notice -->
        <v-row no-gutters justify="space-between" class="pt-4">
          <!-- contact -->
          <v-col class="custom-text-noto text-body-2" cols="12" md="6">
            <v-row no-gutters class="white--text pr-5">
              <v-col cols="12">
                <v-img
                  max-width="178"
                  height="20"
                  :src="require('~/assets/image/waninlogo.svg')"
                  class="mb-3"
                />
              </v-col>
              <v-col cols="12">
                <p class="mb-0">
                  {{ $t('copy_right') }}<br />
                  {{ $t('custom_call') }}<br />
                  {{ $t('custom_fax') }}：(04)2259-3887 <br />
                  {{ $t('service_email') }}：
                  <v-hover v-slot="{ hover }">
                    <a
                      class="text-decoration-none custom-transition-box"
                      :class="hover ? 'primary--text' : 'white--text'"
                      href="mailto:<EMAIL>"
                    >
                      <EMAIL>
                    </a>
                  </v-hover>
                </p>
              </v-col>
            </v-row>
          </v-col>
          <!-- notice -->
          <v-col
            class="white--text custom-text-noto text-body-2 pt-xl-0 pt-lg-0 pt-md-0 pt-sm-4 pt-4"
            cols="12"
            md="6"
          >
            <v-row no-gutters class="flex-nowrap">
              <div style="width: 48px">
                <v-img width="48px" height="48px" :src="getImage('icon/18plus.svg')" />
              </div>
              <div>
                <ul type="disc" class="ml-4 mb-0 white--text custom-text-noto text-body-2">
                  <li v-for="(noty, index) in footerConfig.footerNotyArray" :key="index">
                    {{ $t(noty) }}
                  </li>
                </ul>
              </div>
            </v-row>
          </v-col>
          <v-col cols="12" class="pt-4"> <v-divider /> </v-col>
        </v-row>
        <!-- license & lanuage -->
        <v-row no-gutters align="center" justify="space-between" class="pt-4 pb-2">
          <!-- license -->
          <v-col cols="12" md="6" class="custom-text-noto text-body-2">
            <v-card elevation="0" color="transparent" class="pr-5">
              <v-hover v-for="(info, index) in companyInfoList" :key="index" v-slot="{ hover }">
                <v-btn
                  elevation="0"
                  text
                  :small="$vuetify.breakpoint.xsOnly ? true : false"
                  class="no-shadow px-0 custom-transition-box"
                  color="transparent"
                  @click="goTo(info.url, info.name)"
                >
                  <span
                    class="text-decoration-none"
                    :class="hover ? 'primary--text' : 'white--text'"
                  >
                    {{ $t(info.name) }}
                  </span>
                  <span v-show="index !== companyInfoList.length - 1" class="white--text px-1">
                    |
                  </span>
                </v-btn>
              </v-hover>
            </v-card>
          </v-col>
          <!-- lanuage -->
          <v-col cols="12" md="6" class="pa-0 pt-xl-0 pt-lg-0 pt-md-0 pt-4">
            <v-card elevation="0" color="transparent" class="d-flex align-center justify-start">
              <locale v-if="localeStatus" />
              <v-card
                v-for="(item, idx) in footerConfig.communityList"
                :key="idx"
                class="cursor-pointer rouned-circle"
                :class="localeStatus ? 'ml-3' : 'ml-1 mr-2'"
                color="transparent"
                width="24"
                height="24"
                @click="goTo(item.link)"
              >
                <v-img
                  v-if="item.iconFile"
                  :src="getImage('icon/' + item.iconFile)"
                  width="100%"
                  height="100%"
                />
                <v-icon v-if="item.icon" class="footer-item--text white" v-text="item.icon" />
              </v-card>
              <!-- 只有星城有該logo -->
              <v-card
                class="ml-2 cursor-pointer rouned-circle"
                color="transparent"
                width="106"
                height="26"
                @click="goTo('https://www.xin-stars.com/')"
              >
                <!-- svg在電腦及ios看起來糊糊的，所以使用png -->
                <v-img :src="getImage('icon/footer-logo.png')" width="100%" height="100%" />
              </v-card>
            </v-card>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-footer>
</template>

<script>
  import images from '~/mixins/images'
  import orientation from '@/mixins/orientation.js'
  const NUXT_ENV = process.env.NUXT_ENV
  const STATION = process.env.STATION
  const stationConfig = require(`@/station/${STATION}/${NUXT_ENV}`).default

  export default {
    mixins: [images, orientation],
    components: {
      //翻譯 目前不需要  先註解
      locale: () => import('~/components/locale')
    },

    data() {
      const localeStatus = stationConfig.customLocaleList.length > 1

      return {
        localeStatus,
        footerList: this.$store.getters['navigation/footer']
      }
    },
    computed: {
      footerConfig() {
        return this.$UIConfig.footer
      },
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      companyInfoList({ $store }) {
        return $store.getters[`${STATION}/companyInfo/list`]
      }
    },
    methods: {
      goTo(link, name) {
        if (name === 'intellectual_property_policy') {
          if (!this.maintainSystem[0]?.maintaining) {
            // 如果名稱為intellectual_property_policy且沒有維護，使用 Vue Router 導航
            this.$router.push({ path: this.localePath(link) })
          }
          // 如果系統維護中，按鈕禁用
        } else {
          // 對於其他項目，保持原有的行為
          this.$lineOpenWindow.open(link, '_blank')
        }
      },
      toPage(link) {
        //換頁
        this.$router.push({ path: this.localePath(link) })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .custom-transition-box,
  .custom-transition-box span {
    transition: all 0.2s ease-in;
  }
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>

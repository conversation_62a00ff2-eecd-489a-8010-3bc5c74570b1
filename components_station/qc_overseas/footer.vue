<template>
  <v-footer
    v-if="!showGameModeStatus"
    color="footer-fill"
    class="pt-4 pb-10 px-0"
    :class="{
      'pb-0': $vuetify.breakpoint.smOnly,
      'mb-10': $vuetify.breakpoint.smAndDown,
      'notch-left': hasLeftNotch,
      'notch-right': hasRightNotch
    }"
    width="100%"
    bottom
  >
    <v-row no-gutters justify="center" class="px-xl-0 px-lg-0 px-md-6 px-sm-4 px-4">
      <v-col cols="12" lg="9" xl="9">
        <!-- news & download 2 btn -->
        <v-row no-gutters class="custom-text-noto">
          <!-- news btn -->
          <v-btn text class="small" @click="toPage('/news?type=2&page=1')">
            <v-row no-gutters align="center" class="text-button footer-button--text">
              <span class="material-symbols-outlined"> campaign </span>
              <span class="ml-2">{{ $t('announcement') }}</span>
            </v-row>
          </v-btn>
          <!-- 建立捷徑 -->
          <v-btn text class="small" @click="toPage('/teach/pwa')">
            <v-row no-gutters align="center" class="text-button footer-button--text">
              <v-icon>mdi-tray-arrow-down</v-icon>
              <span class="ml-2">{{ $t('shortcut_tutorial') }}</span>
            </v-row>
          </v-btn>
        </v-row>
        <!-- contact & notice -->
        <v-row no-gutters justify="space-between" class="pt-4">
          <!-- contact -->
          <v-col class="custom-text-noto text-body-2" cols="12" md="6" lg="5">
            <v-row no-gutters class="white--text pr-5">
              <v-col cols="12">
                <div class="d-flex justify-start align-center flex-nowrap mb-4">
                  <v-img
                    max-width="90"
                    height="25"
                    contain
                    :src="require('~/assets/image/malaysia_01/logo-pacify.png')"
                  />
                  <v-divider class="mx-4" vertical></v-divider>
                  <v-img
                    max-width="153"
                    height="25"
                    contain
                    :src="require('~/assets/image/malaysia_01/logo.png')"
                  />
                </div>
              </v-col>
              <v-col cols="12">
                <p class="mb-0">
                  PACIFY SERVICE CO., LTD.<br />
                  {{ 'Contact：' + serviceMail }}
                </p>
              </v-col>
            </v-row>
          </v-col>
          <!-- notice -->
          <v-col
            class="white--text custom-text-noto text-body-2 pt-xl-0 pt-lg-0 pt-md-0 pt-sm-4 pt-4"
            cols="12"
            md="6"
            lg="5"
          >
            <v-row no-gutters class="flex-nowrap">
              <div>
                <ul type="disc" class="ml-4 mb-0 white--text custom-text-noto text-body-2">
                  <li v-for="(noty, index) in footerConfig.footerNotyArray" :key="index">
                    {{ $t(noty) }}
                  </li>
                </ul>
              </div>
            </v-row>
          </v-col>
          <v-col cols="12" class="pt-4"> <v-divider /> </v-col>
        </v-row>
        <!-- license & lanuage -->
        <v-row no-gutters align="center" justify="space-between" class="pt-4 pb-2">
          <!-- license -->
          <v-col cols="12" md="6" lg="5" class="custom-text-noto text-body-2">
            <v-card elevation="0" color="transparent">
              <v-btn
                v-for="(info, index) in companyInfoList"
                :key="index"
                elevation="0"
                target="blank"
                text
                :small="$vuetify.breakpoint.xsOnly ? true : false"
                class="no-shadow px-0"
                color="transparent"
                @click="openDialog(info.id)"
              >
                <span class="white--text">
                  {{ $t(info.name) }}
                </span>
                <span v-show="index !== companyInfoList.length - 1" class="white--text px-1">
                  |
                </span>
              </v-btn>
            </v-card>
          </v-col>
          <!-- lanuage -->
          <v-col cols="12" md="6" lg="5" offset-lg="1" class="pa-0 pt-xl-0 pt-lg-0 pt-md-0 pt-4">
            <v-card elevation="0" color="transparent" class="d-flex align-center justify-start">
              <locale v-if="localeStatus" />
              <v-card
                v-for="(item, idx) in footerConfig.communityList"
                :key="idx"
                class="cursor-pointer rouned-circle"
                :class="localeStatus ? 'ml-3' : 'ml-1 mr-2'"
                color="transparent"
                width="24"
                height="24"
                @click="goTo(item.link)"
              >
                <v-img
                  v-if="item.iconFile"
                  :src="getImage('icon/' + item.iconFile)"
                  width="100%"
                  height="100%"
                />
                <v-icon v-if="item.icon" class="footer-item--text white" v-text="item.icon" />
              </v-card>
            </v-card>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-footer>
</template>

<script>
  import images from '~/mixins/images'
  import orientation from '@/mixins/orientation.js'
  const NUXT_ENV = process.env.NUXT_ENV
  const STATION = process.env.STATION
  const stationConfig = require(`@/station/${STATION}/${NUXT_ENV}`).default

  export default {
    mixins: [images, orientation],
    components: {
      //翻譯 目前不需要  先註解
      locale: () => import('~/components/locale')
    },

    data() {
      const localeStatus = stationConfig.customLocaleList.length > 1

      return {
        localeStatus,
        footerList: this.$store.getters['navigation/footer']
      }
    },
    computed: {
      footerConfig() {
        return this.$UIConfig.footer
      },
      companyInfoList({ $store }) {
        return $store.getters[`${STATION}/companyInfo/list`]
      },
      serviceMail({ $store }) {
        return $store.getters[`${STATION}/companyInfo/serviceMail`]
      },
      showGameModeStatus({ $store }) {
        return $store.getters['menu/showGameModeStatus']
      }
    },
    methods: {
      goTo(link) {
        this.$lineOpenWindow.open(link, '_blank')
      },
      toPage(link) {
        //換頁
        this.$router.push({ path: this.localePath(link) })
      },
      openDialog(type) {
        this.$store.commit(`${STATION}/companyInfo/SET_COMPANY_POLICY_TYPE`, type)
        this.$nuxt.$emit('root:showCompanyDialogStatus', true)
      }
    }
  }
</script>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .notch-left {
      padding-left: env(safe-area-inset-left) !important;
    }
    .notch-right {
      padding-right: env(safe-area-inset-right) !important;
    }
  }
</style>

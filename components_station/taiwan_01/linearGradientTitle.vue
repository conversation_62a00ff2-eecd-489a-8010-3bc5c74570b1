<template>
  <v-row no-gutters align="center" justify="center">
    <div class="gradient-border-left border-title-left"></div>
    <div
      class="gradient-primary--text font-weight-bold custom-text-noto text-h5 mx-4 title-heavy--text"
    >
      <span>{{ titleTmp }}</span>
    </div>
    <div class="gradient-border-right border-title-right"></div>
  </v-row>
</template>

<script>
  export default {
    name: 'LinearGradientTitle',
    props: {
      title: { type: String, default: '' }
    },
    data() {
      return {
        titleTmp: this.title
      }
    },
    watch: {
      title: {
        handler(val) {
          this.titleTmp = val
        },
        immediate: true
      }
    }
  }
</script>

<style scope lang="scss">
  .gradient-border-right {
    width: 56px;
    border: 1px solid;
    // replaceColor 以下可刪 已套用.border-title-right
    border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 1) 0%,
      rgba(247, 182, 117, 0) 100%
    );
    -webkit-border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 1) 0%,
      rgba(247, 182, 117, 0) 100%
    );
    -moz-border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 1) 0%,
      rgba(247, 182, 117, 0) 100%
    );
    -ms-border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 1) 0%,
      rgba(247, 182, 117, 0) 100%
    );
    border-image-slice: 1;
    -webkit-border-image-slice: 1;
    -moz-border-image-slice: 1;
    -ms-border-image-slice: 1;
    transform: rotate(180deg);
  }

  .gradient-border-left {
    width: 56px;
    border: 1px solid;
    // replaceColor 以下可刪 已套用.border-title-left
    border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 0) 0%,
      rgba(247, 182, 117, 1) 100%
    );
    -webkit-border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 0) 0%,
      rgba(247, 182, 117, 1) 100%
    );
    -moz-border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 0) 0%,
      rgba(247, 182, 117, 1) 100%
    );
    -ms-border-image-source: linear-gradient(
      270deg,
      rgba(247, 182, 117, 0) 0%,
      rgba(247, 182, 117, 1) 100%
    );
    border-image-slice: 1;
    -webkit-border-image-slice: 1;
    -moz-border-image-slice: 1;
    -ms-border-image-slice: 1;
    transform: rotate(180deg);
  }
</style>

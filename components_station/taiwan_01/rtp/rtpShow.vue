<template>
  <v-chip
    class="rtp-chip px-2 ma-0 w-100 custom-text-noto text-body-2"
    :color="backgroundColor"
    small
    label
    :text-color="chipTextColor"
  >
    <p class="w-100 mb-0 d-flex align-center" :class="justify" ref="rtpObserverTarget">
      <span class="custom-text-noto text-caption pr-1 text-game-square-rtp--text">
        <template v-if="text"> {{ $t(text) }} </template>
        {{ rtp }}%
      </span>
      <span
        v-show="!isIntroDialogShow && (breakpoint.lgAndDown || isOuterHover) && isInView"
        class="material-symbols-outlined"
      >
        <!-- animationLoop 控制播放功能，因為開啟時會導致背景音樂Dialog卡頓衝突 -->
        <lottie-vue-player
          :src="icon.includes('up') ? upOptions.animation : downOptions.animation"
          :autoplay="animationLoop"
          :loop="animationLoop"
          :key="`${animationLoop}-${icon}-hover`"
          style="width: 24px; height: 24px"
        />
      </span>
      <span
        v-show="(isIntroDialogShow || breakpoint.xlOnly) && !isOuterHover"
        class="material-symbols-outlined"
      >
        <lottie-vue-player
          :src="icon.includes('up') ? upOptions.default : downOptions.default"
          :autoplay="isIntroDialogShow ? animationLoop : false"
          :loop="isIntroDialogShow ? animationLoop : false"
          :key="`${animationLoop}-${icon}-default`"
          style="width: 24px; height: 24px"
      /></span>
    </p>
  </v-chip>
</template>
<script>
  import trendingDownWhite from '~/assets/lottie/trending_down_white_animation.json'
  import trendingDownWhiteDefault from '~/assets/lottie/trending_down_white_default.json'
  import trendingUpWhite from '~/assets/lottie/trending_up_white_animation.json'
  import trendingUpWhiteDefault from '~/assets/lottie/trending_up_white_default.json'
  import trendingDownGreen from '~/assets/lottie/trending_down_green_animation.json'
  import trendingDownGreenDefault from '~/assets/lottie/trending_down_green_default.json'
  import trendingUpRed from '~/assets/lottie/trending_up_red_animation.json'
  import trendingUpRedDefault from '~/assets/lottie/trending_up_red_default.json'

  export default {
    name: 'Rtpshow',
    props: {
      text: {
        type: String,
        default: ''
      },
      backgroundColor: {
        type: String,
        required: true
      },
      rtp: {
        type: Number,
        default: 0,
        required: true
      },
      icon: {
        type: String,
        required: true
      },
      iconColor: {
        type: String,
        required: true
      },
      chipTextColor: {
        type: String,
        required: true
      },
      justify: {
        type: String,
        default: 'justify-center'
      },
      isIntroDialogShow: {
        type: Boolean,
        default: false
      },
      isOuterHover: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        animationLoop: true,
        isInView: false
      }
    },
    computed: {
      downOptions() {
        const iconType = this.iconColor.split('--')[0]
        return {
          default: {
            white: JSON.stringify(trendingDownWhiteDefault),
            // rtpDashBoad 顯示則一律啟用動畫
            success: this.isIntroDialogShow
              ? JSON.stringify(trendingDownGreen)
              : JSON.stringify(trendingDownGreenDefault)
          }[iconType],
          animation: {
            white: JSON.stringify(trendingDownWhite),
            success: JSON.stringify(trendingDownGreen)
          }[iconType]
        }
      },
      upOptions() {
        const iconType = this.iconColor.split('--')[0]
        return {
          default: {
            white: JSON.stringify(trendingUpWhiteDefault),
            error: this.isIntroDialogShow
              ? JSON.stringify(trendingUpRed)
              : JSON.stringify(trendingUpRedDefault)
          }[iconType],
          animation: {
            white: JSON.stringify(trendingUpWhite),
            error: JSON.stringify(trendingUpRed)
          }[iconType]
        }
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    methods: {
      handleMusicDialog(isOpen) {
        this.$nextTick(() => {
          this.animationLoop = !isOpen
        })
      }
    },
    mounted() {
      this.$nuxt.$on('root:showMusicPlayerDialogStatus', this.handleMusicDialog)
      const observer = new IntersectionObserver(
        ([entry]) => {
          this.isInView = entry.isIntersecting
        },
        {
          root: null, // 觀察範圍為整個視窗 viewport
          threshold: 0 // 只要出現一點點就算進入畫面
        }
      )
      const target = this.$refs.rtpObserverTarget
      if (target) observer.observe(target)
    },
    beforeDestroy() {
      this.$nuxt.$off('root:showMusicPlayerDialogStatus', this.handleMusicDialog)
      if (this.$refs.rtpObserverTarget && this.observer) {
        this.observer.unobserve(this.$refs.rtpObserverTarget)
      }
    }
  }
</script>
<style lang="scss">
  .rtp-chip {
    .v-chip__content {
      width: 100%;
      .vue-lottie-player {
        background: transparent;
      }
      .lf-spinner {
        display: none;
      }
    }
  }
</style>

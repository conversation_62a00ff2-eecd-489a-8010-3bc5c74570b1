<template>
  <v-row
    no-gutters
    align="center"
    :class="[
      'topbar-height',
      'pl-2',
      'pl-sm-4',
      'w-100',
      { 'flex-nowrap': $vuetify.breakpoint.xsOnly }
    ]"
    :style="{ height: $vuetify.breakpoint.xsOnly ? '44px' : '38px' }"
  >
    <!-- back icon -->
    <template v-if="$vuetify.breakpoint.xsOnly">
      <v-btn icon large class="mr-1">
        <span class="material-symbols-outlined primary--text" @click="updateShowChatMobile(false)">
          navigate_before
        </span>
      </v-btn>
    </template>
    <!-- chat -->
    <v-row no-gutters align="center">
      <div>
        <span class="text-subtitle-2 custom-text-noto default-content--text">
          {{
            currentChat?.title === 'global_chat' || currentChat?.title === 'guild_chat'
              ? $t(currentChat.title)
              : convertMessage(currentChat.title)
          }}
        </span>
      </div>
    </v-row>
  </v-row>
</template>
<script>
  import images from '~/mixins/images'
  import converter from '~/mixins/converter'
  export default {
    name: 'msgTitle',
    mixins: [images, converter],
    props: {
      currentChat: {
        type: Object,
        default: () => {}
      },
      showChatMobile: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        defaultImg: process.env.IMAGE_URL + '/photo_stickers/default.png'
      }
    },
    methods: {
      updateShowChatMobile(val) {
        this.$emit('update:showChatMobile', val)
      },
      errorCustomImgHandler(title) {
        this.$store.commit('chat/SET_CHAT_IMG', {
          title: title,
          img: this.defaultImg
        })
      }
    }
  }
</script>
<style scoped lang="scss">
  .left-side-width-mobile {
    width: calc(100% - 32px);
  }
  .heat-list {
    height: 448px;
    width: 369px;
  }

  .channel-list-mobile {
    width: 311px;
  }
</style>

<template>
  <div class="msg-custom-sticker scrollbar-thin">
    <!-- 選擇相簿 or 相機(預設隱藏) -->
    <input
      type="file"
      ref="fileInput"
      class="d-none"
      accept="image/*"
      @change="checkCustomStickerFormat"
    />
    <v-btn
      icon
      color="default-content"
      :width="customStickerBtnWidth"
      :disabled="customStickerDisabled"
      @mousedown="selectCustomSticker"
    >
      <v-icon>mdi-image-outline</v-icon>
    </v-btn>
    <selectCustomStickerMethodDialog
      v-if="showSelectCustomStickerMethodDialogStatus"
      :title="$t('upload_image').toUpperCase()"
      :show-select-custom-sticker-method-dialog-status.sync="
        showSelectCustomStickerMethodDialogStatus
      "
      @showEditCustomStickerDialog="showEditCustomStickerDialog"
    />
    <editCustomStickerDialog
      v-if="showEditCustomStickerDialogStatus"
      :show-edit-custom-sticker-dialog-status.sync="showEditCustomStickerDialogStatus"
      @prepareCustomSticker="prepareCustomSticker"
    />
  </div>
</template>

<script>
  import Utility from '~/plugins/xin-socket/utility'
  import cloneDeep from 'lodash/cloneDeep'

  export default {
    name: 'CustomerServiceCustomSticker',
    components: {
      selectCustomStickerMethodDialog: () =>
        import('~/components/chatroom/selectCustomStickerMethodDialog'),
      editCustomStickerDialog: () => import('~/components/chatroom/editCustomStickerDialog')
    },
    props: {
      customStickerDisabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        customStickerBtnWidth: 36,
        isTouchDevice: true,
        showSelectCustomStickerMethodDialogStatus: false,
        showEditCustomStickerDialogStatus: false,
        generation: '1.02',
        childListGeneration: '1.0',
        metadataGeneration: '1.0',
        thumbPath: 'CustomerService/Thumbnail/Temp',
        imgPath: 'CustomerService/Temp'
      }
    },
    computed: {
      userName({ $store }) {
        return $store.getters['role/userName']
      },
      semanticToken({ $store }) {
        return $store.getters['chat/semanticToken']
      },
      downloadBaseURL() {
        return this.$xinStorage.downloadBaseURL
      },
      userPath() {
        return Utility.getUserPath(this.userName)
      },
      dataFileName() {
        return this.$xinStorage.DATA_FILENAME
      }
    },
    mounted() {
      this.$emit('customStickerBtnWidth', this.customStickerBtnWidth)
      this.checkDevice()
      this.initCustomSticker()
    },
    methods: {
      async initCustomSticker() {
        // 刪除過期貼圖
        try {
          await this.deleteExpiredCustomSticker()
        } catch (error) {
          console.warn('Failed to delete expired custom sticker：', error)
        }
      },
      // 取得貼圖相關資料
      async getCustomSticker() {
        const { data, dataThumb } = await this.fetchCustomStickerData()
        const { names, uniqNames, uniqNamesThumb } = this.getNames({ data, dataThumb })
        const { list, listThumb } = await this.getCustomStickerList({
          names,
          uniqNames,
          uniqNamesThumb
        })
        return {
          customStickerData: data,
          customStickerDataThumb: dataThumb,
          customStickerList: list,
          customStickerListThumb: listThumb
        }
      },
      /**
       * 刪除過期自訂貼圖的方法
       *
       * 刪除機制：
       * 1. 過期條件: 貼圖創建時間(CreationTimeMillis)超過兩天
       * 2. 刪除對象: 同時處理小圖(128x128)及大圖(600x600)
       * 3. 刪除流程:
       *    - 貼圖(data.png)
       *    - 貼圖的詳細資料(metadata.txt)
       *    - 貼圖資料(childlist.txt)中移除過期貼圖並重新上傳
       */
      async deleteExpiredCustomSticker() {
        // 獲取玩家的自訂貼圖資料
        const {
          customStickerData,
          customStickerDataThumb,
          customStickerList,
          customStickerListThumb
        } = await this.getCustomSticker()
        // 取得使用者資訊(用來上傳/刪除檔案)
        const { clientId, token } = this.semanticToken

        /**
         * 篩選出需要刪除的貼圖名稱
         * @param {Array} list - 貼圖列表
         * @returns {Array} - 需要刪除的貼圖名稱列表
         */
        const getDeleteNames = (list) => {
          // 計算兩天前的時間點，作為刪除的時間點
          const twoDaysAgo = new Date()
          twoDaysAgo.setDate(twoDaysAgo.getDate() - 2)

          // 如果列表為空，回傳空陣列
          if (list.length === 0) return []

          // 篩選出創建時間為空或超過兩天的貼圖，回傳名稱清單
          return list
            .filter(
              (item) => !item.CreationTimeMillis || new Date(item.CreationTimeMillis) <= twoDaysAgo
            )
            .map((item) => item.Name)
        }

        /**
         * 根據貼圖名稱生成完整的文件路徑
         * @param {Array} names - 貼圖名稱列表
         * @returns {Array} - 小圖和大圖的完整路徑列表
         */
        const getDeletePaths = (names) => {
          // 為每個貼圖名稱生成小圖和大圖路徑，並合併為一個陣列
          return names.flatMap((name) => [`${this.thumbPath}/${name}`, `${this.imgPath}/${name}`])
        }

        // 合併大小圖中需要刪除的貼圖名稱，並移除重複名
        const deleteNames = [
          ...new Set([
            ...getDeleteNames(customStickerList),
            ...getDeleteNames(customStickerListThumb)
          ])
        ]
        // 若刪除清單無內容，則無需觸發刪除機制
        if (!deleteNames.length) return

        // 根據名稱取得所有需要刪除的文件路徑
        const deletePaths = getDeletePaths(deleteNames)
        const tasks = []
        // 添加任務：刪除所有過期貼圖 & 對應詳細資料(metadata)
        tasks.push(
          ...deletePaths.flatMap((path) => [
            () => this.$xinStorage.deleteCustomSticker({ path, clientId, token }), // 刪除貼圖(data.png)
            () => this.$xinStorage.deleteMetadata({ path, clientId, token }) // 刪除詳細資料(metadata)
          ])
        )

        /**
         * 用來新增『更新貼圖清單』的任務
         * @param {Object} data - 貼圖資料
         * @param {Array} names - 需刪除的貼圖名稱
         * @param {String} path - 文件路徑
         */
        const addUploadTask = (data, names, path) => {
          // 確保資料存在且有需要刪除的貼圖時才添加任務
          if (data && Object.keys(data).length && names.length) {
            tasks.push(() =>
              this.$xinStorage.uploadFileList({
                path,
                clientId,
                token,
                file: this.getFileList({
                  type: 'delete',
                  file: data,
                  fileNames: names
                })
              })
            )
          }
        }

        // 添加任務：更新所有貼圖清單
        ;[
          [customStickerData, deleteNames, this.imgPath], // 大圖資料
          [customStickerDataThumb, deleteNames, this.thumbPath] // 小圖數據
        ].forEach(([data, names, path]) => addUploadTask(data, names, path))
        // 執行所有任務並等待結果
        const resDelete = await Promise.allSettled(tasks.map((task) => task()))
        const allSuccess = resDelete.every(
          (res) => res.status === 'fulfilled' && res.value.status === '1'
        )
        if (!allSuccess) throw new Error('Failed to delete custom sticker')
      },
      // 用來判斷是否為觸控裝置
      checkDevice() {
        this.isTouchDevice = 'ontouchend' in document
      },
      sendCustomSticker(customSticker) {
        const obj = { Normal: customSticker.fullPath, Thumbnail: customSticker.fullPathThumb }
        const jsonObj = JSON.stringify(obj)
        const msg = `<ImageUrl>${jsonObj}</ImageUrl>`
        this.$emit('sendCustomerServiceCustomStickerEvent', msg)
      },
      selectCustomSticker() {
        // 移除 v-btn 殘留的 focus 狀態
        this.$nextTick(() => {
          requestAnimationFrame(() => {
            requestAnimationFrame(() => {
              document.activeElement && document.activeElement.blur()
            })
          })
        })
        this.isTouchDevice
          ? this.showSelectCustomStickerMethodDialog(true)
          : this.openCameraOrGallery()
      },
      showSelectCustomStickerMethodDialog(val) {
        this.showSelectCustomStickerMethodDialogStatus = val
      },
      showEditCustomStickerDialog(val) {
        this.showEditCustomStickerDialogStatus = val
      },
      openCameraOrGallery() {
        this.$nextTick(() => {
          this.$refs.fileInput.click()
        })
      },
      checkCustomStickerFormat(e) {
        const img = e.target.files[0]
        // 上傳文件類型是否為圖片
        if (img.type.startsWith('image/') && !img.type.endsWith('gif')) {
          this.$store.commit('customSticker/SET_ORIGIN_PHOTO', img)
          this.showEditCustomStickerDialog(true)
        } else {
          this.$notify.error(this.$t('file_error'))
        }
        // 重置 input value，確保下一次選擇同樣的檔案時能觸發 change
        e.target.value = ''
      },
      async prepareCustomSticker(urlS, urlL) {
        this.$nuxt.$loading.start()
        const resUpload = await this.uploadCustomSticker(urlS, urlL)
        if (resUpload.status === 'success') {
          this.sendCustomSticker(resUpload.param)
        } else {
          this.$notify.error(this.$t('file_error'))
        }
        this.showEditCustomStickerDialog(false)
        this.$nuxt.$loading.finish()
      },
      // 取得貼圖資料
      async fetchCustomStickerData() {
        try {
          const [data, dataThumb] = await Promise.all([
            this.$xinStorage.getFileList({
              username: this.userName,
              path: this.imgPath
            }),
            this.$xinStorage.getFileList({
              username: this.userName,
              path: this.thumbPath
            })
          ])
          return { data, dataThumb }
        } catch (error) {
          // 如果是 404 錯誤(表示檔案不存在)
          if (error.response.status === 404) {
            return {
              data: {},
              dataThumb: {}
            }
          }
          // 其他錯誤則拋出(非200、404的結果)
          throw error
        }
      },
      // 取得貼圖詳細資料
      async fetchCustomStickerMetadata(name) {
        try {
          // 對每個 name 同時取得兩個路徑的檔案
          const [metadata, metadataThumb] = await Promise.all([
            this.$xinStorage.getMetadata({
              username: this.userName,
              path: `${this.imgPath}/${name}`
            }),
            this.$xinStorage.getMetadata({
              username: this.userName,
              path: `${this.thumbPath}/${name}`
            })
          ])

          return {
            metadata,
            metadataThumb
          }
        } catch (error) {
          // 如果是 404 錯誤(表示檔案不存在)，回傳只有 Name 的物件
          if (error.response.status === 404) {
            return {
              metadata: { Name: name },
              metadataThumb: { Name: name }
            }
          }
          // 其他錯誤則拋出(非200、404的結果)
          throw error
        }
      },
      // 取得貼圖詳細資料，回傳相關清單
      async getCustomStickerList({ names, uniqNames, uniqNamesThumb }) {
        // 取得貼圖清單中，每一個貼圖的詳細資訊(metadata)
        const lists = await Promise.all(
          names.map(async (name) => this.fetchCustomStickerMetadata(name))
        )

        // 分離結果到兩個 list
        const list = lists.map((list) => list.metadata)
        const listThumb = lists.map((list) => list.metadataThumb)

        list.push(...uniqNames)
        listThumb.push(...uniqNamesThumb)
        return {
          list,
          listThumb
        }
      },
      getNames({ data, dataThumb }) {
        const nameArr =
          data.Childs?.filter((child) => child.type === 'Item').map((item) => item.Name) || []
        const nameArrThumb =
          dataThumb.Childs?.filter((child) => child.type === 'Item').map((item) => item.Name) || []

        // 如果兩個清單都是空的，直接返回空陣列
        if (!nameArr.length && !nameArrThumb.length) {
          return {
            names: [],
            uniqNames: [],
            uniqNamesThumb: []
          }
        }

        const {
          repeatArr: names,
          uniqArr: uniqNames,
          uniqArrThumb: uniqNamesThumb
        } = this.compareArr(nameArr, nameArrThumb)

        return {
          names,
          uniqNames,
          uniqNamesThumb
        }
      },
      compareArr(arr, arrThumb) {
        // 創建Set提高查找效率
        const arrSet = new Set(arr)
        const arrThumbSet = new Set(arrThumb)

        const repeatArr = arr.filter((name) => arrThumbSet.has(name))
        const uniqArr = arr.filter((name) => !arrThumbSet.has(name)).map((name) => ({ Name: name }))
        const uniqArrThumb = arrThumb
          .filter((name) => !arrSet.has(name))
          .map((name) => ({ Name: name }))

        return {
          repeatArr,
          uniqArr,
          uniqArrThumb
        }
      },
      async getArrayBuffer(url) {
        const response = await fetch(url)
        return await response.arrayBuffer()
      },
      // 取得自訂貼圖完整路徑
      getFullPath(filePath) {
        const hashedPath = Utility.sha1(filePath).toUpperCase()
        const fullPath = `${this.downloadBaseURL}userfile/${this.userPath}/${hashedPath}/${
          this.dataFileName
        }?t=${Date.now()}`
        return fullPath
      },
      /**
       * 上傳自訂貼圖的方法
       * 此方法處理大小圖的上傳、貼圖資料更新、貼圖詳細資料的上傳
       *
       * @param {String} urlS - 小圖(128x128)的 Data URL (base64格式)
       * @param {String} urlL - 大圖(600x600)的 Data URL (base64格式)
       * @returns {Object} - 上傳結果，成功時回傳狀態、大小圖的完整路徑，失敗時回傳狀態
       */
      async uploadCustomSticker(urlS, urlL) {
        // 亂數產生新的圖片名稱，確保檔案名唯一
        const fileName = Utility.createCustomStickerFileName('png')
        // 大圖路徑
        const filePath = `${this.imgPath}/${fileName}`
        // 小圖路徑
        const filePathThumb = `${this.thumbPath}/${fileName}`
        // 取得使用者資訊(用來上傳檔案)
        const { clientId, token } = this.semanticToken
        // 將 Data URL 格式的圖片轉換為 ArrayBuffer 格式，便於上傳
        const [arrayBufferS, arrayBufferL] = await Promise.all([
          this.getArrayBuffer(urlS), // 轉換小圖(128x128)
          this.getArrayBuffer(urlL) // 轉換大圖(600x600)
        ])
        // 創建基礎上傳參數，簡化重複程式碼
        const baseUploadParams = { clientId, token }
        try {
          // 每次上傳前需要取得最新的貼圖清單，確保資料同步
          const { data, dataThumb } = await this.fetchCustomStickerData()
          const tasks = [
            // 上傳大圖
            () =>
              this.$xinStorage.uploadCustomSticker({
                ...baseUploadParams,
                path: filePath,
                image: arrayBufferL
              }),
            // 上傳小圖
            () =>
              this.$xinStorage.uploadCustomSticker({
                ...baseUploadParams,
                path: filePathThumb,
                image: arrayBufferS
              }),
            // 添加新貼圖名稱，更新 or 創建大圖的貼圖資料
            () =>
              this.$xinStorage.uploadFileList({
                ...baseUploadParams,
                path: this.imgPath,
                file: this.getFileList({
                  type: 'add',
                  path: this.imgPath,
                  createdAt: new Date(),
                  file: data,
                  fileNames: [fileName]
                })
              }),
            // 添加新貼圖名稱，更新 or 創建小圖的貼圖資料
            () =>
              this.$xinStorage.uploadFileList({
                ...baseUploadParams,
                path: this.thumbPath,
                file: this.getFileList({
                  type: 'add',
                  path: this.thumbPath,
                  createdAt: new Date(),
                  file: dataThumb,
                  fileNames: [fileName]
                })
              }),
            // 上傳大圖的貼圖詳細資料，包含創建時間等信息
            () =>
              this.$xinStorage.uploadMetadata({
                ...baseUploadParams,
                path: filePath,
                file: this.getMetadata({
                  path: filePath,
                  createdAt: new Date(),
                  fileName,
                  image: arrayBufferL
                })
              }),
            // 上傳小圖的貼圖詳細資料，包含創建時間等信息
            () =>
              this.$xinStorage.uploadMetadata({
                ...baseUploadParams,
                path: filePathThumb,
                file: this.getMetadata({
                  path: filePathThumb,
                  createdAt: new Date(),
                  fileName,
                  image: arrayBufferS
                })
              })
          ]

          const resUpload = await Promise.all(tasks.map((task) => task()))
          const allSuccess = resUpload.every((res) => res.status === '1')
          if (allSuccess) {
            const param = {
              fullPathThumb: this.getFullPath(filePathThumb),
              fullPath: this.getFullPath(filePath)
            }
            return { status: 'success', param }
          } else {
            return { status: 'fail' }
          }
        } catch (error) {
          console.warn('Failed to upload custom sticker：', error)
          return { status: 'fail' }
        }
      },
      getFileList({ type, path, createdAt, file, fileNames }) {
        const update = ({ type, file, fileNames }) => {
          const newFile = cloneDeep(file)

          if (type === 'add') {
            newFile.Childs.push(...fileNames.map((fileName) => ({ Name: fileName, type: 'Item' })))
          } else if (type === 'delete') {
            const nameSet = new Set(fileNames) // 用 Set 提高查找效率
            newFile.Childs = newFile.Childs.filter((child) => !nameSet.has(child.Name))
          }

          newFile.UpdatedTimeMillis = Utility.formatDate(new Date()) // 新的更新時間
          return newFile
        }
        const create = ({ path, createdAt, fileNames }) => {
          const newFile = {
            Childs: fileNames.map((fileName) => ({ Name: fileName, type: 'Item' })),
            Path: path,
            Generation: this.generation,
            ChildListGeneration: this.childListGeneration,
            UpdatedTimeMillis: Utility.formatDate(new Date()),
            CreationTimeMillis: Utility.formatDate(createdAt)
          }
          return newFile
        }

        return file && Object.keys(file).length
          ? update({ type, file, fileNames })
          : create({ path, fileNames, createdAt })
      },
      getMetadata({ path, createdAt, fileName, image }) {
        const newFile = {
          customMetadata: { display_name: 'Temp', display_order: '-1' },
          Bucket: this.downloadBaseURL + 'userfile',
          CreationTimeMillis: Utility.formatDate(createdAt),
          Generation: this.generation,
          Md5Hash: Utility.md5(Buffer.from(new Uint8Array(image))),
          MetadataGeneration: this.metadataGeneration,
          Name: fileName,
          Path: path,
          SizeBytes: image.byteLength,
          UpdatedTimeMillis: Utility.formatDate(new Date())
        }
        return newFile
      }
    }
  }
</script>

<style lang="scss" scoped></style>

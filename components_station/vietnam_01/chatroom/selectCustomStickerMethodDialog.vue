<template>
  <v-dialog
    :value="localShowSelectCustomStickerMethodDialogStatus"
    width="358"
    persistent
    content-class="rounded-lg"
  >
    <v-card color="transparent">
      <customDialogTitle :title="title.toUpperCase()" @closeDialog="closeDialog" />
      <v-card-text class="pt-4">
        <!-- 選擇相簿 or 相機(預設隱藏) -->
        <input
          type="file"
          ref="fileInput"
          class="d-none"
          accept="image/*"
          :capture="captureType"
          @change="checkCustomStickerFormat"
        />
        <!-- 選擇圖片 -->
        <v-row no-gutters justify="center" align="center">
          <v-btn class="ma-2 w-100" outlined color="primary" @click="openCameraOrGallery(null)">
            <span class="material-symbols-rounded"> add </span>
            <span> {{ $t('choosse_photo') }} </span>
          </v-btn>
        </v-row>
        <!-- 開啟相機 -->
        <v-row no-gutters justify="center" align="center">
          <v-btn
            class="ma-2 w-100"
            outlined
            color="primary"
            @click="openCameraOrGallery('environment')"
          >
            <span class="material-symbols-outlined mr-2"> photo_camera </span>
            <span> {{ $t('open_camera') }} </span>
          </v-btn>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
  export default {
    name: 'SelectCustomStickerMethodDialog',
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },

    props: {
      showSelectCustomStickerMethodDialogStatus: {
        type: Boolean,
        default: false
      },
      title: { type: String, default: '' }
    },
    data() {
      return { captureType: null }
    },
    computed: {
      localShowSelectCustomStickerMethodDialogStatus() {
        return this.showSelectCustomStickerMethodDialogStatus
      }
    },
    mounted() {
      // 移除 v-dialog 開啟，預設 focus 內部元素的特性
      this.$nextTick(() => {
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            document.activeElement && document.activeElement.blur()
          })
        })
      })
    },
    methods: {
      closeDialog() {
        this.$emit('update:showSelectCustomStickerMethodDialogStatus', false)
      },
      openCameraOrGallery(type) {
        this.captureType = type
        this.$nextTick(() => {
          this.$refs.fileInput.click()
        })
      },
      editCustomSticker() {
        this.closeDialog()
        this.$emit('showEditCustomStickerDialog', true)
      },
      checkCustomStickerFormat(e) {
        const img = e.target.files[0]
        // 上傳文件類型是否為圖片
        if (img.type.startsWith('image/') && !img.type.endsWith('gif')) {
          this.$store.commit('customSticker/SET_ORIGIN_PHOTO', img)
          this.editCustomSticker()
        } else {
          this.$notify.error(this.$t('file_error'))
        }
        // 重置 input value，確保下一次選擇同樣的檔案時能觸發 change
        e.target.value = ''
      }
    }
  }
</script>
<style scoped></style>

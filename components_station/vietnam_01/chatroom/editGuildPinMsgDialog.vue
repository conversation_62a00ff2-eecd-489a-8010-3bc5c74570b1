<template>
  <div>
    <v-dialog v-model="localShowEditGuildPinMsg" width="456px" persistent scrollable>
      <v-card class="pa-0" elevation="0" color="dialog-fill">
        <customDialogTitle
          :title="$t('edit_guild_caption').toUpperCase()"
          @closeDialog="closeDialog"
        />

        <v-card-text
          id="guild-edit-pin-msg-card"
          :class="['pa-4 pa-sm-6', breakpoint.xsOnly ? 'scrollable-xs' : 'scrollable-sm']"
        >
          <div class="d-flex h-100-percent flex-nowrap">
            <div class="flex-grow-1">
              <v-textarea
                v-model="pinMsg"
                class="error-wrap"
                v-validate="{
                  text_fullwidth: $UIConfig.guildEditDialog.textFullWidthValidEnable,
                  validate_string_length: 150
                }"
                :disabled="!$UIConfig.guildEditDialog.notyEnable"
                data-vv-scope="guildEdit"
                name="news"
                :error-messages="errors.first('guildEdit.news')"
                no-resize
                counter="150"
                filled
                shaped
                :label="$t('guild_caption_content') + '*'"
                @keydown="handlePinMsgKeyDown"
              />
              <v-btn
                block
                :disabled="disabledSubmit || pinMsg.length > 150"
                class="button-content--text mt-4 mb-sm-0"
                :color="$UIConfig.defaultBtnColor"
                elevation="0"
                @click="setupGuildValue(pinMsg)"
              >
                {{ $t('confirm_modify') }}
              </v-btn>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <guildConfirmDialog
      v-if="guildConfirmStatus"
      :confirm-obj="confirmObj"
      :show-guild-confirm-dialog-status.sync="guildConfirmStatus"
    />
  </div>
</template>

<script>
  import hiddenScrollHtml from '@/mixins/hiddenScrollHtml.js'
  import relationship from '@/mixins/relationship.js'
  import scssLoader from '@/mixins/scssLoader.js'
  import converter from '~/mixins/converter'
  import guildMgr from '~/mixins/guildMgr'
  export default {
    name: 'EditGuildPinMsgDialog',
    mixins: [relationship, scssLoader, guildMgr, hiddenScrollHtml, converter],
    components: {
      guildConfirmDialog: () => import('~/components/guild/guildConfirmDialog'),
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    props: {
      showEditGuildPinMsgDialogStatus: { type: Boolean, required: true, default: false }
    },
    data() {
      return {
        localShowEditGuildPinMsg: this.showEditGuildPinMsgDialogStatus,
        guildConfirmStatus: false,
        disabledSubmit: false,
        confirmObj: {},
        pinMsg: '',
        onCloseDialogEvent: undefined
      }
    },
    created() {
      this.pinMsg = this.replaceKeywords(this.guildNews)
    },
    async mounted() {
      this.updateListInfo()
    },
    async destroyed() {
      this.disableScroll()
      await this.$store.dispatch('maintain/fetch')
      if (this.maintainSystem[0].maintaining) return
    },
    computed: {
      maintainSystem({ $store }) {
        return $store.getters['maintain/system']
      },
      breakpoint() {
        return this.$vuetify.breakpoint
      }
    },
    watch: {
      showEditGuildPinMsgDialogStatus: {
        async handler(status) {
          if (this.maintainSystem[0].maintaining) return
          this.localShowEditGuildPinMsg = status
        }
      },
      pinMsg: {
        async handler() {
          const hasSplitMark = this.pinMsg.includes(this.$UIConfig.guildPinMsg.splitMark)
          const isCustomValid = await this.$validator.validate('guildEdit.news')
          const isTrimmedValid = this.pinMsg.trim().length > 0
          this.disabledSubmit = hasSplitMark || !isCustomValid || !isTrimmedValid
        },
        deep: true
      }
    },
    methods: {
      closeDialog(isSetupSuccess = false) {
        const callCloseDialog = () => {
          this.localShowEditGuildPinMsg = false
          this.$emit('update:showEditGuildPinMsgDialogStatus', false)
        }
        const isChange = this.pinMsg !== this.guildNews
        if (isChange && !isSetupSuccess) {
          this.confirmObj = {
            confirm: 'leave',
            confirmInfo: this.$t('guild_leave_edit_noty'),
            cancel: 'cancel',
            onConfirmNotify: callCloseDialog
          }
          this.guildConfirmStatus = true
        } else {
          callCloseDialog()
        }
      },
      async updateListInfo() {
        await this.$store.dispatch('guild/fetchSelfGuildDetail')
        await this.$store.dispatch('guild/fetchGuildRanking')
      },
      async setupGuildValue(news) {
        await this.$store.dispatch('maintain/fetch')
        if (this.maintainSystem[0].maintaining) return
        const confirmFun = async () => {
          let isChangeGuildNewsSuccess = true
          news = this.replaceKeywords(news.trim())
          if (news !== this.guildNews) {
            const { success } = await this.changeGuildNews(news)
            isChangeGuildNewsSuccess = success
          }
          if (isChangeGuildNewsSuccess) {
            this.closeDialog(true)
          }
        }
        this.confirmObj = {
          confirm: 'apply',
          confirmInfo: `${this.$t('guild_apply_edit_noty')}`,
          cancel: 'cancel',
          onConfirmNotify: confirmFun
        }
        this.guildConfirmStatus = true
      },
      handlePinMsgKeyDown(event) {
        const currentLength = this.pinMsg ? this.pinMsg.length : 0
        // 允許刪除鍵和方向鍵
        if (
          event.key === 'Backspace' ||
          event.key === 'Delete' ||
          event.key === 'ArrowLeft' ||
          event.key === 'ArrowRight' ||
          event.key === 'ArrowUp' ||
          event.key === 'ArrowDown'
        ) {
          return
        }
        // 如果已達到最大長度，阻止輸入
        if (currentLength >= 150) {
          event.preventDefault()
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  $primary-variant-3: map-get($colors, 'primary-variant-3');
  $dialog-fill: map-get($colors, 'dialog-fill');
  $primary: map-get($colors, 'primary');
  .v-data-table ::v-deep {
    tbody {
      tr:hover {
        background-color: map-get($colors, 'grey-4');
      }
    }
  }
  .error-wrap ::v-deep .v-messages {
    white-space: normal !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    line-height: 1.2;
  }
  .guild-info-gackground {
    background: rgba(0, 0, 0, 0.12);
  }
  .guild-info-gackground-xs {
    background: $dialog-fill;
  }
  .guild-edit-dialog-height-vertical {
    height: calc(var(--vh, 1vh) * 100 - 333px) !important;
  }
  .v-tabs-items {
    background-color: transparent !important;
  }
  .v-tab--active {
    color: $primary !important;
  }
  .h-100-percent {
    height: 100% !important;
  }
  #guild-edit-pin-msg-card {
    &.scrollable-sm {
      max-height: calc(90vh - 52px);
      overflow-y: auto;
    }
    &.scrollable-xs {
      max-height: calc(100vh - 52px);
      overflow-y: auto;
    }
    @supports (height: 90svh) {
      &.scrollable-sm {
        max-height: calc(90svh - 52px);
      }
      &.scrollable-xs {
        max-height: calc(100svh - 52px);
      }
    }
  }
</style>

<!-- eslint-disable vue/no-v-html -->
<template>
  <v-row no-gutters justify="center" ref="companyDialog" class="company-dialog">
    <v-dialog
      v-model="showCompanyDialogStatus"
      :fullscreen="$vuetify.breakpoint.smAndDown"
      class="dialog-fill"
      scrollable
      max-width="800"
      :attach="$refs.companyDialog"
    >
      <v-card color="transparent">
        <customDialogTitle
          :title="$t('privacy_policy')"
          @closeDialog="closeDialog"
          :class="['company-dialog-title', { 'notch-left': hasLeftNotch }]"
        />
        <v-card-text
          style="height: 800px"
          :class="[
            'px-10 pb-6 pt-4 company-dialog-card-text',
            { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }
          ]"
        >
          <v-row no-gutters>
            <v-col>
              <nuxt-content :document="article" />
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-row>
</template>
<script>
  import orientation from '@/mixins/orientation.js'
  const STATION = process.env.STATION
  export default {
    name: 'companyDialog',
    mixins: [orientation],
    props: {
      showCompanyDialogStatus: { type: Boolean, default: false }
    },
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    data() {
      return {
        article: {}
      }
    },
    watch: {
      showCompanyDialogStatus(val) {
        if (val === false) {
          this.closeDialog()
        }
      }
    },
    computed: {
      companyPolicyType({ $store }) {
        return $store.getters[`${STATION}/companyInfo/companyPolicyType`]
      }
    },
    async mounted() {
      try {
        this.article = await this.$content(
          `articles/india_01/${this.$i18n.locale}/privacy_policy`
        ).fetch()
      } catch (loadErr) {
        console.log(loadErr)
      }
    },
    methods: {
      closeDialog() {
        this.$nuxt.$emit('root:showCompanyDialogStatus', false)
      }
    }
  }
</script>
<style>
  .custom-default-content-1 {
    color: rgba(255, 255, 255, 0.7) !important;
  }
  .default-content-line {
    text-decoration: underline !important;
    opacity: 1 !important;
  }
  .company-dialog {
    position: relative;
    z-index: 1001;
  }
</style>

<style lang="scss" scoped>
  @media (orientation: landscape) {
    .company-dialog-title {
      &.notch-left {
        padding-left: calc(24px + env(safe-area-inset-left)) !important;
      }
      &.notch-right {
        padding-right: calc(24px + env(safe-area-inset-right)) !important;
      }
    }
    .company-dialog-card-text {
      &.notch-left {
        padding-left: calc(40px + env(safe-area-inset-left)) !important;
      }
      &.notch-right {
        padding-right: calc(40px + env(safe-area-inset-right)) !important;
      }
    }
  }
</style>

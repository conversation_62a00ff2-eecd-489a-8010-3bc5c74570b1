<!-- eslint-disable vue/no-v-html -->
<template>
  <v-row no-gutters justify="center" class="maintain-dialog">
    <v-dialog
      v-model="localShow"
      :fullscreen="$vuetify.breakpoint.smAndDown"
      class="dialog-fill"
      scrollable
      max-width="620"
      persistent
    >
      <v-card color="transparent">
        <customDialogTitle
          :title="$t('maintain_bulletin')"
          @closeDialog="closeDialog"
          :class="['maintain-dialog-title', { 'notch-left': hasLeftNotch }]"
        />
        <v-card-text
          style="height: 260px"
          :class="[
            'px-10 pb-6 pt-4 maintain-dialog-card-text',
            { 'notch-left': hasLeftNotch, 'notch-right': hasRightNotch }
          ]"
        >
          <v-row no-gutters>
            <v-col>
              <nuxt-content :document="article" />
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-row>
</template>
<script>
  import orientation from '@/mixins/orientation.js'
  export default {
    name: 'MaintainDialog',
    mixins: [orientation],
    data() {
      return {
        article: null,
        localShow: false
      }
    },
    props: {
      showMaintainDialogStatus: { type: Boolean, default: false }
    },
    components: {
      customDialogTitle: () => import('~/components/customDialogTitle')
    },
    watch: {
      showMaintainDialogStatus: {
        handler(val) {
          // 同步外部狀態到本地狀態
          this.localShow = val
        },
        immediate: true
      },
      localShow(val) {
        // 當本地狀態改變時，通知外部
        if (!val) {
          this.closeDialog()
        }
      }
    },
    async created() {
      try {
        this.article = await this.$content(`articles/${this.$i18n.locale}/maintain`).fetch()
      } catch (loadErr) {
        console.log(loadErr)
      }
    },
    methods: {
      closeDialog() {
        this.$nuxt.$emit('root:showMaintainDialogStatus', false)
      }
    }
  }
</script>
<style>
  .custom-default-content-1 {
    color: rgba(255, 255, 255, 0.7) !important;
  }
  .default-content-line {
    text-decoration: underline !important;
    opacity: 1 !important;
  }
  .maintain-dialog {
    position: relative;
    z-index: 1001;
  }
</style>
<style lang="scss" scoped>
  @media (orientation: landscape) {
    .maintain-dialog-title {
      &.notch-left {
        padding-left: calc(24px + env(safe-area-inset-left)) !important;
      }
      &.notch-right {
        padding-right: calc(24px + env(safe-area-inset-right)) !important;
      }
    }
    .maintain-dialog-card-text {
      &.notch-left {
        padding-left: calc(40px + env(safe-area-inset-left)) !important;
      }
      &.notch-right {
        padding-right: calc(40px + env(safe-area-inset-right)) !important;
      }
    }
  }
</style>

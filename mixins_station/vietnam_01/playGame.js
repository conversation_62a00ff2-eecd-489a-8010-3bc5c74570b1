import playGameTaiwan from '~/mixins/game/playGameTaiwan'

export default {
  mixins: [playGameTaiwan],
  computed: {
    isVipLevelLimitSingle() {
      const gameVipLevel = this.singleGameHallInfo.vipLevel
      // 越南站特殊處理：vipLevel為-1的遊戲允許所有玩家遊玩
      if (gameVipLevel === -1) return false
      // 青銅等級1的遊戲：需要玩家等級達到10級
      if (gameVipLevel === 1 && this.level < 10) return true
      // 一般VIP等級限制：玩家VIP等級需達到遊戲要求（99為特殊無限制等級）
      if (gameVipLevel !== 99 && this.vipLevel < gameVipLevel) return true
      // 沒有限制
      return false
    }
  },
  methods: {
    checkYears18Noty(fn) {
      const wbesiteMaintainOrError = this.status404 || this.maintainSystem[0].maintaining

      // 初始化 18 禁提示的 localStorage
      if (!this.$localStorage.get('years18Noty')) {
        this.$localStorage.set('years18Noty', { expired: '1950-01-01' })
      }

      // 獲取已顯示過的提示清單
      const shownNoty = this.$localStorage.get('shownNoty') || []
      const shownYears18Noty = shownNoty.includes('years18Noty')

      // 判斷是否顯示 18 禁提示
      let showYears18Noty = false
      const years18Noty = this.$localStorage.get('years18Noty')
      const targetDate = this.$moment(years18Noty.expired, 'YYYY-MM-DD')

      // 檢查是否今天已顯示過提示，或是第一次訪問
      const isToday = this.$moment().isSame(targetDate, 'day')
      const isFirst = years18Noty.expired === '1950-01-01'
      showYears18Noty = isFirst || !isToday

      if (!wbesiteMaintainOrError && !shownYears18Noty && showYears18Noty) {
        // 彈出 18 禁提示後，執行接續行為、儲存到"已顯示彈窗"陣列中
        this.$nuxt.$emit('root:showYears18NotyStatus', {
          show: true,
          onConfirmNotify: fn
        })
        shownNoty.push('years18Noty')
        this.$localStorage.set('shownNoty', shownNoty)
      } else if (fn) {
        fn()
      }
    },
    // 越南站特殊處理：
    async payGameClickHandler() {
      if (this.maintainSystem[0].maintaining) return

      const localStorageLiveConfirm = this.$localStorage.get('localStorageLiveConfirm').expired
      if (!this.hasExpSingle) {
        this.clickedNoExpGameInfo.platformId = this.singleGameHallInfo.platformId
        this.clickedNoExpGameInfo.gameId = this.singleGameHallInfo.id
        this.clickedNoExpGameInfo.isDemo = false
      }
      if (this.hasRobotSingle) {
        this.clickedhasRobotGameInfo.platformId = this.singleGameHallInfo.platformId
        this.clickedhasRobotGameInfo.gameId = this.singleGameHallInfo.id
        this.clickedhasRobotGameInfo.isDemo = false
      }
      const memberLevel = this.vipLevel
      // 越南站特殊處理：允許 VIP 等級 0 的玩家玩 vipLevel = -1 的遊戲
      const canOpenGame =
        (memberLevel > 0 || this.singleGameHallInfo.vipLevel === -1) && !this.isVipLevelLimitSingle

      if (this.singleGameHallInfo.maintaining) return

      if (canOpenGame) {
        const isShowDialog =
          this.$UIConfig.gameIframe.showNoExpDialog &&
          (localStorageLiveConfirm === undefined ||
            this.$moment(localStorageLiveConfirm).isBefore(this.$moment(), 'day'))
        if (isShowDialog && this.singleGameHallInfo.categoryType === 200) {
          // 先彈出免責聲明，但不帶 URL
          this.$nuxt.$emit('root:showGameCardConfirmDialogStatus', {
            show: true,
            hasExp: this.hasExpSingle,
            hasRobot: this.hasRobotSingle,
            onConfirmNotify: async () => {
              // 點擊時才重新取得最新 URL
              const newHandler = await this.prepareHandler(
                this.singleGameHallInfo.platformId,
                this.singleGameHallInfo.categoryType,
                this.singleGameHallInfo.id,
                false
              )

              if (newHandler.isSuccess) {
                this.openGame(newHandler.openGame)
              } else if (this.showGameModeStatus) {
                // 若在遊戲頁，因任何狀況無法開啟遊戲，轉跳回首頁
                this.$router.push(this.localePath('/'))
              }
            },
            onCancelNotify: this.showGameModeStatus
              ? () => this.$router.push(this.localePath('/'))
              : () => {}
          })
        } else if (!this.hasExpSingle && this.hasRobotSingle) {
          // 如果兩個都有，顯示both提示
          this.showNotyBothRobotExpNotyDialogStatus = {
            show: true,
            onConfirmNotify: this.playNoExpGame,
            onCancelNotify: this.showGameModeStatus
              ? () => this.$router.push(this.localePath('/'))
              : () => {}
          }
        } else if (!this.hasExpSingle) {
          // 如果無經驗值，顯示noExp提示
          this.showNotyNoExpGainNotyDialogStatus = {
            show: true,
            onConfirmNotify: this.playNoExpGame,
            onCancelNotify: this.showGameModeStatus
              ? () => this.$router.push(this.localePath('/'))
              : () => {}
          }
        } else if (this.hasRobotSingle) {
          // 如果有機器人，顯示hasRobot提示
          this.showNotyHasRobotNotyDialogStatus = {
            show: true,
            onConfirmNotify: this.playNoExpGame,
            onCancelNotify: this.showGameModeStatus
              ? () => this.$router.push(this.localePath('/'))
              : () => {}
          }
        } else {
          this.openGameHandler = await this.prepareHandler(
            this.singleGameHallInfo.platformId,
            this.singleGameHallInfo.categoryType,
            this.singleGameHallInfo.id,
            false
          )

          if (!this.openGameHandler.isSuccess) {
            // 若在遊戲頁，因任何狀況無法開啟遊戲，轉跳回首頁
            if (this.showGameModeStatus) this.$router.push(this.localePath('/'))
            return
          }

          this.openGame(this.openGameHandler.openGame)
        }
      } else if (this.showGameModeStatus || this.isVipLevelLimitSingle) {
        if (process.client) this.$notify.error(this.$t('game_restriction_notice'))
        this.$router.push(this.localePath('/'))
      } else {
        this.showNotyNotRealMemberDialogStatus = true
      }
    }
  }
}

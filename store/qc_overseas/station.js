const NUXT_ENV = process.env.NUXT_ENV
const loadConfig = require(`~/station/${process.env.STATION}/${NUXT_ENV}.js`).default

export const state = () => ({
  loading: false,
  stationName: '',
  stationConstant: null
})

export const getters = {
  loading(state) {
    return state.loading
  },
  stationName(state) {
    return state.stationName
  },
  stationConstant(state) {
    return state.stationConstant
  }
}

export const mutations = {
  SET_LOADING(state, data) {
    state.loading = data
  },
  SET_STATION_NAME(state, data) {
    state.stationName = data
  },
  SET_STATION_CONSTANT(state, data) {
    state.stationConstant = data
  }
}

export const actions = {
  async setStation({ commit }, stationName) {
    commit('SET_STATION_NAME', stationName)
    const stationConstant = loadConfig.game.stationConstant.find((item) => {
      return item.stationName === stationName
    })
    commit('SET_STATION_CONSTANT', stationConstant)
  }
}

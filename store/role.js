import Vue from 'vue'

const STATION = process.env.STATION
export const state = () => ({
  vipLevelInfo: require('@/assets/json/vip_level_info.json'),
  roleCount: 0,
  list: [],
  haspwd: false,
  accessToken: '',
  expiresAt: '',
  isLogin: false,
  id: '',
  userName: '',
  fbName: '',
  phoneNumber: '',
  level: 0,
  honor: 0, // 榮譽值，目前用在寄信費用扣除
  activeValue: 0, // 玩家活耀值
  vipLevel: 0,
  vipLevelTitle: [
    'none_vip',
    'bronze',
    'silver',
    'gold',
    'whiteGold',
    'platinum',
    'diamond',
    'fancyDiamond',
    'moonDazzle',
    'sunDazzle',
    'starDazzle'
  ],
  vipLevelImgFileName: [
    '0-1_None',
    '1-1_Bronze',
    '2-1_Silver',
    '3-1_Gold',
    '4-1_WhiteGold',
    '5-1_Platinum',
    '6-1_Diamond',
    '7-1_FancyDiamond',
    '8-1_MoonDazzle',
    '9-1_SunDazzle',
    '10-1_StarDazzle'
  ],
  vipLevelImg3xFileName: [
    '0-1_None@3x',
    '1-1_Bronze@3x',
    '2-1_Silver@3x',
    '3-1_Gold@3x',
    '4-1_WhiteGold@3x',
    '5-1_Platinum@3x',
    '6-1_Diamond@3x',
    '7-1_FancyDiamond@3x',
    '8-1_MoonDazzle@3x',
    '9-1_SunDazzle@3x',
    '10-1_StarDazzle@3x'
  ],
  vipLevelIconFileName: [
    '0_None_icon.png',
    '1_Bronze_icon.png',
    '2_Silver_icon.png',
    '3_Gold_icon.png',
    '4_WhiteGold_icon.png',
    '5_Platinum_icon.png',
    '6_Diamond_icon.png',
    '7_FancyDiamond_icon.png',
    '8_MoonDazzle_icon.png',
    '9_SunDazzle_icon.png',
    '10_StarDazzle_icon.png'
  ],
  userDefalutAvatar: [
    'boy.png',
    'girl.png',
    'face_01.png',
    'face_02.png',
    'face_03.png',
    'face_04.png',
    'face_05.png',
    'face_06.png',
    'face_07.png',
    'face_08.png',
    'face_09.png',
    'face_10.png',
    'face_11.png',
    'face_12.png',
    'face_13.png',
    'face_14.png',
    'face_15.png',
    'face_16.png',
    'face_17.png',
    'face_18.png',
    'face_19.png',
    'face_20.png',
    'face_21.png',
    'face_22.png',
    'face_23.png',
    'face_24.png',
    'face_25.png',
    'face_26.png',
    'face_27.png',
    'face_28.png'
  ],
  avatarFrameIcon: '',
  starsClassIcon: [],
  balance: '', //bingInt
  points: 0,
  safe: 0,
  rewardMoneny: 0,
  thumbUrl: '',
  xinToken: '',
  rank: 0,
  connInfo: {},
  selfProviderWallets: [],
  platformId: 0,
  gameId: 0,
  accountType: 0,
  isBind: false,
  referralMonany: 0,
  isEnableSafetyCode: false,
  deviceList: [],
  loginRecord: [],
  playerProfile: [],
  facebookId: '',
  //倒數計時，用於選角色頁面以及創角頁面
  loginLastTime: null,
  //用於判斷是否繼續倒數計時
  loginTimmerState: false,
  // 回饋%數
  discount: 0,
  //購買上限
  limit: 0,
  //目前所選的照片，用於頭貼更換
  selectPhoto: null,
  online: false,
  birthdate: '',
  loginType: 0,
  //其他遊戲角色
  otherGamesRole: {
    //麻將之星 先註解掉 等確定不需要再刪除
    // mjs: { gameName: '', username: '', subTitle: '' }
  },
  // 登入前的動作
  preLoginAction: {},
  // 左側功能列的角色功能清單
  roleCategory: [],
  //銀幣
  silverCoin: 0,
  copperCoin: 0,
  pkCoin: { id: '', count: 0 }, // PK幣
  ruby: { id: '', count: 0 }, // 紅寶石
  battleCoin: { id: '', count: 0 }, // 小鬥幣
  premiumKey: { id: '', count: 0 }, // 高額鑰匙
  statusInventory: null, // 背包道具（狀態類、其他類）
  gameInventory: null // 背包道具（遊戲類）
})

export const getters = {
  vipLevelInfo(state) {
    return state.vipLevelInfo
  },
  transactionFee(state) {
    return state.vipLevel === 0 ? 0 : state.vipLevelInfo[state.vipLevel - 1].TransactionFee
  },
  roleCount(state) {
    return state.list.length
  },
  list(state) {
    return state.list
  },
  haspwd(state) {
    return state.haspwd
  },
  userName(state) {
    return state.userName
  },
  fbName(state) {
    return state.fbName
  },
  honor(state) {
    return state.honor
  },
  activeValue(state) {
    return state.activeValue
  },
  phoneNumber(state) {
    return state.phoneNumber
  },
  real(state) {
    return !!state.level
  },
  isLogin(state) {
    return state.isLogin
  },
  accessToken(state) {
    return state.accessToken
  },
  expiresAt(state) {
    return state.expiresAt
  },
  id(state) {
    return state.id
  },
  level(state) {
    return state.level
  },
  vipLevel(state) {
    return state.vipLevel
  },
  vipLevelTitle(state) {
    return state.vipLevelTitle
  },
  vipLevelImgFileName(state) {
    return state.vipLevelImgFileName
  },
  vipLevelImg3xFileName(state) {
    return state.vipLevelImg3xFileName
  },
  vipLevelIconFileName(state) {
    return state.vipLevelIconFileName
  },
  userDefalutAvatar(state) {
    return state.userDefalutAvatar
  },
  balance(state) {
    return state.balance
  },
  points(state) {
    return state.points
  },
  safe(state) {
    return state.safe
  },
  rewardMoneny(state) {
    return state.rewardMoneny
  },
  thumbUrl(state) {
    return state.thumbUrl
  },
  avatarFrameIcon(state) {
    return state.avatarFrameIcon
  },
  starsClassIcon(state) {
    return state.starsClassIcon
  },
  xinToken(state) {
    return state.xinToken
  },
  rank(state) {
    return state.rank
  },
  connInfo(state) {
    return state.connInfo
  },
  selfProviderWallets(state) {
    return state.selfProviderWallets
  },
  platformId(state) {
    return state.platformId
  },
  gameId(state) {
    return state.gameId
  },
  accountType(state) {
    return state.accountType
  },
  isBind(state) {
    return state.isBind
  },
  referralMonany(state) {
    return state.referralMonany
  },
  isEnableSafetyCode(state) {
    return state.isEnableSafetyCode
  },
  deviceList(state) {
    return state.deviceList
  },
  loginRecord(state) {
    return state.loginRecord
  },
  playerProfile(state) {
    return state.playerProfile
  },
  facebookId(state) {
    return state.facebookId
  },
  loginLastTime(state) {
    return state.loginLastTime
  },
  loginTimmerState(state) {
    return state.loginTimmerState
  },
  discount(state) {
    return state.discount
  },
  limit(state) {
    return state.limit
  },
  selectPhoto(state) {
    return state.selectPhoto
  },
  online(state) {
    return state.online
  },
  birthdate(state) {
    return state.birthdate
  },
  loginType(state) {
    return state.loginType
  },
  otherGamesRole(state) {
    return state.otherGamesRole
  },
  preLoginAction(state) {
    return state.preLoginAction
  },
  roleCategory(state) {
    return state.roleCategory
  },
  silverCoin(state) {
    return state.silverCoin
  },
  copperCoin(state) {
    return state.copperCoin
  },
  pkCoin(state) {
    return state.pkCoin
  },
  ruby(state) {
    return state.ruby
  },
  battleCoin(state) {
    return state.battleCoin
  },
  premiumKey(state) {
    return state.premiumKey
  },
  statusInventory(state) {
    return state.statusInventory
  },
  gameInventory(state) {
    return state.gameInventory
  }
}
export const mutations = {
  SET_ROLECOUNT(state, data) {
    state.roleCount = data
  },
  SET_LIST(state, data) {
    state.list = data
  },
  SET_ISLOGIN(state, data) {
    state.isLogin = data
  },
  SET_HASPWD(state, data) {
    state.haspwd = data
  },
  SET_USERNAME(state, data) {
    state.userName = data
  },
  SET_FBNAME(state, data) {
    state.fbName = data
  },
  SET_ACTIVEVALUE(state, data) {
    state.activeValue = data
  },
  SET_HONOR(state, data) {
    state.honor = data
  },
  SET_PHONENUMBER(state, data) {
    state.phoneNumber = data
  },
  SET_ACCESSTOKEN(state, data) {
    state.accessToken = data
  },
  SET_XINTOKEN(state, data) {
    state.xinToken = data
  },
  SET_EXPIRES_AT(state, data) {
    state.expiresAt = data
  },
  SET_ID(state, data) {
    state.id = data
  },
  SET_LEVEL(state, data) {
    state.level = data
  },
  SET_VIPLEVEL(state, data) {
    state.vipLevel = data
  },
  SET_VIP_LEVEL_TITLE(state, data) {
    state.vipLevelTitle = data
  },
  SET_VIP_LEVEL_IMG_FILE_NAME(state, data) {
    state.vipLevelImgFileName = data
  },
  SET_VIP_LEVEL_IMG_3X_FILE_NAME(state, data) {
    state.vipLevelImg3xFileName = data
  },
  SET_VIP_LEVEL_ICON_FILE_NAME(state, data) {
    state.vipLevelIconFileName = data
  },
  SET_BALANCE(state, data) {
    state.balance = data
  },
  SET_POINTS(state, data) {
    state.points = data
  },
  SET_SAFE(state, data) {
    state.safe = data
  },
  SET_REWARDMONEY(state, data) {
    state.rewardMoney = data
  },
  SET_THUMBURL(state, data) {
    state.thumbUrl = data
  },
  SET_AVATARFRAMEICON(state, data) {
    state.avatarFrameIcon = data
  },
  SET_STARSCLASSICON(state, data) {
    state.starsClassIcon = data
  },
  SET_RANK(state, data) {
    state.rank = data
  },
  SET_CONNINFO(state, data) {
    state.connInfo = data
  },
  SET_PROVIDER_WALLETS(state, { providerId, balance }) {
    const selfProviderWallets = state.selfProviderWallets
    if (selfProviderWallets.length === 0) {
      selfProviderWallets.push({ providerId, balance })
    } else {
      selfProviderWallets.map((wallet) => {
        if (wallet.providerId === providerId) {
          wallet.balance = balance
        } else {
          selfProviderWallets.push({ providerId, balance })
        }
      })
    }
  },
  SET_PLATFORMID(state, data) {
    state.platformId = data
  },
  SET_GAMEID(state, data) {
    state.gameId = data
  },
  SET_ACCOUNTTYPE(state, data) {
    state.accountType = data
  },
  SET_ISBIND(state, data) {
    state.isBind = data
  },
  SET_REFERRALMONANY(state, data) {
    state.referralMonany = data
  },
  RESET(state) {
    Vue.set(state, 'roleCount', 0)
    Vue.set(state, 'list', [])
    Vue.set(state, 'isLogin', false)
    Vue.set(state, 'haspwd', false)
    Vue.set(state, 'userName', '')
    Vue.set(state, 'fbName', '')
    Vue.set(state, 'honor', 0)
    Vue.set(state, 'phoneNumber', '')
    Vue.set(state, 'accessToken', '')
    Vue.set(state, 'expiresAt', 0)
    Vue.set(state, 'id', 0)
    Vue.set(state, 'real', false)
    Vue.set(state, 'level', 0)
    Vue.set(state, 'vipLevel', 0)
    Vue.set(state, 'balance', '')
    Vue.set(state, 'rewardMoney', 0)
    Vue.set(state, 'points', 0)
    Vue.set(state, 'safe', 0)
    Vue.set(state, 'rank', 0)
    Vue.set(state, 'thumbUrl', '')
    Vue.set(state, 'vipLevelImg', '')
    Vue.set(state, 'avatarFrame', '')
    Vue.set(state, 'starsClassIcon', '')
    Vue.set(state, 'connInfo', {})
    Vue.set(state, 'discount', 0)
    Vue.set(state, 'limit', 0)
    Vue.set(state, 'facebookId', '')
    Vue.set(state, 'selectPhoto', null)
    Vue.set(state, 'online', false)
    Vue.set(state, 'silverCoin', 0)
    Vue.set(state, 'copperCoin', 0)
    Vue.set(state, 'pkCoin', { id: '', count: 0 })
    Vue.set(state, 'ruby', { id: '', count: 0 })
    Vue.set(state, 'battleCoin', { id: '', count: 0 })
    Vue.set(state, 'premiumKey', { id: '', count: 0 })
    Vue.set(state, 'statusInventory', null)
    Vue.set(state, 'gameInventory', null)
  },
  INSERT_LIST(state, data) {
    state.list.unshift(data)
  },
  SET_IS_ENABLE_SAFETY_CODE(state, data) {
    state.isEnableSafetyCode = data
  },
  SET_DEVICE_LIST(state, data) {
    state.deviceList = data
  },
  SET_LOGIN_RECORD(state, data) {
    state.loginRecord = data
  },
  REMOVE_DEVICE_LIST(state, id) {
    state.deviceList = state.deviceList.filter((item) => item.id !== id)
  },
  SET_FACEBOOK_ID(state, data) {
    state.facebookId = data
  },
  SET_LOGIN_LAST_TIME(state, data) {
    state.loginLastTime = data
  },
  SET_LOGIN_TIMMER_STATE(state, data) {
    state.loginTimmerState = data
  },
  SET_DISCOUNT(state, data) {
    state.discount = data
  },
  SET_LIMIT(state, data) {
    state.limit = data
  },
  SET_SELECT_PHOTO(state, data) {
    state.selectPhoto = data
  },
  SET_PLAYER_PROFILE(state, data) {
    state.playerProfile = data
  },
  SET_ONLINE(state, data) {
    state.online = data
  },
  SET_LOGIN_TYPE(state, data) {
    state.loginType = data
  },
  SET_BIRTHDATE(state, data) {
    state.birthdate = data
  },
  SET_OTHER_GAMES_ROLE(state, data) {
    state.otherGamesRole[data.key] = data.value
  },
  SET_PRE_LOGIN_ACTION(state, data) {
    state.preLoginAction = data
  },
  SET_ROLE_CATEGORY(state, data) {
    state.roleCategory = data
  },
  SET_ROLE_CATEGORY_STATUS(state, { name, status }) {
    const match = state.roleCategory.find((item) => item.name === name)
    match && (match.active = status === undefined ? !match.active : status)
  },
  SET_ROLE_CATEGORY_ALL_INACTIVE(state) {
    const filter = state.roleCategory.filter((item) => item.active)
    filter.forEach((item) => (item.active = false))
  },
  SET_SILVER_COIN(state, data) {
    state.silverCoin = data
  },
  SET_COPPER_COIN(state, data) {
    state.copperCoin = data
  },
  SET_PK_COINS(state, data) {
    state.pkCoin = data
  },
  SET_RUBY(state, data) {
    state.ruby = data
  },
  SET_BATTLE_COIN(state, data) {
    state.battleCoin = data
  },
  SET_PREMIUM_KEY(state, data) {
    state.premiumKey = data
  },
  SET_STATUS_INVENTORY(state, data) {
    state.statusInventory = data
  },
  ADD_STATUS_INVENTORY(state, data) {
    if (!Array.isArray(state.statusInventory)) {
      state.statusInventory = []
    }
    const index = state.statusInventory.findIndex((item) => item.id === data.id)
    if (index !== -1) {
      Vue.set(state.statusInventory, index, {
        id: data.id,
        count: data.count
      })
    } else {
      state.statusInventory.push({
        id: data.id,
        count: data.count
      })
    }
  },
  SET_GAME_INVENTORY(state, data) {
    state.gameInventory = data
  },
  ADD_GAME_INVENTORY(state, data) {
    if (Array.isArray(state.gameInventory)) {
      state.gameInventory.push(data)
    } else {
      state.gameInventory = data
    }
  },
  SET_USER_DEFAULT_AVATAR(state, data) {
    state.userDefalutAvatar = data
  }
}
export const actions = {
  async fetch({ dispatch }, profileData) {
    await dispatch('profileInit', profileData)
  },
  async updateLevel({ dispatch, commit }, level) {
    commit('SET_LEVEL', level)
    await dispatch('setAvatarIcon')
  },
  async profileInit({ dispatch, commit }, profileData) {
    const userData = { userName: profileData.username }
    const thumbUrl = await dispatch('getThumbUrl', userData)
    const userValue = {
      phoneNumber: profileData.phoneNumber,
      honor: profileData.honor,
      activeValue: profileData.activeValue,
      username: profileData.username,
      level: profileData.level,
      levelVip: profileData.levelVip,
      money: profileData.money,
      rank: profileData.rank,
      thumbUrl: thumbUrl,
      referralMonany: profileData.referralMonany,
      facebookId: profileData.facebookId,
      silverCoin: profileData.silver
    }
    if (!userValue.phoneNumber) {
      const res = await dispatch('social/getUserDetail', userValue.username, { root: true })
      userValue.activeValue = res.activeValue
      userValue.phoneNumber = res.phoneNumber
      userValue.honor = res.honor
    }
    commit('SET_PHONENUMBER', userValue.phoneNumber)
    commit('SET_HONOR', userValue.honor)
    commit('SET_ACTIVEVALUE', userValue.activeValue)
    commit('SET_USERNAME', userValue.username)
    commit('SET_LEVEL', userValue.level)
    commit('SET_VIPLEVEL', userValue.levelVip)
    commit('SET_BALANCE', userValue.money)
    commit('SET_RANK', userValue.rank)
    commit('SET_THUMBURL', userValue.thumbUrl)
    commit('SET_REFERRALMONANY', userValue.referralMonany)
    commit('SET_FACEBOOK_ID', userValue.facebookId)
    commit('SET_SILVER_COIN', userValue.silverCoin)
    //目前vip為4以上，即可使用vip購點
    if (userValue.levelVip >= 4) {
      this.commit(`${STATION}/payment/SET_VIP_STATE`, true)
    }

    await dispatch('setAvatarIcon')
    commit('SET_ISLOGIN', true)
    commit('SET_ONLINE', true)
  },
  async setCoins({ commit }, data) {
    data.forEach((coin) => {
      switch (coin.id) {
        case 109469:
        case 114539:
          commit('SET_PK_COINS', { id: coin.id, count: coin.balance })
          break
        case 109628:
        case 116449:
          commit('SET_RUBY', { id: coin.id, count: coin.balance })
          break
        case 109481:
        case 114622:
          commit('SET_BATTLE_COIN', { id: coin.id, count: coin.balance })
          break
        case 109799:
        case 117104:
          commit('SET_PREMIUM_KEY', { id: coin.id, count: coin.balance })
          break
      }
    })
  },
  async setAvatarIcon({ commit, getters }) {
    const level = getters['level']
    let xinLevel = 0
    let newStarsClassImg = ''
    let starsCLassNumber1 = ''
    let starsCLassNumber2 = ''
    let calculateStarsLevel = ''
    let starsClassData = []
    switch (true) {
      case level === 0:
        xinLevel = 0
        break
      case level >= 1 && level <= 49:
        xinLevel = 1
        break
      case level > 49 && level < 200:
        xinLevel = 2
        break
      case level > 199 && level < 1000:
        xinLevel = 3
        break
      case level > 999 && level < 5000:
        xinLevel = 4
        break
      case level > 9999 && level < 20000:
        xinLevel = 5
        break
      case level > 19999 && level < 50000:
        xinLevel = 6
        break
      case level > 49999 && level < 100000:
        xinLevel = 7
        break
      case level > 99999 && level < 150000:
        xinLevel = 8
        break
      case level > 149999 && level < 200000:
        xinLevel = 9
        break
      case level >= 200000:
        if (level <= 200000) {
          starsCLassNumber1 = '1'
          starsCLassNumber2 = '0'
        } else {
          let upCondition = 50000
          calculateStarsLevel = level === 200000 ? 1 : (level - 200000) / upCondition
          calculateStarsLevel = calculateStarsLevel + 10
          if (calculateStarsLevel > 99) {
            calculateStarsLevel = 99
          }
          newStarsClassImg = new String(Math.ceil(calculateStarsLevel))
          starsCLassNumber1 = newStarsClassImg[0]
          starsCLassNumber2 = newStarsClassImg[1]
        }

        starsClassData = [
          'rank_10.png',
          'num_' + starsCLassNumber1 + '.png',
          'num_' + starsCLassNumber2 + '.png'
        ]

        xinLevel = 10

        break

      default:
        xinLevel = 0
        break
    }
    const vipLevel = getters['vipLevel']
    const selectedAvatarFrameImg = 'rankframe_' + vipLevel + '.png'

    if (starsClassData.length === 0) {
      starsClassData = ['rank_' + xinLevel + '.png']
    }
    commit('SET_AVATARFRAMEICON', selectedAvatarFrameImg)
    commit('SET_STARSCLASSICON', starsClassData)
  },
  // eslint-disable-next-line no-unused-vars
  async getThumbUrl({ commit, getters, rootGetters }, { userName, getCache }) {
    const now = new Date() // 獲取當前日期和時間
    const useCache = getCache === false ? false : true
    let thumbUrl = process.env.IMAGE_URL + '/photo_stickers/default.png'
    try {
      const cacheAvatar = rootGetters['chat/cacheAvatar']
      if (useCache && userName in cacheAvatar && now < cacheAvatar[userName].expiresAt) {
        return cacheAvatar[userName].url
      }
      // 使用星城圖片加密規則
      const usernameEncode = this.$xinUtility.usernameEncode(userName)
      const response = await this.$xinUserPhoto.api.load('DownLoadHeadIMG', usernameEncode, 'TW')
      if (response !== 0) {
        const regex = /https?:\/\/\S+/g
        const url = response.match(regex)
        if (url) {
          thumbUrl = url[0]
        }
      }

      const expiresAt = new Date(now.getTime() + 30 * 60000) // 增加半小時
      commit(
        'chat/SET_CACHE_AVATAR',
        { name: userName, data: { url: thumbUrl, expiresAt: expiresAt } },
        { root: true }
      )
    } catch (err) {
      console.log(`err of get user ${userName}: `, err)
    }
    return thumbUrl
  },
  async saveToken({ dispatch, commit }, data) {
    // timestamps
    // const now = this.$moment().format('X')
    // 日期轉成我們看的懂的語法
    // const expiresDate = this.$moment.unix(expiresAt).format('ddd, DD MMM YYYY HH:mm:ss')
    const maxAge = 86400
    const xinToken = data.clientId + '-' + data.protocolId + '-' + data.token
    commit('SET_XINTOKEN', xinToken)
    this.$cookies.set('xinToken', xinToken, { path: '/', maxAge })
    await dispatch('getToken')
  },
  async getToken({ commit }) {
    const xinToken = this.$cookies.get('xinToken')
    const splits = xinToken.split('-') // 使用 "-" 分割字串
    const connInfo = {
      clientId: Number(splits[0]),
      protocolId: Number(splits[1]),
      token: Number(splits[2])
    }
    commit('SET_CONNINFO', connInfo)
    commit('SET_XINTOKEN', xinToken)
  },

  async uploadPhoto(ctx, { arrayBuffer, username }) {
    let uint8Array = arrayBuffer
    // eslint-disable-next-line no-unused-vars
    const blob = new Blob([uint8Array], { type: 'image/png' })
    const crc32 = this.$xinUtility.crc32(uint8Array)

    const date = new Date()
    // 將日期取出 HHmmssdd 當作 iv
    const iv =
      date.getHours().toString().padStart(2, '0') +
      date.getMinutes().toString().padStart(2, '0') +
      date.getSeconds().toString().padStart(2, '0') +
      date.getDate().toString().padStart(2, '0')

    // 將日期轉為字串當作 Data，給Server 驗證使用
    const data =
      date.getFullYear().toString().padStart(4, '0') +
      '-' +
      (date.getMonth() + 1).toString().padStart(2, '0') +
      '-' +
      date.getDate().toString().padStart(2, '0') +
      ' ' +
      date.getHours().toString().padStart(2, '0') +
      ':' +
      date.getMinutes().toString().padStart(2, '0') +
      ':' +
      date.getSeconds().toString().padStart(2, '0')

    const usernameEncoded = this.$xinUtility.usernameEncode(username)
    const encrypted = this.$xinUtility.encryptoDes(usernameEncoded, { iv, key: 'waninxin' })
    const image = new File([uint8Array], username + '.png', { type: 'image/png' })

    const response = await this.$xinUserPhoto.api.upload(
      usernameEncoded,
      data,
      encrypted,
      crc32,
      image
    )
    return response
  },

  async getBetReport(ctx, username) {
    const reqData = this.$wsPacketFactory.getBetReport(username)
    this.$wsClient.send(reqData)
    const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
      return data.isFeature(this.$xinConfig.FEATURE.USER_DETAIL.BET_REPORT)
    })
    return res
  },
  async getAchievement(ctx, username) {
    const reqData = this.$wsPacketFactory.getAchievement(username)
    this.$wsClient.send(reqData)
    const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
      return data.isFeature(this.$xinConfig.FEATURE.USER_DETAIL.ACHIEVEMENT)
    })
    return res
  },
  async updateUserDetail({ state, dispatch, commit }) {
    const res = await dispatch('social/getUserDetail', state.userName, { root: true })
    commit('SET_PLATFORMID', res.platformId)
    commit('SET_GAMEID', res.gameId)
    commit('SET_ACCOUNTTYPE', res.accountType)
    commit('SET_ISBIND', res.isBind)
    commit('SET_HONOR', res.honor)
    commit('SET_ACTIVEVALUE', res.activeValue)
    commit('SET_LIMIT', res.limit)
    commit('SET_DISCOUNT', res.discount)
    commit('SET_ONLINE', res.online)
  },
  startLoginTimer({ state, commit }) {
    commit('SET_LOGIN_TIMMER_STATE', true)
    let timer = setInterval(() => {
      if (state.loginLastTime > 0) {
        let loginLastTime = state.loginLastTime - 1
        commit('SET_LOGIN_LAST_TIME', loginLastTime)
        if (!state.loginTimmerState) {
          clearInterval(timer)
        }
      } else {
        commit('SET_LOGIN_TIMMER_STATE', false)
        clearInterval(timer)
      }
    }, 1000)
  },
  setPlayerProfile({ dispatch, commit, rootGetters: { station } }) {
    const {
      env: { STATION }
    } = station
    dispatch('updateVIPLevelData') // 基本的設定項目，包含可能顯示的選項
    const baseData = [
      {
        action: 'account_circle',
        active: false,
        items:
          STATION === 'vietnam_01'
            ? [{ title: 'personal_status', link: '/player/info/status' }]
            : [
                { title: 'personal_status', link: '/player/info/status' },
                { title: 'login_info', link: '/player/info/login' },
                { title: 'daily_list', link: '/player/ranking/daily' }
              ],
        title: 'player_info'
      },
      { action: 'group', title: 'friend', link: '/player/info/friend' },
      { action: 'logout ', items: [], title: 'logout' }
    ]

    let data
    // 根據不同站點設定不同的顯示項目
    switch (STATION) {
      case 'qc_overseas':
      case 'qc_domestic':
        data = [{ action: 'logout ', items: [], title: 'logout' }]
        break
      case 'taiwan_01':
        data = [
          {
            ...baseData[0],
            items: [
              ...baseData[0].items,
              { title: 'achievement', link: '/player/info/achievements' },
              { title: 'backpack_title', link: '/player/info/inventory' }
            ]
          },
          ...baseData.slice(1)
        ]
        break
      case 'taiwan_02':
        data = [
          {
            ...baseData[0],
            items: [
              ...baseData[0].items,
              { title: 'achievement', link: '/player/info/achievements' }
            ]
          },
          ...baseData.slice(1)
        ]
        break
      default:
        data = [...baseData]
    }
    commit('SET_PLAYER_PROFILE', data)
  }, //取得麻將之星等級
  async getMjsRank() {
    const reqData = this.$wsPacketFactory.getMahjongRank()
    this.$wsClient.send(reqData)
    const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
      return data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.MAHJONG)
    })
    return res
  },
  updateVIPLevelData({ commit }) {
    commit('SET_VIP_LEVEL_TITLE', this.$UIConfig.vipLevel.vipLevelTitle)
    commit('SET_VIP_LEVEL_IMG_FILE_NAME', this.$UIConfig.vipLevel.vipLevelImgFileName)
    commit('SET_VIP_LEVEL_IMG_3X_FILE_NAME', this.$UIConfig.vipLevel.vipLevelImg3xFileName)
    commit('SET_VIP_LEVEL_ICON_FILE_NAME', this.$UIConfig.vipLevel.vipLevelIconFileName)
  },
  updateUserDefaultAvatar({ commit }) {
    commit('SET_USER_DEFAULT_AVATAR', this.$UIConfig.userDefaultAvatar)
  },
  // 取得左側功能列的角色功能清單
  getRoleCategory({ commit, rootGetters: { station } }) {
    const {
      lock: { guild }
    } = station

    let data
    if (guild) {
      data = [
        {
          icon: 'emoji_flags',
          active: true,
          items: [
            { name: 'guild_info', path: '/guild/info' },
            { name: 'guild_list', path: '/guild/list' }
          ],
          name: 'guild'
        },
        { icon: 'local_activity', items: [], name: 'redeem' }
      ]
    } else {
      data = [{ icon: 'local_activity', items: [], name: 'redeem' }]
    }
    commit('SET_ROLE_CATEGORY', data)
  }
}

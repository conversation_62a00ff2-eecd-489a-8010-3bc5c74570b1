import Vue from 'vue'
import cloneDeep from 'lodash/cloneDeep'
import SpeexDecoder from '~/plugins/NSpeex/NSpeex.Decoder.js'
import BandMode from '~/plugins/NSpeex/BandMode.js'

import Utility from '~/plugins/xin-socket/utility'
const STATION = process.env.STATION
const providersConfig = require(`@/station/${STATION}/config.providers`).default

const globalTitle = 'global_chat'
const guildTitle = 'guild_chat'
const channelTitle = 'chat_session'
//用於判別星城所回傳密語之暱稱
const officialName = ['官方-', 'Official', '@lang_223']
const checkMsgType = (msg) =>
  msg.content.type === 3 || msg.content.type === 4 || msg.content.type === 8
const itemLimit = 200

function decompressAndTranscodeAudio(Data) {
  if (!Data) return

  // 先转成 Uint8Array
  let U8Arr = new Uint8Array(Data)
  // 建立 Int16Array
  let I16Arr = new Int16Array(U8Arr.length * 4)

  let SpDecoder = new SpeexDecoder(BandMode.Narrow, false)
  try {
    SpDecoder.Decode(U8Arr, 0, U8Arr.length, I16Arr, 0, false) // 解压缩
  } catch (e) {
    U8Arr = I16Arr = SpDecoder = null
    return
  }
  let F32Arr = new Float32Array(I16Arr) // 建立信号格式所需的数据数组
  for (let i = 0; i < F32Arr.length; i++) {
    if (F32Arr[i] !== 0) F32Arr[i] = F32Arr[i] / 32767.0 // 转换信号值 -1~1
  }

  return F32Arr
}

export const state = () => ({
  chats: [
    {
      title: globalTitle,
      id: 0,
      img: 'allChannel.png',
      noty: 0,
      isOfficial: true,
      key: globalTitle,
      isBlock: false,
      isFriend: false,
      lastUpdate: new Date(),
      pin: null
    },
    {
      title: guildTitle,
      id: 1,
      img: 'photo-chat.png',
      noty: 0,
      isOfficial: true,
      key: guildTitle,
      isBlock: false,
      isFriend: false,
      lastUpdate: new Date(),
      pin: null
    }
  ],
  msg: { global_chat: [], guild_chat: [] },
  customerServiceMsg: [],
  allChatNoty: 0,
  whisperNoty: 0,
  customerServiceNoty: 0,
  customerSendType: 0,
  setting: {
    opacity: 0.6,
    global: {
      stiker: true,
      customStiker: true,
      voice: true,
      autoVoice: true
    },
    whisper: {
      stiker: true,
      customStiker: true,
      voice: true,
      autoVoice: true,
      notySound: true,
      onlyFriend: false
    }
  },
  isKeyboardOpen: false,
  cacheAvatar: {},
  selectedChat: 0,
  showSemantic: false,
  semanticToken: undefined,
  channels: []
})

export const getters = {
  chats(state) {
    return state.chats
  },
  msg(state) {
    return state.msg
  },
  customerServiceMsg(state) {
    return state.customerServiceMsg
  },
  allChatNoty(state) {
    return state.allChatNoty
  },
  whisperNoty(state) {
    return state.whisperNoty
  },
  customerServiceNoty(state) {
    return state.customerServiceNoty
  },
  customerSendType(state) {
    return state.customerSendType
  },
  setting(state) {
    return state.setting
  },
  opacity(state) {
    return state.setting.opacity
  },
  globalSetting(state) {
    return state.setting.global
  },
  whisperSetting(state) {
    return state.setting.whisper
  },
  isKeyboardOpen(state) {
    return state.isKeyboardOpen
  },
  cacheAvatar(state) {
    return state.cacheAvatar
  },
  selectedChat(state) {
    return state.selectedChat
  },
  showSemantic(state) {
    return state.showSemantic
  },
  semanticToken(state) {
    return state.semanticToken
  },
  channels(state) {
    return state.channels
  }
}

export const mutations = {
  SET_CHATS(state, payload) {
    state.chats = payload
  },
  ADD_CHATS(state, payload) {
    state.chats.push(payload)
  },
  REMOVE_CHATS(state, payload) {
    const index = state.chats.findIndex((item) => item.title === payload)
    if (index !== -1) {
      state.chats.splice(index, 1)
    }
  },
  SET_CHAT_IS_BLOCK(state, { key, payload }) {
    const index = state.chats.findIndex((item) => item.key === key)

    if (index !== -1) {
      state.chats[index].isBlock = payload
    }
  },
  SET_CHAT_IS_FRIEND(state, { key, payload }) {
    const index = state.chats.findIndex((item) => item.key === key)

    if (index !== -1) {
      state.chats[index].isFriend = payload
    }
  },
  ADD_CHANNELS(state, payload) {
    state.channels.push(payload)
  },
  SET_MSG(state, { title, payload }) {
    //因為msg是object，所以要用Vue.set才能觸發更新
    Vue.set(state.msg, title, payload)
  },
  ADD_MSG(state, { title, payload }) {
    state.msg[title].push(payload)
    if (state.msg[title].length > itemLimit) {
      state.msg[title].shift()
    }
  },
  ADD_GUILD_MSG(state, { title, payload }) {
    state.msg[title].push(payload)
    state.msg[title].sort((a, b) => {
      return a.id - b.id
    })
    if (state.msg[title].length > itemLimit) {
      state.msg[title].shift()
    }
  },
  UPDATE_CHANNEL_POPULATION(state, payload) {
    let channel = state.channels[payload.index]
    if (channel.population > payload.population) {
      channel.popIsUp = false
    } else if (channel.population < payload.population) {
      channel.popIsUp = true
    }
    channel.population = payload.population
  },
  UPDATE_CHANNEL_POINT(state, payload) {
    let channel = state.channels[payload.index]
    channel.point = payload.point
  },
  SORT_CHANNEL(state, payload) {
    const selfName = payload.selfName
    function sortChannel(a, b) {
      // 第一優先條件：如果頻道名稱與自己的角色名稱一樣，排在前面
      if (a.name === selfName && b.name !== selfName) {
        return -1
      }
      if (!a.name !== selfName && b.name === selfName) {
        return 1
      }

      // 第二條件：如果頻道名稱開頭為 *，排在前面
      if (a.name.startsWith('*') && !b.name.startsWith('*')) {
        return -1
      }
      if (!a.name.startsWith('*') && b.name.startsWith('*')) {
        return 1
      }

      // 第三條件：按照人數排序（不包含"星城頻道"開頭的）
      if (!a.name.startsWith('星城頻道') && !b.name.startsWith('星城頻道')) {
        return b.population - a.population
      }

      return 0 // 其他情況不變
    }

    state.channels = payload.channels.sort(sortChannel)
  },
  ADD_CUSTOMER_SERVICE_MSG(state, payload) {
    state.customerServiceMsg.push(payload)
  },
  UNSHIFT_CUSTOMER_SERVICE_MSG(state, payload) {
    state.customerServiceMsg.unshift(payload)
  },
  SET_CHAT_ONLINE_STATUS_BY_ID(state, payload) {
    let index = state.chats.findIndex((item) => item.id === payload.id)
    if (index !== -1) {
      state.chats[index].online = payload.online
    }
  },
  SET_CHAT_ONLINE_STATUS_BY_NAME(state, payload) {
    let index = state.chats.findIndex((item) => item.title === payload.name)
    if (index !== -1) {
      state.chats[index].online = payload.online
    }
  },
  SET_NOTY(state, { index, payload }) {
    state.chats[index].noty = payload
  },
  REMOVE_USER_FROM_CHANNEL(state, payload) {
    if (payload.length !== 0)
      payload.forEach((user) => {
        state.currentChannelDetail.users.forEach((channelUser, index) => {
          if (user.username === channelUser.username) {
            state.currentChannelDetail.users.splice(index, 1)
          }
        })
      })
  },
  ADD_USER_TO_CHANNEL(state, payload) {
    if (payload.length !== 0)
      payload.forEach((user) => {
        const userExist = state.currentChannelDetail.users.find((channelUser) => {
          return user.username === channelUser.username
        })
        if (!userExist) state.currentChannelDetail.users.push(user)
      })
  },
  SET_ALL_CHAT_NOTY(state, payload) {
    state.allChatNoty = payload
  },
  ADD_ALL_CHAT_NOTY(state, payload) {
    state.allChatNoty += payload
  },
  SET_WHISPER_NOTY(state, payload) {
    state.whisperNoty = payload
  },
  ADD_WHISPER_NOTY(state, payload) {
    state.whisperNoty += payload
  },
  ADD_CUSTOMER_SERVICE_NOTY(state, payload) {
    state.customerServiceNoty += payload
  },
  SET_CUSTOMER_SERVICE_NOTY(state, payload) {
    state.customerServiceNoty = payload
  },
  SET_CUSTOMER_SEND_TYPE(state, payload) {
    state.customerSendType = payload
  },
  CLEAR_ALL_CHAT_NOTY(state) {
    state.allChatNoty = 0
  },
  CLEAR_CUSTOMER_SERVICE_NOTY(state) {
    state.customerServiceNoty = 0
  },
  CLEAR_CUSTOMER_SEND_TYPE(state) {
    state.customerSendType = 0
  },
  SET_SETTING(state, payload) {
    state.setting = payload
  },
  SET_OPACITY(state, payload) {
    state.setting.opacity = payload
  },
  SET_GLOBAL_STIKER(state, payload) {
    state.setting.global.stiker = payload
  },
  SET_GLOBAL_CUSTOM_STIKER(state, payload) {
    state.setting.global.customStiker = payload
  },
  SET_GLOBAL_VOICE(state, payload) {
    state.setting.global.voice = payload
  },
  SET_GLOBAL_AUTO_VOICE(state, payload) {
    state.setting.global.autoVoice = payload
  },
  SET_WHISPER_STIKER(state, payload) {
    state.setting.whisper.stiker = payload
  },
  SET_WHISPER_CUSTOM_STIKER(state, payload) {
    state.setting.whisper.customStiker = payload
  },
  SET_WHISPER_VOICE(state, payload) {
    state.setting.whisper.voice = payload
  },
  SET_WHISPER_AUTO_VOICE(state, payload) {
    state.setting.whisper.autoVoice = payload
  },
  SET_WHISPER_NOTY_SOUND(state, payload) {
    state.setting.whisper.notySound = payload
  },
  SET_WHISPER_ONLY_FRIEND(state, payload) {
    state.setting.whisper.onlyFriend = payload
  },
  SET_IS_REAED(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].isRead = payload.status
      }
    }
  },
  SET_CUSTOMER_SERVICE_IS_REAED(state, payload) {
    let index = state.customerServiceMsg.findIndex((item) => item.id === payload.id)
    if (index !== -1) {
      state.customerServiceMsg[index].isRead = payload.status
    }
  },
  /**
   * 根據訊息 id，批次新增或更新客服訊息物件的指定欄位
   * 若找不到對應訊息則不處理
   * 使用 Vue.set 確保響應式更新
   */
  SET_CUSTOMER_SERVICE_MSG(state, { id, payload }) {
    const index = state.customerServiceMsg.findIndex((item) => item.id === id)
    if (index === -1) return

    // 逐一將 payload 內的 key-value 設定到訊息物件，確保響應式
    payload.forEach(({ key, value }) => {
      Vue.set(state.customerServiceMsg[index].msg, key, value)
    })
  },
  CLEAR_ALL_MSG(state) {
    for (const key in state.msg) {
      Vue.set(state.msg, key, [])
    }
    state.chats.forEach((chat) => {
      chat.noty = 0
    })
    this.commit('social/CLEAR_FRIEND_ALL_NOTY', null, { root: true })
    mutations.CLEAR_ALL_CHAT_NOTY(state)
  },
  SET_CACHE_AVATAR(state, payload) {
    Vue.set(state.cacheAvatar, payload.name, payload.data)
  },
  SET_IS_PLAYING(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.isPlaying = payload.isPlaying
      }
    }
  },
  SET_STICKER(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.stickerId = payload.stickerId
      }
    }
  },
  SET_STICKER_ERROR(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.errorStatus = payload.errorStatus
      }
    }
  },
  SET_CHAT_IMG(state, payload) {
    const index = state.chats.findIndex((item) => item.title === payload.title)
    if (index !== -1) {
      state.chats[index].img = payload.img
    }
  },
  SET_CUSTOM_IMG(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.imageUrl = payload.imageUrl
        state.msg[payload.key][index].msg.thumbUrl = payload.thumbUrl
      }
    }
  },
  SET_FRIEND_PIN(state, data) {
    let chat = state.chats
      .filter((item) => item.isFriend === true)
      .find((item) => item.title === data.title)
    if (chat) {
      chat.pin = data.pin
    }
  },
  UPDATE_AUDIO_BUFFER(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.audioBuffer = payload.audioBuffer
      }
    }
  },
  UPDATE_DURATION(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.duration = payload.duration
      }
    }
  },
  UPDATE_GET_AUDIO_BUFFER_STATUS(state, payload) {
    if (payload.key in state.msg) {
      let index = state.msg[payload.key].findIndex((item) => item.id === payload.id)
      if (index !== -1) {
        state.msg[payload.key][index].msg.getAudioBufferStatus = payload.getAudioBufferStatus
      }
    }
  },
  UPDATE_CHAT_LAST_UPDATE(state, payload) {
    const index = state.chats.findIndex((item) => item.title === payload.key)
    if (index !== -1) {
      state.chats[index].lastUpdate = payload.lastUpdate
    }
  },
  SET_IS_KEYBOARD_OPEN(state, payload) {
    state.isKeyboardOpen = payload
  },
  SET_SELECTED_CHAT(state, payload) {
    state.selectedChat = payload
  },
  SET_SHOW_SEMANTIC(state, payload) {
    state.showSemantic = payload
  },
  SET_SEMANTIC_TOKEN(state, payload) {
    if (!state.semanticToken) state.semanticToken = payload
  },
  RESET(state) {
    Vue.set(state, 'msg', { global_chat: [], guild_chat: [] })
    Vue.set(state, 'chats', [
      {
        title: globalTitle,
        id: 0,
        img: 'allChannel.png',
        noty: 0,
        isOfficial: true,
        key: globalTitle,
        isBlock: false,
        isFriend: false,
        lastUpdate: new Date()
      },
      {
        title: guildTitle,
        id: 1,
        img: 'allChannel.png',
        noty: 0,
        isOfficial: true,
        key: guildTitle,
        isBlock: false,
        isFriend: false,
        lastUpdate: new Date(),
        pin: null
      }
    ])
    Vue.set(state, 'selectedChat', 0)
    Vue.set(state, 'allChatNoty', 0)
    Vue.set(state, 'whisperNoty', 0)
    Vue.set(state, 'customerServiceNoty', 0)
    Vue.set(state, 'customerServiceMsg', [])
    Vue.set(state, 'isKeyboardOpen', false)
    Vue.set(state, 'cacheAvatar', {})
    Vue.set(state, 'semanticToken', undefined)
  }
}
export const actions = {
  addWelcomeToGlobalChat({ state, commit }) {
    const welcomeMessageContent = this.$UIConfig.chatroom.welcomeMessageContent
    // 如果歡迎訊息是空的，就不加入
    if (!welcomeMessageContent) return
    const welcomeMessage = {
      id: Date.now(),
      messageType: -1,
      icon: 'campaign',
      date: new Date(),
      msg: welcomeMessageContent,
      observed: true,
      user: { name: 'official' },
      isRead: true
    }
    if (state.msg[globalTitle].length === 0) {
      commit('ADD_MSG', { title: globalTitle, payload: welcomeMessage })
      commit('UPDATE_CHAT_LAST_UPDATE', { key: globalTitle, lastUpdate: new Date() })
    }
  },
  async addForbiddenChat({ state, commit, dispatch, rootGetters }, { message, title }) {
    const selfName = rootGetters['role/userName']
    const thumbUrl = await dispatch('role/getThumbUrl', { userName: selfName }, { root: true })
    const now = Date.now()

    const msg = {
      id: now + String(Math.floor(Math.random() * 100000)).padStart(5, '0'),
      messageType: 4,
      img: thumbUrl,
      user: {
        name: selfName,
        color: { red: 54, green: 191, blue: 54 } // 綠色
      },
      date: now,
      msg: {
        color: { red: 255, blue: 255, green: 255 }, // 白色
        message,
        type: 3
      },
      isRead: false,
      isBlock: false
    }

    // 處理消息添加
    if (title === 'customer') {
      commit('ADD_CUSTOMER_SERVICE_MSG', msg)
    } else if (title in state.msg) {
      const mutationType = title === guildTitle ? 'ADD_GUILD_MSG' : 'ADD_MSG'
      commit(mutationType, { title, payload: msg })
    } else {
      commit('SET_MSG', { title, payload: [msg] })
    }

    // 更新狀態
    commit('SET_SHOW_SEMANTIC', true)
    commit('UPDATE_CHAT_LAST_UPDATE', { key: title, lastUpdate: new Date() })
  },
  async addGlobalChat({ state, dispatch, commit, rootGetters }, { payload, avatar }) {
    if (this.$UIConfig.lock.chatroom.checkoutUserStation) {
      const res = await dispatch('social/getUserCategory', payload.user.name, { root: true })
      if (!res.isSameCountry) return
    }
    if (checkMsgType(payload)) {
      const isBlock = await dispatch('isBlock', payload.user.name)
      if (!isBlock) {
        const selfName = rootGetters['role/userName']
        const msg = await dispatch('createMsg', { payload, avatar, type: 'global' })
        const isSelf = payload.user.name === selfName

        if (globalTitle in state.msg) {
          commit('ADD_MSG', { title: globalTitle, payload: msg })
        } else {
          commit('SET_MSG', { title: globalTitle, payload: [msg] })
        }

        commit('UPDATE_CHAT_LAST_UPDATE', { key: globalTitle, lastUpdate: new Date() })

        if (!isSelf) {
          commit('SET_NOTY', { index: 0, payload: state.chats[0].noty + 1 })
          dispatch('countAllChatNoty')
        }
      }
    }
  },
  async addGuildChat({ state, dispatch, commit, rootGetters }, { payload, avatar }) {
    if (!this.$UIConfig.lock.guild) return
    const stringIsNullOrEmpty = (str) => {
      return str === '' || str === null || str === undefined
    }
    const hexToRgb = (hex) => {
      hex = '#' + hex
      const color = hex
        .replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i, (m, r, g, b) => '#' + r + r + g + g + b + b)
        .substring(1)
        .match(/.{2}/g)
        .map((x) => parseInt(x, 16))
      return { red: color[0], green: color[1], blue: color[2] }
    }
    const isBlock = await dispatch('isBlock', payload.username)
    let thumbUrl = rootGetters['role/thumbUrl']
    let msg = {}
    let guildMsg = {}
    let userType = {}
    let userMsg = {}
    let msgType = stringIsNullOrEmpty(payload.username) ? 1 : 2
    const env = process.env.NUXT_ENV
    const downloadBaseURL = providersConfig[env].fileService.storage
    const userPath = Utility.getUserPath(payload.username)
    if (avatar)
      thumbUrl = await dispatch('role/getThumbUrl', { userName: payload.username }, { root: true })
    try {
      guildMsg = JSON.parse(payload.message)
      if (!stringIsNullOrEmpty(payload.username)) {
        userType = {
          name: payload.username,
          color: { red: 54, green: 191, blue: 54 }
        }
        switch (guildMsg.Type) {
          case 0:
            {
              const message = await dispatch(
                'literalKeywords/replaceKeywords',
                guildMsg.Contents.Text,
                {
                  root: true
                }
              )
              userMsg = {
                color: hexToRgb(guildMsg.Contents.TextColor),
                message: message,
                type: 3
              }
            }

            msgType = 4
            break
          case 1:
            {
              const [type, token, path1, path2] = guildMsg.Contents.ImageURL.split(' ')
              const userThumbUrl = `${downloadBaseURL}userfile/${userPath}/${path2.toUpperCase()}/data.png?t=${token}`
              const userImageUrl = `${downloadBaseURL}userfile/${userPath}/${path1.toUpperCase()}/data.png?t=${token}`
              userMsg = {
                color: { red: 255, blue: 255, green: 255 },
                imageUrl: userImageUrl,
                thumbUrl: userThumbUrl,
                type: 8,
                dataType: Number(type)
              }
            }
            break
          case 2:
            {
              const [type, token, path1] = guildMsg.Contents.VoiceURL.split(' ')
              const soundUrl = `${downloadBaseURL}userfile/${userPath}/${path1.toUpperCase()}/data.mp3?t=${token}`
              fetch(soundUrl).then((res) => {
                res.arrayBuffer().then((buffer) => {
                  const audio = decompressAndTranscodeAudio(buffer)
                  const audioContext = new (window.AudioContext || window.webkitAudioContext)()
                  const audioBuffer = audioContext.createBuffer(1, audio.length, 7600)
                  audioBuffer.copyToChannel(audio, 0)
                  const key = guildTitle
                  commit('UPDATE_AUDIO_BUFFER', {
                    key: key,
                    id: payload.time,
                    audioBuffer: audioBuffer
                  })
                  commit('UPDATE_DURATION', {
                    key: key,
                    id: payload.time,
                    duration: Math.ceil(audioBuffer.duration)
                  })
                  commit('UPDATE_GET_AUDIO_BUFFER_STATUS', {
                    key: key,
                    id: payload.time,
                    getAudioBufferStatus: true
                  })
                })
              })
              userMsg = {
                color: { red: 255, blue: 255, green: 255 },
                getAudioBufferStatus: false,
                audioBuffer: null,
                type: 8,
                dataType: Number(type),
                isPlaying: false,
                duration: 0
              }
            }
            break
          case 3:
            userMsg = {
              color: { red: 255, blue: 255, green: 255 },
              errorStatus: false,
              stickerId: guildMsg.Contents.StickerName,
              type: 4
            }
            break
          default: {
            const message = await dispatch('literalKeywords/replaceKeywords', payload.message, {
              root: true
            })
            userMsg = {
              color: { red: 255, blue: 255, green: 255 },
              message: message,
              type: 3
            }
          }
        }
      }
    } catch (e) {
      userType = !stringIsNullOrEmpty(payload.username)
        ? {
            name: payload.username,
            color: { red: 54, green: 191, blue: 54 }
          }
        : { name: 'system', color: { red: 54, green: 191, blue: 54 } }
      userMsg = {
        color: { red: 251, blue: 251, green: 251 },
        message: payload.message,
        type: 3
      }
    }
    msg = stringIsNullOrEmpty(payload.username)
      ? {
          id: payload.time + String(Math.floor(Math.random() * 100000)).padStart(5, '0'),
          date: payload.time,
          messageType: msgType,
          img: '',
          user: userType,
          msg: userMsg,
          //observed: false,
          isRead: true,
          isBlock: isBlock
        }
      : {
          id: payload.time + String(Math.floor(Math.random() * 100000)).padStart(5, '0'),
          date: payload.time,
          messageType: msgType,
          img: thumbUrl,
          user: userType,
          msg: userMsg,
          isRead: false,
          isBlock: isBlock
        }
    if (!isBlock) {
      const selfName = rootGetters['role/userName']
      const isSelf = payload.username === selfName
      if (isSelf) msg.isRead = true
      if (guildTitle in state.msg) commit('ADD_GUILD_MSG', { title: guildTitle, payload: msg })
      else commit('SET_MSG', { title: guildTitle, payload: [msg] })
      commit('UPDATE_CHAT_LAST_UPDATE', { key: guildTitle, lastUpdate: new Date() })
      if (!isSelf && !stringIsNullOrEmpty(payload.username)) {
        commit('SET_NOTY', { index: 1, payload: state.chats[1].noty + 1 })
      }
      dispatch('countAllChatNoty')
      dispatch('countWhisperNoty')
    }
  },
  resetGuildChat({ dispatch, commit }, data) {
    if (data.hasGuild) return
    commit('SET_NOTY', { index: 1, payload: 0 })
    commit('SET_MSG', { title: guildTitle, payload: [] })
    dispatch('countAllChatNoty')
    dispatch('countWhisperNoty')
  },
  async addWhisperChat({ state, rootGetters, dispatch, commit }, { payload, avatar }) {
    if (checkMsgType(payload)) {
      const isBlock = await dispatch('isBlock', payload.user.name)
      if (!isBlock) {
        const dmText = ['密給', '@lang_215', 'Mật ngữ với']
        const selfName = rootGetters['role/userName']
        const msg = await dispatch('createMsg', { payload, avatar, type: 'whisper' })
        let chatIndex = -1
        let friendIndex = -1
        let isFriend = false
        const friendList = rootGetters['social/friendList']
        const isIncludeDMText = dmText.some((text) => {
          return payload.content.allMessage.includes(text)
        })
        const isSelf = isIncludeDMText ? msg.user.name === selfName : payload.user.name === selfName

        //check if friend
        friendList.forEach((friend, index) => {
          if (friend.username === payload.user.name) {
            friendIndex = index
          }
        })
        isFriend = friendIndex !== -1

        let isInChat = false

        state.chats.forEach((chat, index) => {
          if (payload.user.name === chat.title) {
            isInChat = true
            chatIndex = index
          }
        })

        if (!isInChat) {
          const maxId = state.chats.reduce((prev, current) =>
            prev.id > current.id ? prev : current
          ).id
          const isofficial = officialName.some((text) => {
            return msg.user.name.includes(text)
          })
          const chat = {
            title: isIncludeDMText ? payload.user.name : msg.user.name,
            id: maxId + 1,
            img: isFriend ? friendList[friendIndex].thumbUrl : msg.img,
            noty: isIncludeDMText ? 0 : 1,
            isOfficial: isofficial ? true : false,
            key: isIncludeDMText ? payload.user.name : msg.user.name,
            online: true,
            isBlock: isBlock,
            isFriend: isFriend,
            lastUpdate: new Date(),
            pin: null
          }
          commit('ADD_CHATS', chat)
        }
        //add noty & update lastupdate
        else {
          if (chatIndex !== -1 && !isSelf) {
            commit('SET_NOTY', { index: chatIndex, payload: state.chats[chatIndex].noty + 1 })
          }
          commit('UPDATE_CHAT_LAST_UPDATE', {
            key: isIncludeDMText ? payload.user.name : msg.user.name,
            lastUpdate: new Date()
          })
        }
        dispatch('countAllChatNoty')
        dispatch('countWhisperNoty')

        let finalMsg = msg
        // 如果是發送者是自己，且訊息類型為自訂貼圖，總部回傳封包的圖片路徑會有問題(userPath 是接收者，應該要是發送者)，需進行修正
        // APP 端也有針對這問題額外處理
        if (isIncludeDMText && msg.msg.type === 8) {
          const userName = rootGetters['role/userName']
          const userPath = Utility.getUserPath(userName)

          finalMsg = {
            ...msg,
            msg: {
              ...msg.msg,
              imageUrl: msg.msg.imageUrl.replace(/\/userfile\/[^/]+\//, `/userfile/${userPath}/`),
              thumbUrl: msg.msg.thumbUrl.replace(/\/userfile\/[^/]+\//, `/userfile/${userPath}/`)
            }
          }
        }
        if (payload.user.name in state.msg) {
          commit('ADD_MSG', { title: payload.user.name, payload: finalMsg })
        } else {
          commit('SET_MSG', { title: payload.user.name, payload: [finalMsg] })
        }
      }
    }
  },
  async addAnnouncement({ state, dispatch, commit }, payload) {
    payload.content = {}
    payload.user = {}
    payload.user.color = {}
    payload.content.color = {}
    payload.user.name = 'system'
    payload.content.message = payload.message
    payload.content.type = 3
    payload.user.color.red = 255
    payload.user.color.blue = 255
    payload.user.color.green = 255
    payload.content.color.red = 255
    payload.content.color.blue = 255
    payload.content.color.green = 255
    payload.messageType = 1
    const msg = await dispatch('createMsg', { payload, type: 'announcementMsg' })

    if (globalTitle in state.msg) {
      commit('ADD_MSG', { title: globalTitle, payload: cloneDeep(msg) })
    } else {
      commit('SET_MSG', { title: globalTitle, payload: [cloneDeep(msg)] })
    }

    commit('SET_NOTY', { index: 0, payload: state.chats[0].noty + 1 })
    dispatch('countAllChatNoty')
  },
  async addCustomerServiceMsg({ state, dispatch, commit, rootGetters }, payload) {
    const selfName = rootGetters['role/userName']
    //用於判別星城所回傳密語之暱稱
    const customerTextList = ['@lang_147', '【線上客服】']
    const isCustomer = customerTextList.some((text) => {
      return payload.message.startsWith(text)
    })
    const isSelf = payload.message.startsWith(selfName)
    const isCustomerServiceOrSelf = isCustomer || isSelf
    if (isCustomerServiceOrSelf) {
      let colonIndex

      if (payload.message.startsWith(customerTextList[0])) {
        colonIndex = payload.message.indexOf('\uFF04')
      } else {
        colonIndex = payload.message.indexOf('：')
      }

      if (colonIndex !== -1) {
        const serverTextIndex = this.$i18n.t('lang_147')
        const firstPart = payload.message.startsWith(customerTextList[0])
          ? serverTextIndex.substring(0, serverTextIndex.indexOf(':'))
          : payload.message.substring(0, colonIndex)
        const secondPart = payload.message.substring(colonIndex + 1)

        payload.content = {}
        payload.user = {}
        payload.user.color = {}
        payload.content.color = {}
        payload.user.name = firstPart
        payload.content.message = secondPart
        payload.content.type = 3
        payload.user.color.red = payload.color.r
        payload.user.color.blue = payload.color.b
        payload.user.color.green = payload.color.g
        payload.content.color.red = payload.color.r
        payload.content.color.blue = payload.color.b
        payload.content.color.green = payload.color.g
        payload.messageType = null
      }
    } else {
      payload.content = {}
      payload.user = {}
      payload.user.color = {}
      payload.content.color = {}
      payload.user.name = 'system'
      payload.content.message = payload.message
      payload.content.type = 3
      payload.user.color.red = payload.color.r
      payload.user.color.blue = payload.color.b
      payload.user.color.green = payload.color.g
      payload.content.color.red = payload.color.r
      payload.content.color.blue = payload.color.b
      payload.content.color.green = payload.color.g
      payload.messageType = 1
    }
    const msg = await dispatch('createMsg', { payload, type: 'customerServiceMsg' })
    if (isSelf) msg.isRead = true
    commit('ADD_CUSTOMER_SERVICE_MSG', msg)
    //  站台差異註解
    const isReport = state.customerSendType === 2
    if (isCustomer) {
      //此未讀訊息+1知情況僅限於使客服聊天

      commit('ADD_CUSTOMER_SERVICE_NOTY', 1)
    } else if (isReport) {
      commit('ADD_CUSTOMER_SERVICE_NOTY', 1)
    }
  },
  async createMsg({ dispatch, commit, rootGetters }, { payload, avatar, type }) {
    let thumbUrl = ''
    let userName = payload.user.name
    let msg = {}

    const selfName = rootGetters['role/userName']
    const selfAvatar = rootGetters['role/thumbUrl']
    const dmText = ['密給', '@lang_215', 'Mật ngữ với']
    const allMsg = payload.content
      ? payload.content.allMessage
        ? payload.content.allMessage
        : ''
      : ''
    const isIncludeDMText = dmText.some((text) => {
      return allMsg.includes(text)
    })
    if ((type === 'whisper' && isIncludeDMText) || userName === selfName) {
      thumbUrl = selfAvatar
      userName = selfName
    } else if (type === 'customerServiceMsg' || type === 'announcementMsg') {
      thumbUrl = null
    } else if (avatar) {
      thumbUrl = await dispatch('role/getThumbUrl', { userName: userName }, { root: true })
    }
    // 須注意，目前觀察server回傳#FF0000(紅色)的文字實際上應顯示為白色
    const isRed =
      payload.content.color.red === 255 &&
      payload.content.color.blue === 0 &&
      payload.content.color.green === 0

    const whiteColor = {
      red: 255,
      blue: 255,
      green: 255
    }

    //check if block
    const isBlock = await dispatch('isBlock', payload.user.name)
    const message = await dispatch('literalKeywords/replaceKeywords', payload.content.message, {
      root: true
    })
    //短時間會接收到大量系統公頻訊息  Date.now() 會有機會導致ID相同 再加上隨機5碼區格
    const createID = Date.now() + String(Math.floor(Math.random() * 100000)).padStart(5, '0')
    if (payload.content.type === 3) {
      msg = {
        id: createID,
        messageType: payload.messageType,
        user: {
          name: userName,
          color: payload.user.color
        },
        date: new Date(),
        msg: {
          color: isRed ? whiteColor : payload.content.color,
          message: message,
          type: payload.content.type
        },
        isRead: isBlock ? true : false,
        isBlock: isBlock
      }
    } else if (payload.content.type === 4) {
      msg = {
        id: createID,
        messageType: payload.messageType,
        user: {
          name: userName,
          color: payload.user.color
        },
        date: new Date(),
        msg: {
          color: isRed ? whiteColor : payload.content.color,
          stickerId: payload.content.stickerId,
          type: payload.content.type,
          errorStatus: false
        },
        isRead: isBlock ? true : false,
        isBlock: isBlock
      }
    } else if (payload.content.type === 8) {
      if (payload.content.dataType === 1) {
        msg = {
          id: createID,
          messageType: payload.messageType,
          user: {
            name: userName,
            color: payload.user.color
          },
          date: new Date(),
          msg: {
            color: isRed ? whiteColor : payload.content.color,
            imageUrl: payload.content.imageUrl,
            thumbUrl: payload.content.thumbUrl,
            type: payload.content.type,
            dataType: payload.content.dataType
          },
          isRead: isBlock ? true : false,
          isBlock: isBlock
        }
      } else if (payload.content.dataType === 2) {
        msg = {
          id: createID,
          messageType: payload.messageType,
          user: {
            name: userName,
            color: payload.user.color
          },
          date: new Date(),
          msg: {
            color: isRed ? whiteColor : payload.content.color,
            getAudioBufferStatus: false,
            audioBuffer: null,
            type: payload.content.type,
            dataType: payload.content.dataType,
            isPlaying: false,
            duration: 0
          },
          isRead: isBlock ? true : false,
          isBlock: isBlock
        }
        fetch(payload.content.voiceUrl).then((res) => {
          res.arrayBuffer().then((buffer) => {
            const audio = decompressAndTranscodeAudio(buffer)
            const audioContext = new (window.AudioContext || window.webkitAudioContext)()
            const audioBuffer = audioContext.createBuffer(1, audio.length, 7600)
            audioBuffer.copyToChannel(audio, 0)
            const key = type === 'global' ? globalTitle : payload.user.name
            commit('UPDATE_AUDIO_BUFFER', {
              key: key,
              id: createID,
              audioBuffer: audioBuffer
            })
            commit('UPDATE_DURATION', {
              key: key,
              id: createID,
              duration: Math.ceil(audioBuffer.duration)
            })
            commit('UPDATE_GET_AUDIO_BUFFER_STATUS', {
              key: key,
              id: createID,
              getAudioBufferStatus: true
            })
          })
        })
      }
    }
    if (thumbUrl?.length !== 0) msg.img = thumbUrl

    return msg
  },
  parseMessage(str) {
    const emojiMap = {
      9: 'smile.gif',
      a: 'laugh.gif',
      b: 'cry.gif'
      // ... 其他對應
    }
    // eslint-disable-next-line no-control-regex
    const regex = /\u001b\|\*([a-zA-Z0-9])/g
    let result = []
    let lastIndex = 0
    let match
    while ((match = regex.exec(str)) !== null) {
      if (match.index > lastIndex) {
        result.push({ type: 'text', value: str.slice(lastIndex, match.index) })
      }
      const emojiKey = match[1] // 只取最後一個字元
      if (emojiMap[emojiKey]) {
        result.push({ type: 'emoji', value: emojiMap[emojiKey], alt: emojiKey })
      } else {
        result.push({ type: 'text', value: match[0] })
      }
      lastIndex = regex.lastIndex
    }
    if (lastIndex < str.length) {
      result.push({ type: 'text', value: str.slice(lastIndex) })
    }
    return result
  },
  async countAllChatNoty({ commit, state, dispatch }) {
    let noty = 0
    for (let chatItem of state.chats) {
      if (
        chatItem.key === globalTitle ||
        chatItem.key === guildTitle ||
        chatItem.key === channelTitle ||
        (!chatItem.isBlock &&
          (!state.setting.whisper.onlyFriend || (await dispatch('isFriend', chatItem.key))))
      )
        noty += chatItem.noty
    }

    commit('SET_ALL_CHAT_NOTY', noty)
  },
  async countWhisperNoty({ commit, state, dispatch }) {
    let noty = 0
    for (let chatItem of state.chats) {
      if (
        chatItem.key !== globalTitle &&
        chatItem.key !== guildTitle &&
        !chatItem.isBlock &&
        (!state.setting.whisper.onlyFriend || (await dispatch('isFriend', chatItem.key)))
      )
        noty += chatItem.noty
    }

    commit('SET_WHISPER_NOTY', noty)
  },

  isBlock({ rootGetters }, payload) {
    const blockList = rootGetters['social/blockList']
    let isBlock = false
    blockList.forEach((block) => {
      if (block.username === payload) {
        isBlock = true
      }
    })
    return isBlock
  },
  isFriend({ rootGetters }, payload) {
    const friendList = rootGetters['social/friendList']
    let isFriend = false
    friendList.forEach((friend) => {
      if (friend.username === payload) {
        isFriend = true
      }
    })
    return isFriend
  },
  addFriendEvent({ commit, rootGetters, state }, username) {
    const index = state.chats.findIndex((chat) => username === chat.title)
    const friendList = rootGetters['social/friendList']
    const friend = friendList.find(
      (friend) => friend.username.toLowerCase() === username.toLowerCase()
    )
    //若對話不存在，則新增對話
    if (index === -1) {
      const maxId = state.chats.reduce((prev, current) =>
        prev.id > current.id ? prev : current
      ).id

      commit('ADD_CHATS', {
        id: maxId + 1,
        img: friend.thumbUrl,
        isBlock: false,
        isFriend: true,
        isOfficial: false,
        key: username,
        lastUpdate: null,
        noty: 0,
        online: friend.online,
        title: username,
        pin: false
      })
      //如果msg裡面沒有他的對話，則新增一個空的對話
      if (!(username in state.msg)) commit('SET_MSG', { title: username, payload: [] })
    }
    //若對話原本就存在，1.設定該頻道上下線狀態 2.設為好友 3.移除黑名單 4.PIN值從null設為false
    else {
      commit('SET_CHAT_ONLINE_STATUS_BY_NAME', {
        name: state.chats[index].title,
        online: friend.online
      })
      commit('SET_CHAT_IS_FRIEND', {
        key: state.chats[index].key,
        payload: true
      })
      commit('SET_CHAT_IS_BLOCK', {
        key: state.chats[index].key,
        payload: false
      })
      if (state.chats[index]?.pin !== true)
        commit('SET_FRIEND_PIN', {
          title: state.chats[index].title,
          pin: false
        })
    }
  },
  deleteFriendEvent({ state, dispatch, commit }, username) {
    const index = state.chats.findIndex((chat) => username === chat.title)

    //如果對話原本就存在，就解除其好友關係且將pin值設為null
    if (index !== -1) {
      //有對話紀錄 而非空的頻道
      if (state.msg[username] && state.msg[username].length > 0) {
        commit('SET_FRIEND_PIN', {
          title: username,
          pin: null
        })
        commit('SET_CHAT_IS_FRIEND', {
          key: username,
          payload: false
        })
      } else {
        if (state.chats[index].id !== 0) {
          commit('REMOVE_CHATS', username)
        }
      }
    }

    dispatch('saveFriendsPinToLocalStroage')
  },
  addBlockEvent({ commit, state }, username) {
    const index = state.chats.findIndex((chat) => username === chat.title)
    if (index !== -1) {
      commit('SET_CHAT_IS_BLOCK', {
        key: state.chats[index].key,
        payload: true
      })
    }
  },
  deleteBlockEvent({ commit, state }, username) {
    const index = state.chats.findIndex((chat) => username === chat.title)
    if (index !== -1) {
      commit('SET_CHAT_IS_BLOCK', {
        key: state.chats[index].key,
        payload: false
      })
    }
  },
  saveFriendsPinToLocalStroage({ state, rootGetters }) {
    // 取得當前登入的角色名稱
    const selfName = rootGetters['role/userName']
    // 從 localStorage 讀取所有角色的釘選資料
    const friendsPin = localStorage.getItem('friendsPin')
    let friendsPinObj = {}

    if (friendsPin) {
      friendsPinObj = JSON.parse(friendsPin)
      // 檢查是否為舊格式（舊格式：最外層直接是聊天對象名稱，內層是空物件或只有 pin 屬性）
      const isOldFormat = Object.values(friendsPinObj).some((value) => {
        return (
          Object.keys(value).length === 0 || (Object.keys(value).length === 1 && 'pin' in value)
        )
      })
      // 如果是舊格式，清空所有資料（避免新舊格式資料混在一起）
      if (isOldFormat) friendsPinObj = {}
    }

    // 整理當前角色的釘選資料
    const selfData = state.chats.reduce((acc, { isFriend, title, pin }) => {
      // 如果 pin 是 undefined，使用 false
      if (isFriend) acc[title] = { pin: pin ?? false }
      return acc
    }, {})

    // 當前角色有釘選資料時才加入，反之則刪除
    if (Object.keys(selfData).length > 0) {
      friendsPinObj[selfName] = selfData
    } else {
      delete friendsPinObj[selfName]
    }

    // 儲存更新後的完整資料回 localStorage
    localStorage.setItem('friendsPin', JSON.stringify(friendsPinObj))
  },
  async setCurrentChannelDetail({ commit }, payload) {
    commit('SET_CURRENT_CHANNELS_NAME', payload.name)
    commit('SET_CURRENT_CHANNELS_DETAIL', payload)
  },

  async setChannelDetail({ commit, dispatch, rootGetters }, payload) {
    const selfName = rootGetters['role/userName']
    await Promise.all(
      payload.channels.map((channel) => dispatch('role/getThumbUrl', channel.name, { root: true }))
    ).then((thumbUrls) => {
      for (let i = 0; i < thumbUrls.length; i++) {
        payload.channels[i].thumbUrl = thumbUrls[i]
      }
    })
    payload.channels.forEach((element) => {
      element.popIsUp = true
    })
    commit('SORT_CHANNEL', { selfName: selfName, channels: payload.channels })
  },
  async updateChannels({ state, commit, dispatch, rootGetters }, payload) {
    const selfName = rootGetters['role/userName']
    for (let channel of payload) {
      const cannelIndex = state.channels.findIndex((item) => item.name === channel.name)
      if (cannelIndex === -1) {
        const thumbUrl = await dispatch('role/getThumbUrl', channel.name, { root: true })
        commit('ADD_CHANNELS', { ...channel, thumbUrl, popIsUp: true })
      } else {
        if (channel.point !== undefined) {
          commit('UPDATE_CHANNEL_POINT', { index: cannelIndex, point: channel.point })
        }
        if (channel.population !== undefined) {
          commit('UPDATE_CHANNEL_POPULATION', {
            index: cannelIndex,
            population: channel.population
          })
        }
      }
    }
    commit('SORT_CHANNEL', { selfName: selfName, channels: state.channels })
  }
}

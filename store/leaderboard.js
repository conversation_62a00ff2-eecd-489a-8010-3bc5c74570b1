import Vue from 'vue'

export const state = () => ({
  topPlayersData: {},
  activeRank: 0,
  isLoading: true,
  webGameList: [],
  rtpCache: new Map(),
  selectPlayerData: {},
  leaderboardCache: new Map()
})

export const getters = {
  topPlayersData(state) {
    return state.topPlayersData
  },
  activeRank(state) {
    return state.activeRank
  },
  isLoading(state) {
    return state.isLoading
  },
  webGameList(state) {
    return state.webGameList
  },
  getRtpData: (state) => (gameId) => {
    return state.rtpCache.get(gameId) || {}
  },
  getAllRtpData(state) {
    return state.rtpCache
  },
  selectPlayerData(state) {
    return state.selectPlayerData
  },
  leaderboardCache(state) {
    return state.leaderboardCache
  }
}

export const mutations = {
  SET_TOP_PLAYERS_DATA(state, value) {
    state.topPlayersData = value
  },
  RESET_TOP_PLAYERS_DATA(state) {
    Vue.set(state, 'topPlayersData', {})
  },
  SET_ACTIVE_RANK(state, value) {
    state.activeRank = value
  },
  SET_IS_LOADING(state, value) {
    state.isLoading = value
  },
  SET_WEB_GAME_LIST(state, value) {
    if (!Array.isArray(value)) return
    // list.length === 1 個人排行榜的請求
    if (value.length === 1 && state.webGameList.length) {
      state.webGameList = [...state.webGameList, ...value]
    } else {
      state.webGameList = value
    }
  },
  MODIFY_WEB_GAME_BY_PLATFORM_ID(state, { gameId, platformId, errCode }) {
    switch (errCode) {
      case 50038: // 黑名單
        state.webGameList.filter((item) => item.platformId !== platformId)
        break
      case 99002: // 維護
        state.webGameList.forEach((item) => {
          if (item.id === gameId) item.maintaining = true
        })
        break
      case 60001: // 下架
        state.webGameList = state.webGameList.filter((item) => item.id !== gameId)
        break
    }
  },
  RESET_WEB_GAME_LIST(state) {
    Vue.set(state, 'webGameList', [])
  },
  SET_BULK_RTP_DATA(state, rtpList) {
    rtpList.forEach((item) => {
      state.rtpCache.set(item.gameId, item)
    })
  },
  SET_SELECT_PLAYER_DATA(state, value) {
    state.selectPlayerData = value
  },
  RESET_SELECT_PLAYER_DATA(state) {
    Vue.set(state, 'selectPlayerData', {
      username: '',
      level: 0,
      levelVip: 0,
      money: 0,
      thumbUrl: '',
      online: false,
      guildName: ''
    })
  },
  SET_LEADERBOARD_CACHE(state, { params, data }) {
    const serializeKey = (obj) => {
      return Object.keys(obj)
        .sort()
        .map((k) => `${k}:${obj[k]}`)
        .join('|')
    }
    if (state.leaderboardCache.has(serializeKey(params))) return
    state.leaderboardCache.set(serializeKey(params), data)
  }
}

export const actions = {
  async getWinRanking({ commit }, params) {
    const { data } = await this.$clientApi.leaderboard.getWinRanking(params)
    data.count = data?.list?.length
    if (!data?.count) {
      commit('RESET_TOP_PLAYERS_DATA')
      commit('SET_ACTIVE_RANK', null)
      return
    }
    params.key = 'rank'
    commit('SET_LEADERBOARD_CACHE', { params, data })
    commit('SET_TOP_PLAYERS_DATA', data)
  },
  async getOddsRanking({ commit }, params) {
    const { data } = await this.$clientApi.leaderboard.getOddsRanking(params)
    data.count = data?.list?.length
    if (!data?.count) {
      commit('RESET_TOP_PLAYERS_DATA')
      commit('SET_ACTIVE_RANK', null)
      return
    }
    params.key = 'odds'
    commit('SET_LEADERBOARD_CACHE', { params, data })
    commit('SET_TOP_PLAYERS_DATA', data)
  },
  async getWebGameList({ state, commit, rootGetters }, gameIds) {
    if (!gameIds || !gameIds?.length) return
    const headerData = {
      username: rootGetters['role/userName'] ? rootGetters['role/userName'] : null,
      alias: null
    }
    // 單一玩家的遊戲排名，取得遊戲資訊
    if (gameIds.length === 1) {
      const isExist = state.webGameList.some((item) => item.id === gameIds[0])
      if (isExist) return
    }

    const { list } = await this.$clientApi.leaderboard.leaderboardGameList(
      headerData,
      gameIds,
      this.$i18n.locale
    )
    if (list && list?.length) commit('SET_WEB_GAME_LIST', list)
  },
  async fetchGameRtp({ state, commit }, gameIds) {
    if (!gameIds || !gameIds.length) return

    // 已存在緩存中的ID, 不進行請求
    const idsToFetch = gameIds.filter((id) => !state.rtpCache.has(id))
    if (idsToFetch.length === 0) return

    const { list } = await this.$clientApi.game.gameRTPList(idsToFetch)
    if (list && list?.length) commit('SET_BULK_RTP_DATA', list)
  }
}

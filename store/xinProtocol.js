import Vue from 'vue'
const STATION = process.env.STATION
let Config = require(`@/plugins/xin-socket/protocolConfig/${STATION}/protocol.config`).default

const SEND_MAX_ID = 60000
const BANK_GAME_ID = 2

export const state = () => ({
  heartbeat: null,
  serverMsg: '',
  heartbeatTimes: 0,
  loginStep: 0,
  services: {
    [Config.LOGIN_SERVICE.ID]: {
      id: Config.LOGIN_SERVICE.ID,
      enable: false,
      connected: false,
      sendId: 0,
      receiveId: 0
    },
    [Config.LOTTERY_SERVICE.ID]: {
      id: Config.LOTTERY_SERVICE.ID,
      enable: false,
      connected: false,
      sendId: 0,
      receiveId: 0
    },
    [Config.GAME_SERVICE.ID]: {
      id: Config.GAME_SERVICE.ID,
      enable: false,
      connected: false,
      sendId: 0,
      receiveId: 0
    },
    [Config.SOCIAL_SERVICE.ID]: {
      id: Config.SOCIAL_SERVICE.ID,
      enable: false,
      connected: false,
      sendId: 0,
      receiveId: 0
    },
    [Config.BANK_SERVICE.ID]: {
      id: Config.BANK_SERVICE.ID,
      enable: false,
      connected: false,
      sendId: 0,
      receiveId: 0
    },
    [Config.GUILD_SERVICE.ID]: {
      id: Config.GUILD_SERVICE.ID,
      enable: false,
      connected: false,
      sendId: 0,
      receiveId: 0
    },
    [Config.WEB_GAME_SERVICE.ID]: {
      id: Config.WEB_GAME_SERVICE.ID,
      enable: false,
      connected: false,
      sendId: 0,
      receiveId: 0
    },
    [Config.REDEEM_SERVICE.ID]: {
      id: Config.REDEEM_SERVICE.ID,
      enable: false,
      connected: false,
      sendId: 0,
      receiveId: 0
    }
  },
  healthyData: '',
  latency: undefined,
  serverTimestamp: 0,
  socketRetryTimes: 0
})

export const getters = {
  loginServiceSendId(state) {
    return state.services[Config.LOGIN_SERVICE.ID].sendId
  },
  loginServiceReceiveId(state) {
    return state.services[Config.LOGIN_SERVICE.ID].receiveId
  },
  gameServiceSendId(state) {
    return state.services[Config.GAME_SERVICE.ID].sendId
  },
  gameServiceReceiveId(state) {
    return state.services[Config.GAME_SERVICE.ID].receiveId
  },
  socialServiceSendId(state) {
    return state.services[Config.SOCIAL_SERVICE.ID].sendId
  },
  socialServiceReceiveId(state) {
    return state.services[Config.SOCIAL_SERVICE.ID].receiveId
  },
  bankServiceSendId(state) {
    return state.services[Config.BANK_SERVICE.ID].sendId
  },
  bankServiceReceiveId(state) {
    return state.services[Config.BANK_SERVICE.ID].receiveId
  },
  guildServiceSendId(state) {
    return state.services[Config.GUILD_SERVICE.ID].sendId
  },
  guildServiceReceiveId(state) {
    return state.services[Config.GUILD_SERVICE.ID].receiveId
  },
  serverMsg(state) {
    return state.serverMsg
  },
  heartbeatTimes(state) {
    return state.heartbeatTimes
  },
  services(state) {
    return state.services
  },
  loginStep(state) {
    return state.loginStep
  },
  healthyData(state) {
    return state.healthyData
  },
  latency(state) {
    return state.latency
  },
  serverTimestamp(state) {
    return state.serverTimestamp
  },
  socketRetryTimes(state) {
    return state.socketRetryTimes
  }
}

export const mutations = {
  RESET_BANK_SERVICE_SEND_ID(state) {
    state.services[Config.BANK_SERVICE.ID].sendId = 0
  },
  SET_HEARTBEAT(state, data) {
    clearInterval(state.heartbeat)
    state.heartbeat = data
  },
  CLEAR_HEARTBEAT(state) {
    clearInterval(state.heartbeat)
    Vue.set(state, 'heartbeat', null)
  },
  SET_SERVER_MSG(state, data) {
    state.serverMsg = data
  },
  SET_HEARTBEAT_TIMES(state, data) {
    state.heartbeatTimes = data
  },
  SET_SERVICE_IDS(
    state,
    {
      serviceId,
      enable = undefined,
      connected = undefined,
      sendId = undefined,
      receiveId = undefined
    }
  ) {
    if (enable != undefined) state.services[serviceId].enable = enable
    if (connected != undefined) state.services[serviceId].connected = connected
    if (sendId != undefined) state.services[serviceId].sendId = sendId
    if (receiveId != undefined) state.services[serviceId].receiveId = receiveId
  },
  SET_LOGIN_STEP(state, data) {
    state.loginStep = data
  },

  SET_HEALTHY_DATA(state, data) {
    state.healthyData = data
  },
  SET_LATENCY(state, data) {
    state.latency = data
  },
  SET_SERVER_TIMESTAMP(state, data) {
    state.serverTimestamp = data
  },
  SET_SOCKET_RETRY_TIMES(state, data) {
    state.socketRetryTimes = data
  },

  RESET(state) {
    Vue.set(state, 'serverMsg', '')
    Vue.set(state, 'heartbeatTimes', 0)
    clearInterval(state.heartbeat)
    Vue.set(state, 'heartbeat', null)
    Vue.set(state, 'loginStep', 0)
    Vue.set(state, 'services', {
      [Config.LOGIN_SERVICE.ID]: {
        enable: false,
        connected: false,
        sendId: 0,
        receiveId: 0
      },
      [Config.LOTTERY_SERVICE.ID]: {
        enable: false,
        connected: false,
        sendId: 0,
        receiveId: 0
      },
      [Config.GAME_SERVICE.ID]: {
        enable: false,
        connected: false,
        sendId: 0,
        receiveId: 0
      },
      [Config.SOCIAL_SERVICE.ID]: {
        enable: false,
        connected: false,
        sendId: 0,
        receiveId: 0
      },
      [Config.GUILD_SERVICE.ID]: {
        enable: false,
        connected: false,
        sendId: 0,
        receiveId: 0
      },
      [Config.BANK_SERVICE.ID]: {
        enable: false,
        connected: false,
        sendId: 0,
        receiveId: 0
      },
      [Config.WEB_GAME_SERVICE.ID]: {
        enable: false,
        connected: false,
        sendId: 0,
        receiveId: 0
      }
    })
    Vue.set(state, 'healthyData', '')
    Vue.set(state, 'latency', 0)
    Vue.set(state, 'serverTimestamp', 0)
    Vue.set(state, 'socketRetryTimes', 0)
  }
}

export const actions = {
  resetConfig({ commit }, station) {
    Config = require(`@/plugins/xin-socket/protocolConfig/${station}/protocol.config`).default
    commit('RESET')
  },
  async receiveNextId({ commit }, { serviceId, id }) {
    commit('SET_SERVICE_IDS', { serviceId: serviceId, receiveId: id })
  },
  async sendNextId({ commit, getters }, serviceId) {
    let sendId = getters['services'][serviceId].sendId
    if (sendId >= SEND_MAX_ID) {
      sendId = 0
    }
    sendId++
    commit('SET_SERVICE_IDS', { serviceId: serviceId, sendId })
  },
  async initReceived({ commit, dispatch, getters, rootGetters }) {
    // 接收伺服器主動推播的資訊
    this.$wsClient.receivedListeners.add(async (data) => {
      if (data.commandId === 11 || data.commandId === 1) {
        if (data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.GET)) {
          // 初始化MAIL 是一筆一筆送增加該判斷避免洗版
          if (!rootGetters['mail/getFirstRecive']) {
            commit('SET_SERVER_MSG', 'new_mail_revice_noty')
          }
          // 避免收到兩封一樣的信
          if (!rootGetters['mail/list'].find((item) => item.mailId === data.mailId)) {
            dispatch('mail/init', data, { root: true })
          }
        } else if (
          this.$xinConfig.SOCIAL_SERVICE.TYPE.SYSTEM_MESSAGE &&
          data.isFeature(this.$xinConfig.SOCIAL_SERVICE.TYPE.SYSTEM_MESSAGE.ID)
        ) {
          this.$notify.warning(data.message)
        } else if (data.serviceId === this.$xinConfig.GAME_SERVICE.ID && data.type === 253) {
          const currentAnswer = ['快問快答', '你畫我猜']
          const checkStringWithKeywords = (str, keywords) => {
            if (!keywords || keywords.length === 0) return true
            return keywords.every((keyword) => str.includes(keyword))
          }
          if (
            checkStringWithKeywords(data.message, currentAnswer) &&
            this.$UIConfig.miniGame.enable
          ) {
            commit('miniGame/SET_LOCK_ANSWER_BTN', true, { root: true })
            this.$notify.info(this.$i18n.t('miniAnswerCurrentNoty'))
          }
        } else if (data.isFeature(this.$xinConfig.FEATURE.MAIL.ID)) {
          dispatch('social/init', data, { root: true })
        }
      } else if (data.commandId === 4) {
        if (data.silver) commit('role/SET_SILVER_COIN', data.silver, { root: true })
        //設定pk幣、紅寶石
        if (data.coins) dispatch('role/setCoins', data.coins, { root: true })
        // 初始化角色背包道具（狀態類、其他類）
        if (
          this.$xinConfig.FEATURE.USER_DETAIL.STATUS_INVENTORY &&
          data.isFeature(this.$xinConfig.FEATURE.USER_DETAIL.STATUS_INVENTORY)
        )
          commit('role/SET_STATUS_INVENTORY', data.items, { root: true })
        // 獲取角色背包道具（遊戲類）
        if (
          this.$xinConfig.FEATURE.USER_DETAIL.GAME_INVENTORY &&
          data.isFeature(this.$xinConfig.FEATURE.USER_DETAIL.GAME_INVENTORY)
        )
          commit('role/ADD_GAME_INVENTORY', data.items, { root: true })
        if (data.isFeature(this.$xinConfig.FEATURE.USER_DETAIL.LEVEL)) {
          dispatch('role/updateLevel', data.level, { root: true })
        } else if (data.isFeature(this.$xinConfig.FEATURE.USER_DETAIL.MONEY)) {
          commit('role/SET_BALANCE', data.money, { root: true })
        } else if (data.isFeature(this.$xinConfig.FEATURE.MINI_GAME.TYPE.GAME_END)) {
          dispatch('miniGame/closeGame', { hasNextGame: data.endType === 2 }, { root: true })
        }
        //登入成功後，送出至平台
        if (data.isFeature(this.$xinConfig.FEATURE.LOGIN.BASIC_INFO)) {
          let expiredDate = this.$localStorage.get('accept_cookie_policy').expired
          commit('role/SET_USERNAME', data.username, { root: true })
          if (expiredDate && this.$moment(expiredDate).isAfter(this.$moment())) {
            let isPwa = false
            if (process.client) {
              const params = new URLSearchParams(window.location.search)
              const source = params.get('source')
              isPwa =
                source === 'pwa' ||
                window.matchMedia('(display-mode: standalone)').matches ||
                window.matchMedia('(display-mode: fullscreen)').matches ||
                window.matchMedia('(display-mode: minimal-ui)').matches ||
                window.navigator.standalone === true
            }
            if (!this.$UIConfig.lock.checkBindPhoneNumber) {
              this.$clientApi.login.sendAnalytics({
                username: data.username,
                is_new_member: rootGetters['role/loginRecord'].length === 1,
                level: data.level,
                vip_level: data.levelVip,
                phone_number: data.phoneNumber,
                type: rootGetters['role/loginType'],
                use_pwa: isPwa
              })
            } else {
              let analyticsData = {
                username: data.username,
                is_new_member: rootGetters['role/loginRecord'].length === 1,
                level: data.level,
                vip_level: data.levelVip,
                type: rootGetters['role/loginType'],
                use_pwa: isPwa
              }
              //如果有綁定手機，送出手機號碼
              if (data.phoneNumber && data.phoneNumber.length > 0) {
                analyticsData.phone_number = data.phoneNumber
              }
              this.$clientApi.login.sendAnalytics(analyticsData)
            }
          }
        }
      } else if (data.commandId === 9) {
        //銅幣
        commit('role/SET_COPPER_COIN', data.copper, { root: true })
      } else if (data.commandId === 20) {
        if (data.isFeature(this.$xinConfig.FEATURE.FRIEND.TYPE.OFFLINE)) {
          data.online = false
          if (rootGetters['social/friendList'].find((item) => item.username === data.username))
            dispatch('social/setSingleFriendStatus', data, { root: true })
          else dispatch('social/setSingleBlockStatus', data, { root: true })
        }
      } else if (data.commandId === 21) {
        if (data.isFeature(this.$xinConfig.FEATURE.FRIEND.TYPE.ONLINE)) {
          data.online = true
          commit('SET_SERVER_MSG', 'friend_online')
          if (rootGetters['social/friendList'].find((item) => item.username === data.username))
            dispatch('social/setSingleFriendStatus', data, { root: true })
          else dispatch('social/setSingleBlockStatus', data, { root: true })
        }
      } else if (data.commandId === 29) {
        if (data.isFeature(this.$xinConfig.FEATURE.CUSTOMER_SERVICE.ID)) {
          dispatch('chat/addCustomerServiceMsg', data, { root: true })
        }
      } else if (data.commandId === 31) {
        if (data.isFeature(this.$xinConfig.FEATURE.MAIL.ID)) {
          dispatch('social/init', data, { root: true })
        }
      } else if (data.protocolId === 33) {
        if (data.isFeature(this.$xinConfig.FEATURE.LOGOUT.ID)) {
          let connectedServices = await dispatch('getConnectedServices')
          if (getters['loginStep'] === 1 || connectedServices.length < 1) {
            commit('SET_SERVER_MSG', 'server_kick_noty2')
          } else if (data.serviceId !== this.$xinConfig.LOGIN_SERVICE.ID) {
            commit('SET_SERVICE_IDS', { serviceId: data.serviceId, connected: false, receiveId: 0 })
          }
        }
      } else if (data.commandId === 41) {
        commit('role/ADD_STATUS_INVENTORY', data, { root: true })
      } else if (data.commandId === 98) {
        if (data.isFeature(this.$xinConfig.FEATURE.USER_DETAIL.VIP)) {
          commit('role/SET_VIPLEVEL', data.vip, { root: true })
        }
      } else if (data.protocolId === 112) {
        dispatch('role/saveToken', data, { root: true })
        commit('chat/SET_SEMANTIC_TOKEN', data, { root: true })
        commit(
          'SET_HEARTBEAT',
          setInterval(() => {
            dispatch('sendAlive')
            dispatch('updateLatency')
          }, 6000)
        )
      } else if (data.commandId === 135) {
        //多個登入出現，伺服器主動踢出前面登入的裝置
        if (data.action === 1) {
          commit('SET_SERVER_MSG', data.message)
        }
      } else if (data.type === 17) {
        //目前不接收系統訊息 1:系統 2:頻道 3:密語 4:全服
        if (data.messageType === 1) {
          const checkStringWithKeywords = (str, keywords) => {
            if (!keywords || keywords.length === 0) return true
            return keywords.every((keyword) => str.includes(keyword))
          }
          if (!checkStringWithKeywords(data.content.allMessage, this.$UIConfig.miniGame.keyWords)) {
            if (this.$UIConfig.miniGame.enable)
              dispatch('chat/addGlobalChat', { payload: data }, { root: true })
            // dispatch('chat/addChannelChat', { payload: data }, { root: true }) 疑似刪掉了
          }
        }
        if (data.messageType === 2) return
        else if (data.messageType === 3)
          dispatch('chat/addWhisperChat', { payload: data, avatar: true }, { root: true })
        else if (data.messageType === 4)
          dispatch('chat/addGlobalChat', { payload: data, avatar: true }, { root: true })
      } else if (data.type === 18) {
        if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.INFO)) {
          dispatch('chat/setCurrentChannelDetail', data, { root: true })
        } else if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.LIST)) {
          dispatch('chat/setChannelDetail', data, { root: true })
        } else if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.REMOVE)) {
          console.log('REMOVE:', data)
        } else if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.UPDATE)) {
          dispatch('chat/updateChannels', data.channels, { root: true })
        } else if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.USER_EXIT)) {
          commit('chat/REMOVE_USER_FROM_CHANNEL', data.users, { root: true })
        } else if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.USER_JOIN)) {
          commit('chat/ADD_USER_TO_CHANNEL', data.users, { root: true })
        } else if (data.isFeature(this.$xinConfig.FEATURE.CHANNEL.TYPE.USER_POINT)) {
          dispatch('chat/updateChannels', data.channels, { root: true })
        }
      } else if (data.type === 25) {
        //公告訊息與星城遊戲訊息混在一起 目前不接收
        const addAnnouncementStatus = false
        if (addAnnouncementStatus) dispatch('chat/addAnnouncement', data, { root: true })
      } else if (data.type === 47 && data.isFeature(this.$xinConfig.FEATURE.GRAND_PRIZE_NOTY.ID)) {
        dispatch('grandPrize/addGrandPrizeData', data, { root: true })
      } else if (data.type === 48) {
        if (data.isFeature(this.$xinConfig.FEATURE.MAIL.TYPE.UPDATE_USER_ITEM)) {
          //更新玩家道具
          dispatch(
            'role/setCoins',
            [
              {
                id: data.id,
                balance: data.count
              }
            ],
            { root: true }
          )
        }
      } else if (data.type === 72) {
        if (data.hasGuild && data.members && data.members.length > 0) {
          const online = data.members.filter((item) => item.online > 0).length
          const guildMemberList = data.members.map((item) => {
            const defaultAvator = process.env.IMAGE_URL + '/photo_stickers/default.png'
            return { ...item, avator: defaultAvator, name: item.username }
          })
          commit('social/SET_GUILD_MEMBER_LIST', data.members, { root: true })
          commit('guild/SET_GUILD_NAME', data.guildName, { root: true })
          commit('guild/SET_GUILD_FUND', data.guildFund, { root: true })
          commit('guild/SET_JOIN_DATE', data.joinDate, { root: true })
          commit('guild/SET_HAS_GUILD', data.hasGuild, { root: true })
          commit('guild/SET_LEAVE_GUILD', false, { root: true })
          commit('guild/SET_GUILD_ID', data.guildId, { root: true })
          commit('guild/SET_GUILD_MEMBERS', data.members.length, { root: true })
          commit('guild/SET_GUILD_ONLINE_MEMBERS', online, { root: true })
          commit('guild/SET_GUILD_MEMBER_INFO_LIST', guildMemberList, { root: true })
          commit('guild/SET_GUILD_MASTER', data.owner, { root: true })
          if (this.$UIConfig.guildPinMsg.enable && data.hasGuild) {
            if (data.direction) {
              await dispatch('guild/fetchGuildCaption', data.direction, { root: true })
            } else {
              //設置預設的news內容
              await dispatch('guild/fetchGuildCaption', '', { root: true })
            }
          }
        }
        if (!data.hasGuild) {
          const nowPath = window.location.pathname
          const isInfoPage = nowPath.includes('/guild/info')
          if (isInfoPage) this.$router.push(this.localePath('/guild/list'))
          if (rootGetters['guild/hasGuild'] && !rootGetters['guild/leaveGuild'])
            commit('SET_SERVER_MSG', 'guild_reward_noty4')
          dispatch('chat/resetGuildChat', data, { root: true })
          commit('guild/RESET_WITHOUT_RANKING', '', { root: true })
        } else {
          this.$wsClient.send(this.$wsPacketFactory.getGuildDetail(data.guildId))
        }
      } else if (data.type === 100) {
        let isEnable = data.gameIds.find((element) => element == BANK_GAME_ID) != undefined
        commit(`${STATION}/payment/SET_BANK_ENABLE`, isEnable, { root: true })
      } else if (data.type == 250) {
        if (data.commandId === 150) {
          if (data.isFeature(this.$xinConfig.FEATURE.LOGIN.ALLOW_LIST)) {
            commit('role/SET_DEVICE_LIST', data.deviceList, { root: true })
          }
        } else if (data.commandId === 151) {
          if (data.isFeature(this.$xinConfig.FEATURE.LOGIN.LOGIN_RECORD)) {
            commit('role/SET_LOGIN_RECORD', data.records, { root: true })
          }
        }
      } else if (data.type === 187) {
        if (data.isFeature(this.$xinConfig.FEATURE.YOEGAME.TYPE.AD_GIFT_PACK)) {
          try {
            // 讀取 彈窗禮包JSON.json
            const yoeShopUrl = '{0}/yoeShop/ab08031e/json/ad-gift-pack.json?v={1}'.format(
              process.env.IMAGE_URL,
              Math.floor(Math.random() * (100 - 1 + 1)) + 1
            )
            const response = await this.$axios.get(yoeShopUrl)
            // 從 json 中找出彈窗禮包
            const adGiftPack = response.data.find((config) => config.adName === data.adName)
            if (adGiftPack) {
              this.$localStorage.set('adGiftPack', [adGiftPack])
              // 更新 store
              commit('yoeShop/SET_AD_GIFT_PACK', [adGiftPack], { root: true })
              commit('yoeShop/SET_REMAINING_BALANCE', data.remainingBalance, { root: true })
            }
          } catch (e) {
            const yoeShopData = this.$localStorage.get('yoeShop')
            const adGiftPack = yoeShopData.find((config) => config.adName === data.adName)
            if (adGiftPack) {
              commit('yoeShop/SET_AD_GIFT_PACK', [adGiftPack], { root: true })
              commit('yoeShop/SET_REMAINING_BALANCE', data.remainingBalance, { root: true })
            }
          }
        }
      } else if (data.protocolId === 255) {
        // 因為是每三秒收到一次包，所以要-0.5
        commit('SET_HEARTBEAT_TIMES', getters['heartbeatTimes'] - 0.5)
      } else if (data.serviceId === this.$xinConfig.GUILD_SERVICE.ID) {
        switch (data.type) {
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_CHANGE.ID:
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_CHANGE_TOTEM.ID:
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_ADD_MEMBERSHIP.ID:
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_ACCEPT_MEMBERSHIP.ID:
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_REJECT_MEMBERSHIP.ID:
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_REMOVE_MEMBERSHIP.ID:
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_ADD_VICE_PRESIDENT.ID:
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_REMOVE_VICE_PRESIDENT.ID:
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_USER_LEAVE.ID:
            if (data.success) this.$notify.success(data.response)
            else {
              switch (data.type) {
                case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_ADD_MEMBERSHIP.ID:
                case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_ADD_VICE_PRESIDENT.ID:
                case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_ACCEPT_MEMBERSHIP.ID:
                  if (data.response) {
                    const isError = ['lang_81']
                    const checkError = isError.some((str) => data.response.includes(str))
                    if (checkError) this.$notify.error(data.response)
                    else this.$notify.warning(data.response)
                  }
                  break
                default:
                  if (data.response) this.$notify.error(data.response)
                  break
              }
            }
            break
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_CHANGE_CAPTION.ID:
            {
              const guildNewsIsDefault = rootGetters['guild/guildNewsIsDefault']
              if (data.success && !guildNewsIsDefault) this.$notify.success(data.response)
              commit('guild/SET_GUILD_NEWS_IS_DEFAULT', false, { root: true })
            }
            break
          case this.$xinConfig.GUILD_SERVICE.TYPE.ADD_APPLICATION_NOTIFY.ID: //73
            commit('guild/ADD_GUILD_ACCEPT_LIST', data.response, { root: true })
            break
          case this.$xinConfig.GUILD_SERVICE.TYPE.REMOVE_APPLICATION_NOTIFY.ID: //74
            commit('guild/REMOVE_GUILD_ACCEPT_LIST', data.response, { root: true })
            break
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_DETAIL.ID: //11
            {
              const selfName = rootGetters['role/userName']
              const members = data.members.find((item) => item.name === selfName)
              if (members !== undefined) {
                const rank = members === undefined ? 0 : members.rank
                const online = data.members.filter((item) => item.onlines > 0).length
                const guildMemberList = data.members.map((item) => {
                  const defaultAvator = process.env.IMAGE_URL + '/photo_stickers/default.png'
                  return { ...item, avator: defaultAvator }
                })
                const captionNotify = await dispatch(
                  'literalKeywords/replaceKeywords',
                  data.guildcaption,
                  {
                    root: true
                  }
                )
                commit('guild/SET_GUILD_RANK', rank, { root: true })
                commit('guild/SET_GUILD_ID', data.guildId, { root: true })
                commit('guild/SET_GUILD_NAME', data.guildname, { root: true })
                commit('guild/SET_GUILD_MEMBERS', data.members.length, { root: true })
                commit('guild/SET_GUILD_ONLINE_MEMBERS', online, { root: true })
                commit('guild/SET_GUILD_MEMBER_INFO_LIST', guildMemberList, { root: true })
                commit('guild/SET_GUILD_MASTER', data.presidentname, { root: true })
                if (this.$UIConfig.guildPinMsg.enable) {
                  await dispatch('guild/fetchGuildCaption', captionNotify, { root: true })
                } else {
                  commit('guild/SET_GUILD_CAPTION', captionNotify, { root: true })
                }
              }
            }

            break
          case this.$xinConfig.GUILD_SERVICE.TYPE.ADD_MEMBER_NOTIFY.ID: //75
          case this.$xinConfig.GUILD_SERVICE.TYPE.REMOVE_MEMBER_NOTIFY.ID: //76
          case this.$xinConfig.GUILD_SERVICE.TYPE.ADD_VICE_PRESIDENT_NOTIFY.ID: //77
          case this.$xinConfig.GUILD_SERVICE.TYPE.REMOVE_VICE_PRESIDENT_NOTIFY.ID: //78
          case this.$xinConfig.GUILD_SERVICE.TYPE.MEMBER_ONLINE_NOTIFY.ID: //79
          case this.$xinConfig.GUILD_SERVICE.TYPE.MEMBER_OFFLINE_NOTIFY.ID: //80
          case this.$xinConfig.GUILD_SERVICE.TYPE.CHANGE_NAME_NOTIFY.ID: //81
          case this.$xinConfig.GUILD_SERVICE.TYPE.CHANGE_TOTEM_NOTIFY.ID: //82
          case this.$xinConfig.GUILD_SERVICE.TYPE.CHANGE_CAPTION_NOTIFY.ID: //83
          case this.$xinConfig.GUILD_SERVICE.TYPE.MEMBER_LEAVE_NOTIFY.ID: //84
          case this.$xinConfig.GUILD_SERVICE.TYPE.POINT_REFRESH_NOTIFY.ID: //86
            dispatch('guild/fetchUpdateGuildInfoList', data, { root: true })
            break
          case this.$xinConfig.GUILD_SERVICE.TYPE.APPLICANTS_LIST_NOTIFY.ID: //85
            commit('guild/SET_GUILD_ACCEPT_LIST', data.username, { root: true })
            break
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_MESSAGE_ERROR.ID: //102
            this.$notify.warning(data.response)
            break
          case this.$xinConfig.GUILD_SERVICE.TYPE.GUILD_MEMBER_MESSAGE_GET.ID:
            dispatch('chat/addGuildChat', { payload: data, avatar: true }, { root: true })
            break
          case this.$xinConfig.GUILD_SERVICE.TYPE.PERSONAL_BADGE.ID: //9
            {
              const personalBadgeData = {
                ...rootGetters['guild/personalBadge'],
                playerHave: data.badges.length
              }
              commit('guild/SET_PERSONAL_BADGE', personalBadgeData, { root: true })
            }
            break
        }
      } else if (data.serviceId === this.$xinConfig.GAME_SERVICE.ID && data.type === 70) {
        if (this.$UIConfig.miniGame.enable) {
          switch (data.command) {
            case this.$xinConfig.GAME_SERVICE.TYPE.MINI_GAME.COMMAND.SET_HOST:
              break
            case this.$xinConfig.GAME_SERVICE.TYPE.MINI_GAME.COMMAND.GAME_START:
              {
                let gameData = {
                  gameType: data.gameType,
                  countDownTime: data.countDownTime,
                  moderator: data.moderator
                }
                if (data.gameType === 1) gameData.heading = data.heading
                commit('miniGame/SET_MINI_GAME_DATA', gameData, { root: true })
                // commit('miniGame/SET_ISPLAY', true, { root: true })

                dispatch('miniGame/showGameStart', {}, { root: true })
                const endTime = this.$moment().add(data.countDownTime, 'seconds')
                dispatch('miniGame/startTimer', endTime, { root: true })
              }
              break
            case this.$xinConfig.GAME_SERVICE.TYPE.MINI_GAME.COMMAND.ANSWER:
              if (data.answerType === 1)
                commit('miniGame/SET_LOCK_ANSWER_BTN', true, { root: true })
              break
            case this.$xinConfig.GAME_SERVICE.TYPE.MINI_GAME.COMMAND.GAME_END:
              // command 上面有4  所以移至上面此作註記
              break
            case this.$xinConfig.GAME_SERVICE.TYPE.MINI_GAME.COMMAND.UPDATE_CANVAS:
              commit('miniGame/ADD_CANVAS_DATA', data.answers, { root: true })
              break
            case this.$xinConfig.GAME_SERVICE.TYPE.MINI_GAME.COMMAND.CLEAR_CANVAS:
              commit('miniGame/SET_CANVAS_DATA', [], { root: true })
              break
            case this.$xinConfig.GAME_SERVICE.TYPE.MINI_GAME.COMMAND.FORCE_CLOSE:
              dispatch('miniGame/closeGame', { hasNextGame: false }, { root: true })
              break
          }
        }
      }
      // if (data.protocolId !== 255 && data.protocolId !== 38) {
      //   // 不顯示 健康檢查log
      //   console.log('debug data', data)
      // }
    })

    this.$wsClient.closedListeners.add(async (event) => {
      console.log('WEB SOCKET CLOSE!!!', event)
      if (event.code == 1006) {
        this.$wsClient.endPoint.next()
      }
      let retryTimes = getters['socketRetryTimes']
      do {
        let endPoint = this.$wsClient.endPoint.current
        console.log('重新連線到:' + endPoint)

        if (retryTimes > 5) {
          commit('SET_SERVER_MSG', 'server_kick_noty2')
          return
        } else {
          retryTimes++
          commit('SET_SOCKET_RETRY_TIMES', retryTimes)
        }
        try {
          await this.$wsClient.disconnect(1000)
          await this.$wsClient.connect(endPoint)
        } catch (err) {
          console.log('CATCH ERR', err)
          this.$wsClient.endPoint.next()
        }
      } while (!this.$wsClient.isConnected)

      commit('SET_SOCKET_RETRY_TIMES', retryTimes)
      let serviceIds = await dispatch('getEnabledServices')
      if (serviceIds) {
        let connectionInfo = rootGetters['role/connInfo']
        // console.log('CONNECTION_INFO: ', connectionInfo)
        this.$wsClient.send(
          this.$wsPacketFactory.reconnectionPacket(
            serviceIds[0],
            connectionInfo.clientId,
            connectionInfo.token
          )
        )

        await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
          if (data.protocolId == this.$xinConfig.PROTOCOL_ID.RECONNECT) {
            return true
          }
        })

        // 重連當前服務
        let services = getters['services']
        for (let i = 0; i < serviceIds.length; i++) {
          try {
            let serviceId = serviceIds[i]
            let receiveId = services[serviceId].receiveId
            this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(serviceId, receiveId + 1))
            await this.$xinUtility
              .waitEvent(
                this.$wsClient.receivedListeners,
                (data) => data.protocolId == this.$xinConfig.PROTOCOL_ID.REJOINED_SERVICE
              )
              .then(async (data) => {
                commit('SET_SERVICE_IDS', {
                  serviceId: data.serviceId,
                  connected: true,
                  sendId: data.sendId - 1
                })
              })
              .catch(() => {
                throw 'err in reconnection'
              })
          } catch (err) {
            commit('SET_SERVER_MSG', 'server_kick_noty2')
            return
          }
        }

        commit('SET_HEARTBEAT_TIMES', 0)
      }
    })

    setInterval(async () => {
      let services = getters['services']
      for (let id in services) {
        if (services[id].enable && !services[id].connected) {
          this.$wsClient.send(this.$wsPacketFactory.getServiceJoin(id, services[id].receiveId))
          await this.$xinUtility
            .waitEvent(
              this.$wsClient.receivedListeners,
              (data) => data.protocolId == this.$xinConfig.PROTOCOL_ID.REJOINED_SERVICE
            )
            .then(async (data) => {
              commit('SET_SERVICE_IDS', {
                serviceId: data.serviceId,
                connected: true,
                sendId: data.sendId - 1
              })
            })
            .catch(() => {})
        }
      }
    }, 5000)
  },
  async updateLatency(ctx) {
    // 更新 latency (ms)
    let date = new Date()
    this.$wsClient.send(this.$wsPacketFactory.serverEcho(date.getTime().toString()))
    this.$xinUtility
      .waitEvent(
        this.$wsClient.receivedListeners,
        (data) => data.protocolId == this.$xinConfig.PROTOCOL_ID.ECHO
      )
      .then((data) => {
        ctx.commit('SET_LATENCY', data.latency)
      })
      .catch(() => {
        ctx.commit('SET_LATENCY', undefined)
      })
  },
  async sendAlive(ctx) {
    // console.log('heartbeatTimes', ctx.getters['heartbeatTimes'])
    ctx.commit('SET_HEARTBEAT_TIMES', ctx.getters['heartbeatTimes'] + 1)
    this.$wsClient.send(this.$wsPacketFactory.getAlive())

    // 服務器 Echo
    let serviceIds = await ctx.dispatch('getConnectedServices')
    for (let i = 0; i < serviceIds.length; i++) {
      this.$wsClient.send(this.$wsPacketFactory.healthyCheck(serviceIds[i]))
    }
  },
  getEnabledServices({ getters }) {
    let services = getters['services']
    let result = []
    for (let serviceId in services) {
      if (services[serviceId].enable) {
        result.push(Number(serviceId))
      }
    }
    return result
  },
  getConnectedServices({ getters }) {
    let services = getters['services']
    let result = []
    for (let serviceId in services) {
      if (services[serviceId].connected) {
        result.push(Number(serviceId))
      }
    }
    return result
  },
  async getServerLocalTimeDiff({ commit }) {
    try {
      this.$wsClient.send(this.$wsPacketFactory.healthyCheck(this.$xinConfig.SOCIAL_SERVICE.ID))
      const res = await this.$xinUtility.waitEvent(this.$wsClient.receivedListeners, (data) => {
        return (
          data.protocolId === 30 &&
          data.type === 99 &&
          data.serviceId === this.$xinConfig.SOCIAL_SERVICE.ID
        )
      })
      const localTime = new Date().getTime()
      const serverTime = res.serverTime.getTime()
      const timeDiff = localTime - serverTime
      commit('SET_SERVER_TIMESTAMP', timeDiff)
    } catch (error) {
      console.error('Failed to get server time:', error)
      commit('SET_SERVER_TIMESTAMP', 0)
    }
  }
}
